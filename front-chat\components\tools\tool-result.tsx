"use client";

import React from "react";
import { cn } from "@/lib/utils";
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card";
import { AddressResponse } from "@/types/tools";
import {
  Layers3,
  Compass,
  HelpCircle,
  CheckCircle,
  MapPin,
  Search as SearchIcon,
  Zap,
  Bot,
  Palette
} from "lucide-react";
import { componentStyles } from "@/lib/design-tokens";
import { CompactResultTrigger } from "./common/compact-result-trigger";
import { getToolDisplayInfo } from "../annotations";

type DisplayResult = string | object | AddressResponse;

interface ToolResultProps {
  toolName: string;
  state: "call" | "result" | "partial-call";
  content: string;
  className?: string;
  initialOpen?: boolean;
  open?: boolean;
}



export function ToolResult({
  toolName,
  state,
  content,
  className,
  initialOpen = false,
  open,
}: ToolResultProps) {
  const isSuccess = state === "result";

  // Map internal tool names to user-friendly labels
  const TOOL_DISPLAY_NAMES: Record<string, string> = {
    createLayerFilter: "레이어 필터 적용 완료",
    getLayer: "레이어 정보 조회 완료",
    getLayerAttributes: "레이어 속성 조회 완료",
    getLayerList: "레이어 목록 검색 완료",
    performDensityAnalysis: "밀도 분석 완료",
    searchAddress: "주소 검색 완료",
    searchCoord: "좌표 검색 완료",
    searchOrigin: "출발지 검색 완료",
    searchDestination: "목적지 검색 완료",
    searchDirections: "경로 탐색 완료",
    changeBasemap: "배경지도 변경 완료",
    createVectorStyle: "벡터 스타일 생성 완료",
    getLocation: "현재 위치 확인 완료",
    highlightGeometry: "도형 강조 완료",
    setCenter: "지도 중심 이동 완료",
    chooseOption: "선택 완료",
    getUserInput: "정보 입력 완료",
    confirmWithCheckbox: "확인 완료",
    moveMapByDirection: "지도 이동 완료",
    setMapZoom: "지도 확대/축소 완료",
    generateCategoricalStyle: "유형별 스타일 생성 완료",
  };

  const friendlyName = TOOL_DISPLAY_NAMES[toolName] || `${toolName} 실행 결과`;
  const HIL_TOOLS = ["chooseOption", "getUserInput", "confirmWithCheckbox", "getLocation"];
  const isHilTool = HIL_TOOLS.includes(toolName);

  // 아이콘 매핑 (tool-call.tsx의 TOOL_CONFIG와 유사하게 구성)
  const TOOL_ICONS: Record<string, React.ReactNode> = {
    createLayerFilter: <Layers3 className="h-4 w-4" />,
    updateLayerStyle: <Palette className="h-4 w-4" />,
    getLayer: <Layers3 className="h-4 w-4" />,
    getLayerAttributes: <SearchIcon className="h-4 w-4" />,
    getLayerList: <SearchIcon className="h-4 w-4" />,
    performDensityAnalysis: <Zap className="h-4 w-4" />,
    searchAddress: <SearchIcon className="h-4 w-4" />,
    searchCoord: <MapPin className="h-4 w-4 text-purple-600" />,
    searchOrigin: <MapPin className="h-4 w-4 text-green-600" />,
    searchDestination: <MapPin className="h-4 w-4 text-blue-600" />,
    searchDirections: <Compass className="h-4 w-4" />,
    changeBasemap: <MapPin className="h-4 w-4" />,
    createVectorStyle: <Zap className="h-4 w-4" />,
    getLocation: <MapPin className="h-4 w-4" />,
    highlightGeometry: <Zap className="h-4 w-4" />,
    setCenter: <MapPin className="h-4 w-4" />,
    chooseOption: <Bot className="h-4 w-4" />,
    getUserInput: <HelpCircle className="h-4 w-4" />,
    confirmWithCheckbox: <CheckCircle className="h-4 w-4" />,
    moveMapByDirection: <Compass className="h-4 w-4" />,
    setMapZoom: <Compass className="h-4 w-4" />,
    generateCategoricalStyle: <Palette className="h-4 w-4" />,
  };
  const toolIcon = TOOL_ICONS[toolName] || <Zap className="h-4 w-4" />; // 기본 아이콘

  const resultMessage = isSuccess
    ? "실행 결과"
    : state === "partial-call"
    ? "작업이 부분적으로 완료되었어요"
    : "작업을 처리하는 중이에요";

  let displayResult: DisplayResult = content;
  if (typeof content === "string") {
    try {
      const parsedResult = JSON.parse(content);
      if (parsedResult && typeof parsedResult === "object") {
        displayResult = parsedResult as AddressResponse;
      }
    } catch (e) {
      // 파싱 실패 시 문자열 그대로 사용
    }
  }

  if (isHilTool) {
    let hilContent = "";
    if (typeof displayResult === 'object' && displayResult !== null) {
      const obj = displayResult as Record<string, any>; // Cast to an indexable type
      if (toolName === "chooseOption") {
        hilContent = `선택: ${'option' in obj ? obj.option : JSON.stringify(obj)}`;
      } else if (toolName === "getUserInput") {
        hilContent = `입력: ${'input' in obj ? obj.input : JSON.stringify(obj)}`;
      } else if (toolName === "confirmWithCheckbox") {
        hilContent = ('confirmed' in obj && obj.confirmed === true) ? "확인 및 동의 완료" : "동의하지 않음";
      } else if (toolName === "getLocation") {
        if ('coord' in obj) {
          const coord = obj.coord
          const accuracy = obj.accuracy ? ` (정확도: ${Math.round(obj.accuracy)}m)` : '';
          hilContent = `위치: ${coord}, ${accuracy}`;
        } else {
          hilContent = JSON.stringify(obj);
        }
      } else {
        hilContent = JSON.stringify(obj); // Should not happen for defined HIL tools
      }
    } else if (typeof displayResult === 'string') {
      if (toolName === "chooseOption") {
        const [selectedOption] = displayResult.split('|');
        hilContent = `${selectedOption}`;
      }
      else if (toolName === "confirmWithCheckbox") {
        hilContent = displayResult.toLowerCase() === 'true' ? "확인 및 동의 완료" : "동의하지 않음";
      } else {
        hilContent = displayResult; // For chooseOption, getUserInput, getLocation
      }
    } else if (typeof displayResult === 'boolean') {
      if (toolName === "confirmWithCheckbox") {
        hilContent = displayResult === true ? "확인 및 동의 완료" : "동의하지 않음";
      } else {
         hilContent = String(displayResult); // Should be rare for other HIL tools
      }
    } else {
      // For null, undefined, numbers, or other unexpected types
      hilContent = JSON.stringify(displayResult);
    }

    return (
      <Card className={cn(componentStyles.card.base, "bg-neutral-50/60 border-neutral-200/50")}>
        <CardHeader className="py-3 px-4">
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <div className={cn(componentStyles.iconContainer.sm, "bg-neutral-100/80")}>
              {toolIcon}
            </div>
            <span className="text-neutral-900">{friendlyName}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="px-4 pb-3 text-sm text-neutral-700">
          <p>{hilContent}</p>
        </CardContent>
      </Card>
    );
  }

  const toolInfo = getToolDisplayInfo(toolName);

  return (
    <CompactResultTrigger
      icon={toolInfo.icon}
      title={toolInfo.label}
      state={state}
      className={className}
      initialOpen={initialOpen}
      open={open}
    >
      {/* 결과 메시지 */}
      <div className="text-xs text-neutral-600 mb-2">{resultMessage}</div>

      {/* 결과 내용 */}
      <div className="text-neutral-700">
        {typeof displayResult === "object" ? (
          <pre className="whitespace-pre-wrap text-xs font-mono bg-white/60 p-2 rounded border border-neutral-200/40 overflow-x-auto max-h-32 overflow-y-auto">
            {JSON.stringify(displayResult, null, 2)}
          </pre>
        ) : (
          <div className="bg-white/60 p-2 rounded border border-neutral-200/40 max-h-32 overflow-y-auto">
            <p className="whitespace-pre-wrap text-xs">{displayResult}</p>
          </div>
        )}
      </div>
    </CompactResultTrigger>
  );
}
