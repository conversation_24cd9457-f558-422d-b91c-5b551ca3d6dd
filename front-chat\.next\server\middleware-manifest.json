{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_c50ec21a._.js", "server/edge/chunks/9c5b9_@auth_core_16e273d6._.js", "server/edge/chunks/96a70_jose_dist_webapi_68ebb33a._.js", "server/edge/chunks/node_modules__pnpm_60fd9d8f._.js", "server/edge/chunks/[root-of-the-server]__b9a0598e._.js", "server/edge/chunks/edge-wrapper_22e6e254.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api\\/|_next\\/|_static\\/|_vercel|[\\w-]+\\.\\w+).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api/|_next/|_static/|_vercel|[\\w-]+\\.\\w+).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "RzLJEFYUW/CFqaruOjruBpAlZrrsX4lJc3SEBXoIqAE=", "__NEXT_PREVIEW_MODE_ID": "f270ef48808041831c59259c474e0843", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7349fad9f8443de43a408381fc7e74aefb2365a45a9bc3e41856fb4814d0f49d", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "860ce8a338cd086787382edc7f25dbd15afe3de429591a321426fcb45388d164"}}}, "sortedMiddleware": ["/"], "functions": {}}