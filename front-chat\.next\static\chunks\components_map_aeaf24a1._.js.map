{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/map/basemap-button.tsx"], "sourcesContent": ["import { Button } from \"@/components/ui/button\"\r\nimport { Hover<PERSON><PERSON>, HoverCardContent, HoverCardTrigger } from \"@/components/ui/hover-card\"\r\nimport { ScrollArea } from \"@/components/ui/scroll-area\"\r\nimport { MapIcon, ChevronDown, Globe2, Mountain, Image, Paintbrush, LucideIcon } from \"lucide-react\"\r\nimport { useState } from \"react\"\r\nimport { motion, AnimatePresence } from \"framer-motion\"\r\n// import { Map, MapView } from \"@/types/map\"; // Replaced by @geon-map/odf types\r\nimport { UseMapReturn } from \"@geon-map/odf\";\r\nimport { useBasemap } from \"@/providers/basemap-provider\";\r\n\r\ninterface BasemapType {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  icon: LucideIcon;  // Changed to LucideIcon type\r\n  color: string;\r\n}\r\n\r\ntype BasemapId = 'eMapBasic' | 'eMapAIR' | 'eMapColor' | 'eMapWhite';\r\n\r\ntype BasemapsType = {\r\n  [key in BasemapId]: BasemapType;\r\n}\r\n\r\nconst BASE_MAPS: BasemapsType = {\r\n  eMapBasic: {\r\n    id: \"eMapBasic\",\r\n    name: \"일반지도\",\r\n    description: \"도로, 건물, 지형지물이 포함된 기본 지도입니다.\",\r\n    icon: Globe2,\r\n    color: \"#4C6EF5\"\r\n  },\r\n  eMapAIR: {\r\n    id: \"eMapAIR\",\r\n    name: \"항공지도\",\r\n    description: \"위성에서 촬영한 실제 지형을 보여주는 항공사진입니다.\",\r\n    icon: Image,\r\n    color: \"#40C057\"\r\n  },\r\n  eMapColor: {\r\n    id: \"eMapColor\",\r\n    name: \"색각지도\",\r\n    description: \"색상 대비를 강조한 특수 목적 지도입니다.\",\r\n    icon: Paintbrush,\r\n    color: \"#FA5252\"\r\n  },\r\n  eMapWhite: {\r\n    id: \"eMapWhite\",\r\n    name: \"백지도\",\r\n    description: \"깔끔한 흰색 배경의 심플한 지도입니다.\",\r\n    icon: Mountain,\r\n    color: \"#845EF7\"\r\n  }\r\n}\r\n\r\ninterface BasemapButtonProps {\r\n  mapState: UseMapReturn;\r\n}\r\n\r\nexport function BasemapButton({ mapState }: BasemapButtonProps) {\r\n  const [isHovered, setIsHovered] = useState(false);\r\n  const { currentBasemap: currentBasemapId, changeBasemap } = useBasemap();\r\n\r\n  const currentBasemap = BASE_MAPS[currentBasemapId] || BASE_MAPS.eMapBasic;\r\n\r\n  const handleBasemapChange = (mapKey: BasemapId) => {\r\n    changeBasemap(mapKey);\r\n  };\r\n\r\n  return (\r\n    <HoverCard openDelay={100}>\r\n      <HoverCardTrigger asChild>\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"sm\"\r\n          className=\"h-9 flex items-center justify-between w-full bg-background/80 hover:bg-background/90 backdrop-blur-md shadow-sm border transition-all duration-300\"\r\n          onMouseEnter={() => setIsHovered(true)}\r\n          onMouseLeave={() => setIsHovered(false)}\r\n        >\r\n          <motion.div\r\n            initial={{ scale: 1 }}\r\n            className=\"flex items-center gap-2\"\r\n          >\r\n            <MapIcon className=\"h-4 w-4\" style={{ color: currentBasemap.color }} />\r\n            <span className=\"text-sm font-medium\">{currentBasemap.name}</span>\r\n          </motion.div>\r\n          <motion.div\r\n            animate={{ rotate: isHovered ? 180 : 0 }}\r\n            transition={{ duration: 0.2 }}\r\n          >\r\n            <ChevronDown className=\"h-4 w-4 opacity-40\" />\r\n          </motion.div>\r\n        </Button>\r\n      </HoverCardTrigger>\r\n      <HoverCardContent \r\n        className=\"w-auto p-0.5 bg-background/90 backdrop-blur-md border shadow-lg rounded-xl\" \r\n        align=\"end\"\r\n        side=\"left\"\r\n        alignOffset={-40}\r\n        sideOffset={8}\r\n      >\r\n        <ScrollArea className=\"h-auto max-h-[280px]\">\r\n          <div className=\"space-y-0.5 p-1\">\r\n            {(Object.entries(BASE_MAPS) as [BasemapId, BasemapType][]).map(([key, basemapItem]) => { // Renamed 'map' to 'basemapItem'\r\n              const Icon = basemapItem.icon;\r\n              const isSelected = currentBasemapId === key; // Use currentBasemapId defined above\r\n              \r\n              return (\r\n                <motion.div\r\n                  key={key}\r\n                  initial={false}\r\n                  whileHover={{ scale: 0.98 }}\r\n                  transition={{ duration: 0.2 }}\r\n                >\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"sm\"\r\n                    className={`w-full py-3 justify-start text-sm relative group transition-all duration-200\r\n                      ${isSelected ? 'bg-muted' : 'hover:bg-muted/50'}`}\r\n                    onClick={() => handleBasemapChange(key)}\r\n                  >\r\n                    <AnimatePresence>\r\n                      {isSelected && (\r\n                        <motion.div\r\n                          initial={{ opacity: 0, x: -10 }}\r\n                          animate={{ opacity: 1, x: 0 }}\r\n                          exit={{ opacity: 0, x: -10 }}\r\n                          className=\"absolute left-0 top-0 w-1 h-full rounded-full\"\r\n                          style={{ backgroundColor: basemapItem.color }}\r\n                        />\r\n                      )}\r\n                    </AnimatePresence>\r\n                    \r\n                    <div className=\"flex items-center gap-3\">\r\n                      <div className={`p-1.5 rounded-lg transition-colors duration-200 \r\n                        ${isSelected ? 'bg-muted-foreground/10' : 'bg-muted'}`}\r\n                      >\r\n                        <Icon \r\n                          className=\"h-4 w-4\"\r\n                          style={{ color: basemapItem.color }}\r\n                        />\r\n                      </div>\r\n                      <div className=\"flex flex-col items-start gap-0.5\">\r\n                        <span className=\"font-medium\">{basemapItem.name}</span>\r\n                        <span className=\"text-[11px] text-muted-foreground leading-tight\">{basemapItem.description}</span>\r\n                      </div>\r\n                    </div>\r\n                  </Button>\r\n                </motion.div>\r\n              );\r\n            })}\r\n          </div>\r\n        </ScrollArea>\r\n      </HoverCardContent>\r\n    </HoverCard>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAGA;;;;;;;;;;AAgBA,MAAM,YAA0B;IAC9B,WAAW;QACT,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,4RAAA,CAAA,SAAM;QACZ,OAAO;IACT;IACA,SAAS;QACP,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,2RAAA,CAAA,QAAK;QACX,OAAO;IACT;IACA,WAAW;QACT,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,qSAAA,CAAA,aAAU;QAChB,OAAO;IACT;IACA,WAAW;QACT,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM,iSAAA,CAAA,WAAQ;QACd,OAAO;IACT;AACF;AAMO,SAAS,cAAc,EAAE,QAAQ,EAAsB;;IAC5D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,gBAAgB,gBAAgB,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,aAAU,AAAD;IAErE,MAAM,iBAAiB,SAAS,CAAC,iBAAiB,IAAI,UAAU,SAAS;IAEzE,MAAM,sBAAsB,CAAC;QAC3B,cAAc;IAChB;IAEA,qBACE,sSAAC,qIAAA,CAAA,YAAS;QAAC,WAAW;;0BACpB,sSAAC,qIAAA,CAAA,mBAAgB;gBAAC,OAAO;0BACvB,cAAA,sSAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,cAAc,IAAM,aAAa;oBACjC,cAAc,IAAM,aAAa;;sCAEjC,sSAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,OAAO;4BAAE;4BACpB,WAAU;;8CAEV,sSAAC,2RAAA,CAAA,UAAO;oCAAC,WAAU;oCAAU,OAAO;wCAAE,OAAO,eAAe,KAAK;oCAAC;;;;;;8CAClE,sSAAC;oCAAK,WAAU;8CAAuB,eAAe,IAAI;;;;;;;;;;;;sCAE5D,sSAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,QAAQ,YAAY,MAAM;4BAAE;4BACvC,YAAY;gCAAE,UAAU;4BAAI;sCAE5B,cAAA,sSAAC,2SAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAI7B,sSAAC,qIAAA,CAAA,mBAAgB;gBACf,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,aAAa,CAAC;gBACd,YAAY;0BAEZ,cAAA,sSAAC,sIAAA,CAAA,aAAU;oBAAC,WAAU;8BACpB,cAAA,sSAAC;wBAAI,WAAU;kCACZ,AAAC,OAAO,OAAO,CAAC,WAA0C,GAAG,CAAC,CAAC,CAAC,KAAK,YAAY;4BAChF,MAAM,OAAO,YAAY,IAAI;4BAC7B,MAAM,aAAa,qBAAqB,KAAK,qCAAqC;4BAElF,qBACE,sSAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;gCACT,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,YAAY;oCAAE,UAAU;gCAAI;0CAE5B,cAAA,sSAAC,8HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAW,CAAC;sBACV,EAAE,aAAa,aAAa,qBAAqB;oCACnD,SAAS,IAAM,oBAAoB;;sDAEnC,sSAAC,kSAAA,CAAA,kBAAe;sDACb,4BACC,sSAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC9B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,MAAM;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC3B,WAAU;gDACV,OAAO;oDAAE,iBAAiB,YAAY,KAAK;gDAAC;;;;;;;;;;;sDAKlD,sSAAC;4CAAI,WAAU;;8DACb,sSAAC;oDAAI,WAAW,CAAC;wBACf,EAAE,aAAa,2BAA2B,YAAY;8DAEtD,cAAA,sSAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,OAAO,YAAY,KAAK;wDAAC;;;;;;;;;;;8DAGtC,sSAAC;oDAAI,WAAU;;sEACb,sSAAC;4DAAK,WAAU;sEAAe,YAAY,IAAI;;;;;;sEAC/C,sSAAC;4DAAK,WAAU;sEAAmD,YAAY,WAAW;;;;;;;;;;;;;;;;;;;;;;;;+BAnC3F;;;;;wBAyCX;;;;;;;;;;;;;;;;;;;;;;AAMZ;GAjGgB;;QAE8C,oIAAA,CAAA,aAAU;;;KAFxD", "debugId": null}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/map/map-controls.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { cn } from \"@/lib/utils\";\nimport { BasemapButton } from \"./basemap-button\";\nimport { LayerListDialog } from \"../chat-map/layer-list-dialog\";\nimport { UseMapReturn } from \"@geon-map/odf\";\n\ninterface MapControlsProps {\n  mapState?: UseMapReturn;\n  className?: string;\n}\n\nexport function MapControls({ mapState, className }: MapControlsProps) {\n  return (\n    <div className={cn(\n      \"absolute top-4 right-4 z-10 flex flex-col gap-2 max-w-[280px]\",\n      className\n    )}>\n      {/* 레이어 검색 다이얼로그 */}\n      <LayerListDialog mapState={mapState} />\n\n      {/* 배경지도 버튼 */}\n      {mapState && <BasemapButton mapState={mapState} />}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAaO,SAAS,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAoB;IACnE,qBACE,sSAAC;QAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACf,iEACA;;0BAGA,sSAAC,wJAAA,CAAA,kBAAe;gBAAC,UAAU;;;;;;YAG1B,0BAAY,sSAAC,0IAAA,CAAA,gBAAa;gBAAC,UAAU;;;;;;;;;;;;AAG5C;KAbgB", "debugId": null}}, {"offset": {"line": 351, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/map/layer-style-editor.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, use<PERSON><PERSON>back, useMemo } from \"react\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Popover, PopoverContent, PopoverTrigger } from \"@/components/ui/popover\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\nimport { Slider } from \"@/components/ui/slider\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Separator } from \"@/components/ui/separator\";\nimport { Palette, Circle, Square, Triangle, Star, Plus, X } from \"lucide-react\";\nimport type { LayerProps } from \"@geon-map/odf\";\nimport { useLayerManager } from \"@/hooks/use-layer-configs\";\nimport {\n  WMSLayerStyle,\n  LayerStyle,\n  createDefaultPointStyle,\n  createDefaultLineStyle,\n  createDefaultPolygonStyle,\n  createDefaultWFSPointStyle,\n  createDefaultWFSLineStyle,\n  createDefaultWFSPolygonStyle,\n  MarkSymbolizer,\n  LineSymbolizer,\n  FillSymbolizer\n} from \"@/types/layer-style\";\n\n// 필터 표시를 위한 헬퍼 함수\nconst formatFilterDisplay = (filter: any): string => {\n  if (!Array.isArray(filter)) {\n    return 'Filter';\n  }\n\n  const [operator, column, value] = filter;\n\n  // 연산자 한글 변환\n  const operatorMap: Record<string, string> = {\n    '==': '=',\n    '!=': '≠',\n    '>': '>',\n    '<': '<',\n    '>=': '≥',\n    '<=': '≤',\n    '*=': '포함',\n    '&&': '그리고',\n    '||': '또는'\n  };\n\n  // 복합 조건 처리\n  if (operator === '&&' || operator === '||') {\n    const condition1 = Array.isArray(column) ? formatFilterDisplay(column) : column;\n    const condition2 = Array.isArray(value) ? formatFilterDisplay(value) : value;\n    return `${condition1} ${operatorMap[operator]} ${condition2}`;\n  }\n\n  // 단일 조건 처리\n  const operatorText = operatorMap[operator] || operator;\n  const columnText = typeof column === 'string' ? column : String(column);\n  const valueText = value === null ? 'null' : String(value);\n\n  // 특별한 경우들 처리\n  if (operator === '*=') {\n    return `${columnText} 포함 \"${valueText}\"`;\n  }\n\n  if (value === null) {\n    return operator === '==' ? `${columnText} 없음` : `${columnText} 있음`;\n  }\n\n  // 숫자 범위 표현 개선\n  if (typeof value === 'number') {\n    if (operator === '>') return `${columnText} > ${valueText}`;\n    if (operator === '<') return `${columnText} < ${valueText}`;\n    if (operator === '>=') return `${columnText} ≥ ${valueText}`;\n    if (operator === '<=') return `${columnText} ≤ ${valueText}`;\n  }\n\n  return `${columnText} ${operatorText} ${valueText}`;\n};\n\ninterface LayerStyleEditorProps {\n  layer: LayerProps;\n  onStyleChange: (style: LayerStyle) => void;\n}\n\nexport function LayerStyleEditor({ layer, onStyleChange }: LayerStyleEditorProps) {\n  const [isOpen, setIsOpen] = useState(false);\n  const layerManager = useLayerManager();\n\n  // 레이어 상태 정보 가져오기 (확장된 정보 포함)\n  const layerWithState = useMemo(() =>\n    layer.id ? layerManager.getLayerWithState(layer.id) : null,\n    [layerManager, layer.id]\n  );\n\n  // 레이어 타입과 서비스 타입 확인\n  const isWMSLayer = layerWithState?.styleType === 'wms';\n  const isWFSLayer = layerWithState?.styleType === 'wfs';\n  const geometryType = layerWithState?.geometryType || 'point';\n\n\n\n\n\n  // 단일 스타일로 변경 핸들러\n  const handleConvertToSingleStyle = () => {\n    const defaultStyle = (() => {\n      switch (geometryType) {\n        case 'point':\n        case '1':\n          return createDefaultPointStyle();\n        case 'line':\n        case '2':\n          return createDefaultLineStyle();\n        case 'polygon':\n        case '3':\n          return createDefaultPolygonStyle();\n        default:\n          return createDefaultPointStyle();\n      }\n    })();\n\n    handleStyleChange(defaultStyle);\n  };\n\n  // 스타일 초기화 핸들러\n  const handleResetStyle = () => {\n    const defaultStyle = getCurrentStyle();\n    handleStyleChange(defaultStyle);\n  };\n  \n  // 현재 스타일 가져오기 - 레이어 상태 시스템 사용\n  const getCurrentStyle = useCallback((): LayerStyle => {\n    // 레이어 상태에서 현재 스타일 가져오기\n    const currentStyle = layerWithState?.currentStyle;\n\n    if (currentStyle) {\n      return currentStyle;\n    }\n\n    // 기본 스타일 생성\n    if (isWMSLayer) {\n      switch (geometryType) {\n        case 'point':\n        case '1':\n          return createDefaultPointStyle();\n        case 'line':\n        case '2':\n          return createDefaultLineStyle();\n        case 'polygon':\n        case '3':\n          return createDefaultPolygonStyle();\n        default:\n          return createDefaultPointStyle();\n      }\n    } else if (isWFSLayer) {\n      switch (geometryType) {\n        case 'point':\n        case '1':\n          return createDefaultWFSPointStyle();\n        case 'line':\n        case '2':\n          return createDefaultWFSLineStyle();\n        case 'polygon':\n        case '3':\n          return createDefaultWFSPolygonStyle();\n        default:\n          return createDefaultWFSPointStyle();\n      }\n    }\n\n    return createDefaultPointStyle();\n  }, [layerWithState, isWMSLayer, isWFSLayer, geometryType]);\n\n  // 현재 스타일 상태 - 레이어 상태와 동기화\n  const [currentStyle, setCurrentStyle] = useState<LayerStyle>(() => getCurrentStyle());\n\n  // 레이어 상태 변경 시 currentStyle 동기화 (무한 루프 방지)\n  React.useEffect(() => {\n    const newStyle = getCurrentStyle();\n    // 실제로 스타일이 변경된 경우에만 업데이트\n    setCurrentStyle(prevStyle => {\n      if (JSON.stringify(newStyle) !== JSON.stringify(prevStyle)) {\n        return newStyle;\n      }\n      return prevStyle;\n    });\n  }, [layerWithState?.currentStyle, getCurrentStyle]); // getCurrentStyle은 useCallback으로 안정화됨\n\n  // 스타일 변경 핸들러 - 최적화된 레이어 관리 시스템 사용\n  const handleStyleChange = useCallback((newStyle: LayerStyle) => {\n    setCurrentStyle(newStyle);\n\n    // 레이어 관리자를 통한 최적화된 스타일 업데이트\n    if (layer.id) {\n      layerManager.updateStyle(layer.id, newStyle);\n    }\n\n    // 기존 콜백도 호출 (하위 호환성)\n    if (onStyleChange) {\n      onStyleChange(newStyle);\n    }\n  }, [layerManager, layer.id, onStyleChange]);\n\n  // 심볼 타입 아이콘 매핑\n  const getSymbolIcon = (symbolType: string) => {\n    switch (symbolType) {\n      case 'circle':\n        return <Circle size={16} />;\n      case 'square':\n        return <Square size={16} />;\n      case 'triangle':\n        return <Triangle size={16} />;\n      case 'star':\n        return <Star size={16} />;\n      case 'cross':\n        return <Plus size={16} />;\n      case 'x':\n        return <X size={16} />;\n      default:\n        return <Circle size={16} />;\n    }\n  };\n\n  // 지오메트리 타입 한국어 변환\n  const getGeometryTypeText = (type: string) => {\n    switch (type) {\n      case 'point':\n      case '1':\n        return '포인트';\n      case 'line':\n      case '2':\n        return '라인';\n      case 'polygon':\n      case '3':\n        return '폴리곤';\n      default:\n        return '알 수 없음';\n    }\n  };\n\n  const renderWMSStyleEditor = (style: WMSLayerStyle) => {\n    // 여러 rule이 있는 경우 (유형별 스타일)\n    if (style.rules.length > 1) {\n      return (\n        <div className=\"space-y-3\">\n          <div className=\"flex items-center justify-between\">\n            <Label className=\"text-sm font-medium\">유형별 스타일</Label>\n            <Badge variant=\"secondary\" className=\"text-xs\">\n              {style.rules.length}개 규칙\n            </Badge>\n          </div>\n\n          {style.rules.map((rule, index) => {\n            const symbolizer = rule.symbolizers[0];\n\n            // 심볼라이저 업데이트 헬퍼 함수\n            const updateRuleSymbolizer = (updates: any) => {\n              const updatedSymbolizer = { ...symbolizer, ...updates };\n              const newStyle: WMSLayerStyle = {\n                ...style,\n                rules: style.rules.map((r, i) =>\n                  i === index\n                    ? { ...r, symbolizers: [updatedSymbolizer] }\n                    : r\n                )\n              };\n              handleStyleChange(newStyle);\n            };\n\n            return (\n              <Card key={index} className=\"mb-3\">\n                <CardContent className=\"p-4\">\n                  {/* 규칙 헤더 */}\n                  <div className=\"flex items-center justify-between mb-3\">\n                    <div className=\"flex items-center gap-2\">\n                      <span className=\"text-sm font-medium\">{rule.name}</span>\n                    </div>\n                    {rule.filter && (\n                      <Badge variant=\"outline\" className=\"text-xs\">\n                        {formatFilterDisplay(rule.filter)}\n                      </Badge>\n                    )}\n                  </div>\n\n                  {/* Mark 심볼라이저 편집 */}\n                  {symbolizer.kind === 'Mark' && (() => {\n                    const markSymbolizer = symbolizer as MarkSymbolizer;\n                    return (\n                      <div className=\"space-y-3\">\n                        {/* 색상 및 크기 */}\n                        <div className=\"grid grid-cols-2 gap-3\">\n                          <div className=\"space-y-2\">\n                            <Label className=\"text-xs text-muted-foreground\">색상</Label>\n                            <Input\n                              type=\"color\"\n                              value={markSymbolizer.color}\n                              onChange={(e) => updateRuleSymbolizer({ color: e.target.value })}\n                              className=\"h-8 w-full cursor-pointer\"\n                            />\n                          </div>\n                          <div className=\"space-y-2\">\n                            <div className=\"flex items-center justify-between\">\n                              <Label className=\"text-xs text-muted-foreground\">크기</Label>\n                              <Badge variant=\"outline\" className=\"text-xs\">\n                                {markSymbolizer.radius}px\n                              </Badge>\n                            </div>\n                            <Slider\n                              value={[markSymbolizer.radius]}\n                              onValueChange={([value]) => updateRuleSymbolizer({ radius: value })}\n                              min={1}\n                              max={20}\n                              step={1}\n                              className=\"w-full\"\n                            />\n                          </div>\n                        </div>\n\n                        {/* 투명도 */}\n                        <div className=\"space-y-2\">\n                          <div className=\"flex items-center justify-between\">\n                            <Label className=\"text-xs text-muted-foreground\">투명도</Label>\n                            <Badge variant=\"outline\" className=\"text-xs\">\n                              {Math.round(markSymbolizer.fillOpacity * 100)}%\n                            </Badge>\n                          </div>\n                          <Slider\n                            value={[markSymbolizer.fillOpacity]}\n                            onValueChange={([value]) => updateRuleSymbolizer({ fillOpacity: value })}\n                            min={0}\n                            max={1}\n                            step={0.1}\n                            className=\"w-full\"\n                          />\n                        </div>\n\n                        {/* 윤곽선 */}\n                        {markSymbolizer.strokeColor && (\n                          <div className=\"grid grid-cols-2 gap-3\">\n                            <div className=\"space-y-2\">\n                              <Label className=\"text-xs text-muted-foreground\">윤곽선 색상</Label>\n                              <Input\n                                type=\"color\"\n                                value={markSymbolizer.strokeColor}\n                                onChange={(e) => updateRuleSymbolizer({ strokeColor: e.target.value })}\n                                className=\"h-8 w-full cursor-pointer\"\n                              />\n                            </div>\n                            <div className=\"space-y-2\">\n                              <div className=\"flex items-center justify-between\">\n                                <Label className=\"text-xs text-muted-foreground\">윤곽선 두께</Label>\n                                <Badge variant=\"outline\" className=\"text-xs\">\n                                  {markSymbolizer.strokeWidth || 1}px\n                                </Badge>\n                              </div>\n                              <Slider\n                                value={[markSymbolizer.strokeWidth || 1]}\n                                onValueChange={([value]) => updateRuleSymbolizer({ strokeWidth: value })}\n                                min={0}\n                                max={10}\n                                step={1}\n                                className=\"w-full\"\n                              />\n                            </div>\n                          </div>\n                        )}\n                      </div>\n                    );\n                  })()}\n\n                  {/* Line 심볼라이저 편집 */}\n                  {symbolizer.kind === 'Line' && (() => {\n                    const lineSymbolizer = symbolizer as LineSymbolizer;\n                    return (\n                      <div className=\"space-y-3\">\n                        <div className=\"grid grid-cols-2 gap-3\">\n                          <div className=\"space-y-2\">\n                            <Label className=\"text-xs text-muted-foreground\">색상</Label>\n                            <Input\n                              type=\"color\"\n                              value={lineSymbolizer.color}\n                              onChange={(e) => updateRuleSymbolizer({ color: e.target.value })}\n                              className=\"h-8 w-full cursor-pointer\"\n                            />\n                          </div>\n                          <div className=\"space-y-2\">\n                            <div className=\"flex items-center justify-between\">\n                              <Label className=\"text-xs text-muted-foreground\">두께</Label>\n                              <Badge variant=\"outline\" className=\"text-xs\">\n                                {lineSymbolizer.width}px\n                              </Badge>\n                            </div>\n                            <Slider\n                              value={[lineSymbolizer.width]}\n                              onValueChange={([value]) => updateRuleSymbolizer({ width: value })}\n                              min={1}\n                              max={10}\n                              step={1}\n                              className=\"w-full\"\n                            />\n                          </div>\n                        </div>\n                        <div className=\"space-y-2\">\n                          <div className=\"flex items-center justify-between\">\n                            <Label className=\"text-xs text-muted-foreground\">투명도</Label>\n                            <Badge variant=\"outline\" className=\"text-xs\">\n                              {Math.round(lineSymbolizer.opacity * 100)}%\n                            </Badge>\n                          </div>\n                          <Slider\n                            value={[lineSymbolizer.opacity]}\n                            onValueChange={([value]) => updateRuleSymbolizer({ opacity: value })}\n                            min={0}\n                            max={1}\n                            step={0.1}\n                            className=\"w-full\"\n                          />\n                        </div>\n                      </div>\n                    );\n                  })()}\n\n                  {/* Fill 심볼라이저 편집 */}\n                  {symbolizer.kind === 'Fill' && (() => {\n                    const fillSymbolizer = symbolizer as FillSymbolizer;\n                    return (\n                      <div className=\"space-y-3\">\n                        <div className=\"grid grid-cols-2 gap-3\">\n                          <div className=\"space-y-2\">\n                            <Label className=\"text-xs text-muted-foreground\">채우기 색상</Label>\n                            <Input\n                              type=\"color\"\n                              value={fillSymbolizer.color}\n                              onChange={(e) => updateRuleSymbolizer({ color: e.target.value })}\n                              className=\"h-8 w-full cursor-pointer\"\n                            />\n                          </div>\n                          <div className=\"space-y-2\">\n                            <div className=\"flex items-center justify-between\">\n                              <Label className=\"text-xs text-muted-foreground\">투명도</Label>\n                              <Badge variant=\"outline\" className=\"text-xs\">\n                                {Math.round(fillSymbolizer.fillOpacity * 100)}%\n                              </Badge>\n                            </div>\n                            <Slider\n                              value={[fillSymbolizer.fillOpacity]}\n                              onValueChange={([value]) => updateRuleSymbolizer({ fillOpacity: value })}\n                              min={0}\n                              max={1}\n                              step={0.1}\n                              className=\"w-full\"\n                            />\n                          </div>\n                        </div>\n\n                        {/* 윤곽선 */}\n                        {fillSymbolizer.outlineColor && (\n                          <div className=\"grid grid-cols-2 gap-3\">\n                            <div className=\"space-y-2\">\n                              <Label className=\"text-xs text-muted-foreground\">윤곽선 색상</Label>\n                              <Input\n                                type=\"color\"\n                                value={fillSymbolizer.outlineColor}\n                                onChange={(e) => updateRuleSymbolizer({ outlineColor: e.target.value })}\n                                className=\"h-8 w-full cursor-pointer\"\n                              />\n                            </div>\n                            <div className=\"space-y-2\">\n                              <div className=\"flex items-center justify-between\">\n                                <Label className=\"text-xs text-muted-foreground\">윤곽선 두께</Label>\n                                <Badge variant=\"outline\" className=\"text-xs\">\n                                  {fillSymbolizer.outlineWidth || 1}px\n                                </Badge>\n                              </div>\n                              <Slider\n                                value={[fillSymbolizer.outlineWidth || 1]}\n                                onValueChange={([value]) => updateRuleSymbolizer({ outlineWidth: value })}\n                                min={0}\n                                max={10}\n                                step={1}\n                                className=\"w-full\"\n                              />\n                            </div>\n                          </div>\n                        )}\n                      </div>\n                    );\n                  })()}\n                </CardContent>\n              </Card>\n            );\n          })}\n        </div>\n      );\n    }\n\n    // 단일 rule인 경우 (기존 로직)\n    const rule = style.rules[0];\n    const symbolizer = rule.symbolizers[0];\n\n    if (symbolizer.kind === 'Mark') {\n      const markSymbolizer = symbolizer as MarkSymbolizer;\n      return (\n        <div className=\"space-y-4\">\n          {/* 심볼 타입 선택 */}\n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center justify-between\">\n                  <Label className=\"text-sm font-medium\">심볼 타입</Label>\n                  <div className=\"flex items-center gap-2\">\n                    {getSymbolIcon(markSymbolizer.wellKnownName)}\n                    <span className=\"text-xs text-muted-foreground capitalize\">\n                      {markSymbolizer.wellKnownName}\n                    </span>\n                  </div>\n                </div>\n                <Select\n                  value={markSymbolizer.wellKnownName}\n                  onValueChange={(value) => {\n                    const newStyle: WMSLayerStyle = {\n                      ...style,\n                      rules: [{\n                        ...rule,\n                        symbolizers: [{\n                          ...markSymbolizer,\n                          wellKnownName: value as any\n                        }]\n                      }]\n                    };\n                    handleStyleChange(newStyle);\n                  }}\n                >\n                  <SelectTrigger className=\"h-9\">\n                    <SelectValue />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"circle\">\n                      <div className=\"flex items-center gap-2\">\n                        <Circle size={14} />\n                        <span>원</span>\n                      </div>\n                    </SelectItem>\n                    <SelectItem value=\"square\">\n                      <div className=\"flex items-center gap-2\">\n                        <Square size={14} />\n                        <span>사각형</span>\n                      </div>\n                    </SelectItem>\n                    <SelectItem value=\"triangle\">\n                      <div className=\"flex items-center gap-2\">\n                        <Triangle size={14} />\n                        <span>삼각형</span>\n                      </div>\n                    </SelectItem>\n                    <SelectItem value=\"star\">\n                      <div className=\"flex items-center gap-2\">\n                        <Star size={14} />\n                        <span>별</span>\n                      </div>\n                    </SelectItem>\n                    <SelectItem value=\"cross\">\n                      <div className=\"flex items-center gap-2\">\n                        <Plus size={14} />\n                        <span>십자</span>\n                      </div>\n                    </SelectItem>\n                    <SelectItem value=\"x\">\n                      <div className=\"flex items-center gap-2\">\n                        <X size={14} />\n                        <span>X</span>\n                      </div>\n                    </SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* 크기 설정 */}\n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center justify-between\">\n                  <Label className=\"text-sm font-medium\">크기</Label>\n                  <Badge variant=\"secondary\">{markSymbolizer.radius}px</Badge>\n                </div>\n                <Slider\n                  value={[markSymbolizer.radius]}\n                  onValueChange={([value]) => {\n                    const newStyle: WMSLayerStyle = {\n                      ...style,\n                      rules: [{\n                        ...rule,\n                        symbolizers: [{\n                          ...markSymbolizer,\n                          radius: value\n                        }]\n                      }]\n                    };\n                    handleStyleChange(newStyle);\n                  }}\n                  min={1}\n                  max={20}\n                  step={1}\n                  className=\"w-full\"\n                />\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* 색상 및 투명도 설정 */}\n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"space-y-4\">\n                <Label className=\"text-sm font-medium\">채우기</Label>\n                <div className=\"grid grid-cols-2 gap-3\">\n                  <div className=\"space-y-2\">\n                    <Label className=\"text-xs text-muted-foreground\">색상</Label>\n                    <div className=\"flex items-center gap-2\">\n                      <Input\n                        type=\"color\"\n                        value={markSymbolizer.color}\n                        onChange={(e) => {\n                          const newStyle: WMSLayerStyle = {\n                            ...style,\n                            rules: [{\n                              ...rule,\n                              symbolizers: [{\n                                ...markSymbolizer,\n                                color: e.target.value\n                              }]\n                            }]\n                          };\n                          handleStyleChange(newStyle);\n                        }}\n                        className=\"h-9 w-full cursor-pointer\"\n                      />\n                    </div>\n                  </div>\n                  <div className=\"space-y-2\">\n                    <div className=\"flex items-center justify-between\">\n                      <Label className=\"text-xs text-muted-foreground\">투명도</Label>\n                      <Badge variant=\"outline\" className=\"text-xs\">\n                        {Math.round(markSymbolizer.fillOpacity * 100)}%\n                      </Badge>\n                    </div>\n                    <Slider\n                      value={[markSymbolizer.fillOpacity]}\n                      onValueChange={([value]) => {\n                        const newStyle: WMSLayerStyle = {\n                          ...style,\n                          rules: [{\n                            ...rule,\n                            symbolizers: [{\n                              ...markSymbolizer,\n                              fillOpacity: value\n                            }]\n                          }]\n                        };\n                        handleStyleChange(newStyle);\n                      }}\n                      min={0}\n                      max={1}\n                      step={0.1}\n                      className=\"w-full\"\n                    />\n                  </div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* 윤곽선 설정 */}\n          {markSymbolizer.strokeColor && (\n            <Card>\n              <CardContent className=\"p-4\">\n                <div className=\"space-y-4\">\n                  <Label className=\"text-sm font-medium\">윤곽선</Label>\n                  <div className=\"grid grid-cols-2 gap-3\">\n                    <div className=\"space-y-2\">\n                      <Label className=\"text-xs text-muted-foreground\">색상</Label>\n                      <Input\n                        type=\"color\"\n                        value={markSymbolizer.strokeColor}\n                        onChange={(e) => {\n                          const newStyle: WMSLayerStyle = {\n                            ...style,\n                            rules: [{\n                              ...rule,\n                              symbolizers: [{\n                                ...markSymbolizer,\n                                strokeColor: e.target.value\n                              }]\n                            }]\n                          };\n                          handleStyleChange(newStyle);\n                        }}\n                        className=\"h-9 w-full cursor-pointer\"\n                      />\n                    </div>\n                    <div className=\"space-y-2\">\n                      <div className=\"flex items-center justify-between\">\n                        <Label className=\"text-xs text-muted-foreground\">두께</Label>\n                        <Badge variant=\"outline\" className=\"text-xs\">\n                          {markSymbolizer.strokeWidth || 1}px\n                        </Badge>\n                      </div>\n                      <Slider\n                        value={[markSymbolizer.strokeWidth || 1]}\n                        onValueChange={([value]) => {\n                          const newStyle: WMSLayerStyle = {\n                            ...style,\n                            rules: [{\n                              ...rule,\n                              symbolizers: [{\n                                ...markSymbolizer,\n                                strokeWidth: value\n                              }]\n                            }]\n                          };\n                          handleStyleChange(newStyle);\n                        }}\n                        min={0}\n                        max={10}\n                        step={1}\n                        className=\"w-full\"\n                      />\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n        </div>\n      );\n    }\n\n    if (symbolizer.kind === 'Line') {\n      const lineSymbolizer = symbolizer as LineSymbolizer;\n      return (\n        <div className=\"space-y-4\">\n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"space-y-4\">\n                <Label className=\"text-sm font-medium\">선 스타일</Label>\n                <div className=\"grid grid-cols-2 gap-3\">\n                  <div className=\"space-y-2\">\n                    <Label className=\"text-xs text-muted-foreground\">색상</Label>\n                    <Input\n                      type=\"color\"\n                      value={lineSymbolizer.color}\n                      onChange={(e) => {\n                        const newStyle: WMSLayerStyle = {\n                          ...style,\n                          rules: [{\n                            ...rule,\n                            symbolizers: [{\n                              ...lineSymbolizer,\n                              color: e.target.value\n                            }]\n                          }]\n                        };\n                        handleStyleChange(newStyle);\n                      }}\n                      className=\"h-9 w-full cursor-pointer\"\n                    />\n                  </div>\n                  <div className=\"space-y-2\">\n                    <div className=\"flex items-center justify-between\">\n                      <Label className=\"text-xs text-muted-foreground\">투명도</Label>\n                      <Badge variant=\"outline\" className=\"text-xs\">\n                        {Math.round(lineSymbolizer.opacity * 100)}%\n                      </Badge>\n                    </div>\n                    <Slider\n                      value={[lineSymbolizer.opacity]}\n                      onValueChange={([value]) => {\n                        const newStyle: WMSLayerStyle = {\n                          ...style,\n                          rules: [{\n                            ...rule,\n                            symbolizers: [{\n                              ...lineSymbolizer,\n                              opacity: value\n                            }]\n                          }]\n                        };\n                        handleStyleChange(newStyle);\n                      }}\n                      min={0}\n                      max={1}\n                      step={0.1}\n                      className=\"w-full\"\n                    />\n                  </div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center justify-between\">\n                  <Label className=\"text-sm font-medium\">선 두께</Label>\n                  <Badge variant=\"secondary\">{lineSymbolizer.width}px</Badge>\n                </div>\n                <Slider\n                  value={[lineSymbolizer.width]}\n                  onValueChange={([value]) => {\n                    const newStyle: WMSLayerStyle = {\n                      ...style,\n                      rules: [{\n                        ...rule,\n                        symbolizers: [{\n                          ...lineSymbolizer,\n                          width: value\n                        }]\n                      }]\n                    };\n                    handleStyleChange(newStyle);\n                  }}\n                  min={1}\n                  max={10}\n                  step={1}\n                  className=\"w-full\"\n                />\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      );\n    }\n\n    if (symbolizer.kind === 'Fill') {\n      const fillSymbolizer = symbolizer as FillSymbolizer;\n      return (\n        <div className=\"space-y-4\">\n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"space-y-4\">\n                <Label className=\"text-sm font-medium\">채우기</Label>\n                <div className=\"grid grid-cols-2 gap-3\">\n                  <div className=\"space-y-2\">\n                    <Label className=\"text-xs text-muted-foreground\">색상</Label>\n                    <Input\n                      type=\"color\"\n                      value={fillSymbolizer.color}\n                      onChange={(e) => {\n                        const newStyle: WMSLayerStyle = {\n                          ...style,\n                          rules: [{\n                            ...rule,\n                            symbolizers: [{\n                              ...fillSymbolizer,\n                              color: e.target.value\n                            }]\n                          }]\n                        };\n                        handleStyleChange(newStyle);\n                      }}\n                      className=\"h-9 w-full cursor-pointer\"\n                    />\n                  </div>\n                  <div className=\"space-y-2\">\n                    <div className=\"flex items-center justify-between\">\n                      <Label className=\"text-xs text-muted-foreground\">투명도</Label>\n                      <Badge variant=\"outline\" className=\"text-xs\">\n                        {Math.round(fillSymbolizer.fillOpacity * 100)}%\n                      </Badge>\n                    </div>\n                    <Slider\n                      value={[fillSymbolizer.fillOpacity]}\n                      onValueChange={([value]) => {\n                        const newStyle: WMSLayerStyle = {\n                          ...style,\n                          rules: [{\n                            ...rule,\n                            symbolizers: [{\n                              ...fillSymbolizer,\n                              fillOpacity: value\n                            }]\n                          }]\n                        };\n                        handleStyleChange(newStyle);\n                      }}\n                      min={0}\n                      max={1}\n                      step={0.1}\n                      className=\"w-full\"\n                    />\n                  </div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {fillSymbolizer.outlineColor && (\n            <Card>\n              <CardContent className=\"p-4\">\n                <div className=\"space-y-4\">\n                  <Label className=\"text-sm font-medium\">윤곽선</Label>\n                  <div className=\"grid grid-cols-2 gap-3\">\n                    <div className=\"space-y-2\">\n                      <Label className=\"text-xs text-muted-foreground\">색상</Label>\n                      <Input\n                        type=\"color\"\n                        value={fillSymbolizer.outlineColor}\n                        onChange={(e) => {\n                          const newStyle: WMSLayerStyle = {\n                            ...style,\n                            rules: [{\n                              ...rule,\n                              symbolizers: [{\n                                ...fillSymbolizer,\n                                outlineColor: e.target.value\n                              }]\n                            }]\n                          };\n                          handleStyleChange(newStyle);\n                        }}\n                        className=\"h-9 w-full cursor-pointer\"\n                      />\n                    </div>\n                    <div className=\"space-y-2\">\n                      <div className=\"flex items-center justify-between\">\n                        <Label className=\"text-xs text-muted-foreground\">두께</Label>\n                        <Badge variant=\"outline\" className=\"text-xs\">\n                          {fillSymbolizer.outlineWidth || 1}px\n                        </Badge>\n                      </div>\n                      <Slider\n                        value={[fillSymbolizer.outlineWidth || 1]}\n                        onValueChange={([value]) => {\n                          const newStyle: WMSLayerStyle = {\n                            ...style,\n                            rules: [{\n                              ...rule,\n                              symbolizers: [{\n                                ...fillSymbolizer,\n                                outlineWidth: value\n                              }]\n                            }]\n                          };\n                          handleStyleChange(newStyle);\n                        }}\n                        min={0}\n                        max={10}\n                        step={1}\n                        className=\"w-full\"\n                      />\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n        </div>\n      );\n    }\n\n    return null;\n  };\n\n  return (\n    <Popover open={isOpen} onOpenChange={setIsOpen}>\n      <PopoverTrigger asChild>\n        <Button \n          variant=\"ghost\" \n          size=\"icon\"\n        >\n          <Palette size={16} />\n        </Button>\n      </PopoverTrigger>\n      <PopoverContent className=\"w-96 p-0\" side=\"left\" align=\"start\">\n        <div className=\"p-4\">\n          {/* 헤더 */}\n          <div className=\"space-y-3 mb-4\">\n            <div className=\"flex items-center justify-between\">\n              <h4 className=\"font-semibold text-base\">레이어 스타일</h4>\n              <div className=\"flex items-center gap-2\">\n                <Badge variant=\"outline\" className=\"text-xs\">\n                  {isWMSLayer ? 'WMS' : isWFSLayer ? 'WFS' : '기타'}\n                </Badge>\n                <Badge variant=\"secondary\" className=\"text-xs\">\n                  {getGeometryTypeText(geometryType)}\n                </Badge>\n              </div>\n            </div>\n\n            <div className=\"text-sm text-muted-foreground font-medium truncate\">\n              {(layer as any).name || layer.id}\n            </div>\n\n            {/* 스타일 변경 버튼들 */}\n            {isWMSLayer && (\n              <div className=\"space-y-2\">\n                {/* 현재 스타일이 단일인 경우 */}\n                {('rules' in currentStyle && currentStyle.rules.length === 1) && (\n                  <div className=\"flex gap-2\">\n                    <Button\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      className=\"flex-1 text-xs text-muted-foreground\"\n                      onClick={handleResetStyle}\n                    >\n                      초기화\n                    </Button>\n                  </div>\n                )}\n\n                {/* 현재 스타일이 유형별인 경우 */}\n                {('rules' in currentStyle && currentStyle.rules.length > 1) && (\n                  <div className=\"flex gap-2\">\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      className=\"flex-1 text-xs\"\n                      onClick={handleConvertToSingleStyle}\n                    >\n                      <Circle size={14} className=\"mr-1\" />\n                      단일 스타일로 변경\n                    </Button>\n                    <Button\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      className=\"flex-1 text-xs text-muted-foreground\"\n                      onClick={handleResetStyle}\n                    >\n                      초기화\n                    </Button>\n                  </div>\n                )}\n              </div>\n            )}\n\n            <Separator />\n          </div>\n\n          {/* 스타일 편집기 */}\n          <div className=\"space-y-4\">\n            {isWMSLayer && 'rules' in currentStyle && (\n              <div className=\"max-h-96 overflow-y-auto space-y-1\">\n                {renderWMSStyleEditor(currentStyle)}\n              </div>\n            )}\n\n            {isWFSLayer && !('rules' in currentStyle) && (\n              <Card>\n                <CardContent className=\"p-6\">\n                  <div className=\"text-center space-y-2\">\n                    <div className=\"text-sm font-medium\">개발 중</div>\n                    <div className=\"text-xs text-muted-foreground\">\n                      WFS 레이어 스타일 편집기는 추후 구현 예정입니다.\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            )}\n\n            {!isWMSLayer && !isWFSLayer && (\n              <Card>\n                <CardContent className=\"p-6\">\n                  <div className=\"text-center space-y-2\">\n                    <div className=\"text-sm font-medium\">지원되지 않음</div>\n                    <div className=\"text-xs text-muted-foreground\">\n                      이 레이어 타입은 스타일 편집을 지원하지 않습니다.\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            )}\n          </div>\n        </div>\n      </PopoverContent>\n    </Popover>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;;;AAfA;;;;;;;;;;;;;;AA6BA,kBAAkB;AAClB,MAAM,sBAAsB,CAAC;IAC3B,IAAI,CAAC,MAAM,OAAO,CAAC,SAAS;QAC1B,OAAO;IACT;IAEA,MAAM,CAAC,UAAU,QAAQ,MAAM,GAAG;IAElC,YAAY;IACZ,MAAM,cAAsC;QAC1C,MAAM;QACN,MAAM;QACN,KAAK;QACL,KAAK;QACL,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;IACR;IAEA,WAAW;IACX,IAAI,aAAa,QAAQ,aAAa,MAAM;QAC1C,MAAM,aAAa,MAAM,OAAO,CAAC,UAAU,oBAAoB,UAAU;QACzE,MAAM,aAAa,MAAM,OAAO,CAAC,SAAS,oBAAoB,SAAS;QACvE,OAAO,GAAG,WAAW,CAAC,EAAE,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,YAAY;IAC/D;IAEA,WAAW;IACX,MAAM,eAAe,WAAW,CAAC,SAAS,IAAI;IAC9C,MAAM,aAAa,OAAO,WAAW,WAAW,SAAS,OAAO;IAChE,MAAM,YAAY,UAAU,OAAO,SAAS,OAAO;IAEnD,aAAa;IACb,IAAI,aAAa,MAAM;QACrB,OAAO,GAAG,WAAW,KAAK,EAAE,UAAU,CAAC,CAAC;IAC1C;IAEA,IAAI,UAAU,MAAM;QAClB,OAAO,aAAa,OAAO,GAAG,WAAW,GAAG,CAAC,GAAG,GAAG,WAAW,GAAG,CAAC;IACpE;IAEA,cAAc;IACd,IAAI,OAAO,UAAU,UAAU;QAC7B,IAAI,aAAa,KAAK,OAAO,GAAG,WAAW,GAAG,EAAE,WAAW;QAC3D,IAAI,aAAa,KAAK,OAAO,GAAG,WAAW,GAAG,EAAE,WAAW;QAC3D,IAAI,aAAa,MAAM,OAAO,GAAG,WAAW,GAAG,EAAE,WAAW;QAC5D,IAAI,aAAa,MAAM,OAAO,GAAG,WAAW,GAAG,EAAE,WAAW;IAC9D;IAEA,OAAO,GAAG,WAAW,CAAC,EAAE,aAAa,CAAC,EAAE,WAAW;AACrD;AAOO,SAAS,iBAAiB,EAAE,KAAK,EAAE,aAAa,EAAyB;;IAC9E,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,eAAe,CAAA,GAAA,mIAAA,CAAA,kBAAe,AAAD;IAEnC,6BAA6B;IAC7B,MAAM,iBAAiB,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;oDAAE,IAC7B,MAAM,EAAE,GAAG,aAAa,iBAAiB,CAAC,MAAM,EAAE,IAAI;mDACtD;QAAC;QAAc,MAAM,EAAE;KAAC;IAG1B,oBAAoB;IACpB,MAAM,aAAa,gBAAgB,cAAc;IACjD,MAAM,aAAa,gBAAgB,cAAc;IACjD,MAAM,eAAe,gBAAgB,gBAAgB;IAMrD,iBAAiB;IACjB,MAAM,6BAA6B;QACjC,MAAM,eAAe,CAAC;YACpB,OAAQ;gBACN,KAAK;gBACL,KAAK;oBACH,OAAO,CAAA,GAAA,0HAAA,CAAA,0BAAuB,AAAD;gBAC/B,KAAK;gBACL,KAAK;oBACH,OAAO,CAAA,GAAA,0HAAA,CAAA,yBAAsB,AAAD;gBAC9B,KAAK;gBACL,KAAK;oBACH,OAAO,CAAA,GAAA,0HAAA,CAAA,4BAAyB,AAAD;gBACjC;oBACE,OAAO,CAAA,GAAA,0HAAA,CAAA,0BAAuB,AAAD;YACjC;QACF,CAAC;QAED,kBAAkB;IACpB;IAEA,cAAc;IACd,MAAM,mBAAmB;QACvB,MAAM,eAAe;QACrB,kBAAkB;IACpB;IAEA,8BAA8B;IAC9B,MAAM,kBAAkB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;yDAAE;YAClC,uBAAuB;YACvB,MAAM,eAAe,gBAAgB;YAErC,IAAI,cAAc;gBAChB,OAAO;YACT;YAEA,YAAY;YACZ,IAAI,YAAY;gBACd,OAAQ;oBACN,KAAK;oBACL,KAAK;wBACH,OAAO,CAAA,GAAA,0HAAA,CAAA,0BAAuB,AAAD;oBAC/B,KAAK;oBACL,KAAK;wBACH,OAAO,CAAA,GAAA,0HAAA,CAAA,yBAAsB,AAAD;oBAC9B,KAAK;oBACL,KAAK;wBACH,OAAO,CAAA,GAAA,0HAAA,CAAA,4BAAyB,AAAD;oBACjC;wBACE,OAAO,CAAA,GAAA,0HAAA,CAAA,0BAAuB,AAAD;gBACjC;YACF,OAAO,IAAI,YAAY;gBACrB,OAAQ;oBACN,KAAK;oBACL,KAAK;wBACH,OAAO,CAAA,GAAA,0HAAA,CAAA,6BAA0B,AAAD;oBAClC,KAAK;oBACL,KAAK;wBACH,OAAO,CAAA,GAAA,0HAAA,CAAA,4BAAyB,AAAD;oBACjC,KAAK;oBACL,KAAK;wBACH,OAAO,CAAA,GAAA,0HAAA,CAAA,+BAA4B,AAAD;oBACpC;wBACE,OAAO,CAAA,GAAA,0HAAA,CAAA,6BAA0B,AAAD;gBACpC;YACF;YAEA,OAAO,CAAA,GAAA,0HAAA,CAAA,0BAAuB,AAAD;QAC/B;wDAAG;QAAC;QAAgB;QAAY;QAAY;KAAa;IAEzD,0BAA0B;IAC1B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD;qCAAc,IAAM;;IAEnE,0CAA0C;IAC1C,sQAAA,CAAA,UAAK,CAAC,SAAS;sCAAC;YACd,MAAM,WAAW;YACjB,yBAAyB;YACzB;8CAAgB,CAAA;oBACd,IAAI,KAAK,SAAS,CAAC,cAAc,KAAK,SAAS,CAAC,YAAY;wBAC1D,OAAO;oBACT;oBACA,OAAO;gBACT;;QACF;qCAAG;QAAC,gBAAgB;QAAc;KAAgB,GAAG,sCAAsC;IAE3F,kCAAkC;IAClC,MAAM,oBAAoB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;2DAAE,CAAC;YACrC,gBAAgB;YAEhB,4BAA4B;YAC5B,IAAI,MAAM,EAAE,EAAE;gBACZ,aAAa,WAAW,CAAC,MAAM,EAAE,EAAE;YACrC;YAEA,qBAAqB;YACrB,IAAI,eAAe;gBACjB,cAAc;YAChB;QACF;0DAAG;QAAC;QAAc,MAAM,EAAE;QAAE;KAAc;IAE1C,eAAe;IACf,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,sSAAC,6RAAA,CAAA,SAAM;oBAAC,MAAM;;;;;;YACvB,KAAK;gBACH,qBAAO,sSAAC,6RAAA,CAAA,SAAM;oBAAC,MAAM;;;;;;YACvB,KAAK;gBACH,qBAAO,sSAAC,iSAAA,CAAA,WAAQ;oBAAC,MAAM;;;;;;YACzB,KAAK;gBACH,qBAAO,sSAAC,yRAAA,CAAA,OAAI;oBAAC,MAAM;;;;;;YACrB,KAAK;gBACH,qBAAO,sSAAC,yRAAA,CAAA,OAAI;oBAAC,MAAM;;;;;;YACrB,KAAK;gBACH,qBAAO,sSAAC,mRAAA,CAAA,IAAC;oBAAC,MAAM;;;;;;YAClB;gBACE,qBAAO,sSAAC,6RAAA,CAAA,SAAM;oBAAC,MAAM;;;;;;QACzB;IACF;IAEA,kBAAkB;IAClB,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,2BAA2B;QAC3B,IAAI,MAAM,KAAK,CAAC,MAAM,GAAG,GAAG;YAC1B,qBACE,sSAAC;gBAAI,WAAU;;kCACb,sSAAC;wBAAI,WAAU;;0CACb,sSAAC,6HAAA,CAAA,QAAK;gCAAC,WAAU;0CAAsB;;;;;;0CACvC,sSAAC,6HAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;;oCAClC,MAAM,KAAK,CAAC,MAAM;oCAAC;;;;;;;;;;;;;oBAIvB,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM;wBACtB,MAAM,aAAa,KAAK,WAAW,CAAC,EAAE;wBAEtC,mBAAmB;wBACnB,MAAM,uBAAuB,CAAC;4BAC5B,MAAM,oBAAoB;gCAAE,GAAG,UAAU;gCAAE,GAAG,OAAO;4BAAC;4BACtD,MAAM,WAA0B;gCAC9B,GAAG,KAAK;gCACR,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,IACzB,MAAM,QACF;wCAAE,GAAG,CAAC;wCAAE,aAAa;4CAAC;yCAAkB;oCAAC,IACzC;4BAER;4BACA,kBAAkB;wBACpB;wBAEA,qBACE,sSAAC,4HAAA,CAAA,OAAI;4BAAa,WAAU;sCAC1B,cAAA,sSAAC,4HAAA,CAAA,cAAW;gCAAC,WAAU;;kDAErB,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAI,WAAU;0DACb,cAAA,sSAAC;oDAAK,WAAU;8DAAuB,KAAK,IAAI;;;;;;;;;;;4CAEjD,KAAK,MAAM,kBACV,sSAAC,6HAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;0DAChC,oBAAoB,KAAK,MAAM;;;;;;;;;;;;oCAMrC,WAAW,IAAI,KAAK,UAAU,CAAC;wCAC9B,MAAM,iBAAiB;wCACvB,qBACE,sSAAC;4CAAI,WAAU;;8DAEb,sSAAC;oDAAI,WAAU;;sEACb,sSAAC;4DAAI,WAAU;;8EACb,sSAAC,6HAAA,CAAA,QAAK;oEAAC,WAAU;8EAAgC;;;;;;8EACjD,sSAAC,6HAAA,CAAA,QAAK;oEACJ,MAAK;oEACL,OAAO,eAAe,KAAK;oEAC3B,UAAU,CAAC,IAAM,qBAAqB;4EAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wEAAC;oEAC9D,WAAU;;;;;;;;;;;;sEAGd,sSAAC;4DAAI,WAAU;;8EACb,sSAAC;oEAAI,WAAU;;sFACb,sSAAC,6HAAA,CAAA,QAAK;4EAAC,WAAU;sFAAgC;;;;;;sFACjD,sSAAC,6HAAA,CAAA,QAAK;4EAAC,SAAQ;4EAAU,WAAU;;gFAChC,eAAe,MAAM;gFAAC;;;;;;;;;;;;;8EAG3B,sSAAC,8HAAA,CAAA,SAAM;oEACL,OAAO;wEAAC,eAAe,MAAM;qEAAC;oEAC9B,eAAe,CAAC,CAAC,MAAM,GAAK,qBAAqB;4EAAE,QAAQ;wEAAM;oEACjE,KAAK;oEACL,KAAK;oEACL,MAAM;oEACN,WAAU;;;;;;;;;;;;;;;;;;8DAMhB,sSAAC;oDAAI,WAAU;;sEACb,sSAAC;4DAAI,WAAU;;8EACb,sSAAC,6HAAA,CAAA,QAAK;oEAAC,WAAU;8EAAgC;;;;;;8EACjD,sSAAC,6HAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAU,WAAU;;wEAChC,KAAK,KAAK,CAAC,eAAe,WAAW,GAAG;wEAAK;;;;;;;;;;;;;sEAGlD,sSAAC,8HAAA,CAAA,SAAM;4DACL,OAAO;gEAAC,eAAe,WAAW;6DAAC;4DACnC,eAAe,CAAC,CAAC,MAAM,GAAK,qBAAqB;oEAAE,aAAa;gEAAM;4DACtE,KAAK;4DACL,KAAK;4DACL,MAAM;4DACN,WAAU;;;;;;;;;;;;gDAKb,eAAe,WAAW,kBACzB,sSAAC;oDAAI,WAAU;;sEACb,sSAAC;4DAAI,WAAU;;8EACb,sSAAC,6HAAA,CAAA,QAAK;oEAAC,WAAU;8EAAgC;;;;;;8EACjD,sSAAC,6HAAA,CAAA,QAAK;oEACJ,MAAK;oEACL,OAAO,eAAe,WAAW;oEACjC,UAAU,CAAC,IAAM,qBAAqB;4EAAE,aAAa,EAAE,MAAM,CAAC,KAAK;wEAAC;oEACpE,WAAU;;;;;;;;;;;;sEAGd,sSAAC;4DAAI,WAAU;;8EACb,sSAAC;oEAAI,WAAU;;sFACb,sSAAC,6HAAA,CAAA,QAAK;4EAAC,WAAU;sFAAgC;;;;;;sFACjD,sSAAC,6HAAA,CAAA,QAAK;4EAAC,SAAQ;4EAAU,WAAU;;gFAChC,eAAe,WAAW,IAAI;gFAAE;;;;;;;;;;;;;8EAGrC,sSAAC,8HAAA,CAAA,SAAM;oEACL,OAAO;wEAAC,eAAe,WAAW,IAAI;qEAAE;oEACxC,eAAe,CAAC,CAAC,MAAM,GAAK,qBAAqB;4EAAE,aAAa;wEAAM;oEACtE,KAAK;oEACL,KAAK;oEACL,MAAM;oEACN,WAAU;;;;;;;;;;;;;;;;;;;;;;;;oCAOxB,CAAC;oCAGA,WAAW,IAAI,KAAK,UAAU,CAAC;wCAC9B,MAAM,iBAAiB;wCACvB,qBACE,sSAAC;4CAAI,WAAU;;8DACb,sSAAC;oDAAI,WAAU;;sEACb,sSAAC;4DAAI,WAAU;;8EACb,sSAAC,6HAAA,CAAA,QAAK;oEAAC,WAAU;8EAAgC;;;;;;8EACjD,sSAAC,6HAAA,CAAA,QAAK;oEACJ,MAAK;oEACL,OAAO,eAAe,KAAK;oEAC3B,UAAU,CAAC,IAAM,qBAAqB;4EAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wEAAC;oEAC9D,WAAU;;;;;;;;;;;;sEAGd,sSAAC;4DAAI,WAAU;;8EACb,sSAAC;oEAAI,WAAU;;sFACb,sSAAC,6HAAA,CAAA,QAAK;4EAAC,WAAU;sFAAgC;;;;;;sFACjD,sSAAC,6HAAA,CAAA,QAAK;4EAAC,SAAQ;4EAAU,WAAU;;gFAChC,eAAe,KAAK;gFAAC;;;;;;;;;;;;;8EAG1B,sSAAC,8HAAA,CAAA,SAAM;oEACL,OAAO;wEAAC,eAAe,KAAK;qEAAC;oEAC7B,eAAe,CAAC,CAAC,MAAM,GAAK,qBAAqB;4EAAE,OAAO;wEAAM;oEAChE,KAAK;oEACL,KAAK;oEACL,MAAM;oEACN,WAAU;;;;;;;;;;;;;;;;;;8DAIhB,sSAAC;oDAAI,WAAU;;sEACb,sSAAC;4DAAI,WAAU;;8EACb,sSAAC,6HAAA,CAAA,QAAK;oEAAC,WAAU;8EAAgC;;;;;;8EACjD,sSAAC,6HAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAU,WAAU;;wEAChC,KAAK,KAAK,CAAC,eAAe,OAAO,GAAG;wEAAK;;;;;;;;;;;;;sEAG9C,sSAAC,8HAAA,CAAA,SAAM;4DACL,OAAO;gEAAC,eAAe,OAAO;6DAAC;4DAC/B,eAAe,CAAC,CAAC,MAAM,GAAK,qBAAqB;oEAAE,SAAS;gEAAM;4DAClE,KAAK;4DACL,KAAK;4DACL,MAAM;4DACN,WAAU;;;;;;;;;;;;;;;;;;oCAKpB,CAAC;oCAGA,WAAW,IAAI,KAAK,UAAU,CAAC;wCAC9B,MAAM,iBAAiB;wCACvB,qBACE,sSAAC;4CAAI,WAAU;;8DACb,sSAAC;oDAAI,WAAU;;sEACb,sSAAC;4DAAI,WAAU;;8EACb,sSAAC,6HAAA,CAAA,QAAK;oEAAC,WAAU;8EAAgC;;;;;;8EACjD,sSAAC,6HAAA,CAAA,QAAK;oEACJ,MAAK;oEACL,OAAO,eAAe,KAAK;oEAC3B,UAAU,CAAC,IAAM,qBAAqB;4EAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wEAAC;oEAC9D,WAAU;;;;;;;;;;;;sEAGd,sSAAC;4DAAI,WAAU;;8EACb,sSAAC;oEAAI,WAAU;;sFACb,sSAAC,6HAAA,CAAA,QAAK;4EAAC,WAAU;sFAAgC;;;;;;sFACjD,sSAAC,6HAAA,CAAA,QAAK;4EAAC,SAAQ;4EAAU,WAAU;;gFAChC,KAAK,KAAK,CAAC,eAAe,WAAW,GAAG;gFAAK;;;;;;;;;;;;;8EAGlD,sSAAC,8HAAA,CAAA,SAAM;oEACL,OAAO;wEAAC,eAAe,WAAW;qEAAC;oEACnC,eAAe,CAAC,CAAC,MAAM,GAAK,qBAAqB;4EAAE,aAAa;wEAAM;oEACtE,KAAK;oEACL,KAAK;oEACL,MAAM;oEACN,WAAU;;;;;;;;;;;;;;;;;;gDAMf,eAAe,YAAY,kBAC1B,sSAAC;oDAAI,WAAU;;sEACb,sSAAC;4DAAI,WAAU;;8EACb,sSAAC,6HAAA,CAAA,QAAK;oEAAC,WAAU;8EAAgC;;;;;;8EACjD,sSAAC,6HAAA,CAAA,QAAK;oEACJ,MAAK;oEACL,OAAO,eAAe,YAAY;oEAClC,UAAU,CAAC,IAAM,qBAAqB;4EAAE,cAAc,EAAE,MAAM,CAAC,KAAK;wEAAC;oEACrE,WAAU;;;;;;;;;;;;sEAGd,sSAAC;4DAAI,WAAU;;8EACb,sSAAC;oEAAI,WAAU;;sFACb,sSAAC,6HAAA,CAAA,QAAK;4EAAC,WAAU;sFAAgC;;;;;;sFACjD,sSAAC,6HAAA,CAAA,QAAK;4EAAC,SAAQ;4EAAU,WAAU;;gFAChC,eAAe,YAAY,IAAI;gFAAE;;;;;;;;;;;;;8EAGtC,sSAAC,8HAAA,CAAA,SAAM;oEACL,OAAO;wEAAC,eAAe,YAAY,IAAI;qEAAE;oEACzC,eAAe,CAAC,CAAC,MAAM,GAAK,qBAAqB;4EAAE,cAAc;wEAAM;oEACvE,KAAK;oEACL,KAAK;oEACL,MAAM;oEACN,WAAU;;;;;;;;;;;;;;;;;;;;;;;;oCAOxB,CAAC;;;;;;;2BAzNM;;;;;oBA6Nf;;;;;;;QAGN;QAEA,sBAAsB;QACtB,MAAM,OAAO,MAAM,KAAK,CAAC,EAAE;QAC3B,MAAM,aAAa,KAAK,WAAW,CAAC,EAAE;QAEtC,IAAI,WAAW,IAAI,KAAK,QAAQ;YAC9B,MAAM,iBAAiB;YACvB,qBACE,sSAAC;gBAAI,WAAU;;kCAEb,sSAAC,4HAAA,CAAA,OAAI;kCACH,cAAA,sSAAC,4HAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,sSAAC;gCAAI,WAAU;;kDACb,sSAAC;wCAAI,WAAU;;0DACb,sSAAC,6HAAA,CAAA,QAAK;gDAAC,WAAU;0DAAsB;;;;;;0DACvC,sSAAC;gDAAI,WAAU;;oDACZ,cAAc,eAAe,aAAa;kEAC3C,sSAAC;wDAAK,WAAU;kEACb,eAAe,aAAa;;;;;;;;;;;;;;;;;;kDAInC,sSAAC,8HAAA,CAAA,SAAM;wCACL,OAAO,eAAe,aAAa;wCACnC,eAAe,CAAC;4CACd,MAAM,WAA0B;gDAC9B,GAAG,KAAK;gDACR,OAAO;oDAAC;wDACN,GAAG,IAAI;wDACP,aAAa;4DAAC;gEACZ,GAAG,cAAc;gEACjB,eAAe;4DACjB;yDAAE;oDACJ;iDAAE;4CACJ;4CACA,kBAAkB;wCACpB;;0DAEA,sSAAC,8HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,sSAAC,8HAAA,CAAA,cAAW;;;;;;;;;;0DAEd,sSAAC,8HAAA,CAAA,gBAAa;;kEACZ,sSAAC,8HAAA,CAAA,aAAU;wDAAC,OAAM;kEAChB,cAAA,sSAAC;4DAAI,WAAU;;8EACb,sSAAC,6RAAA,CAAA,SAAM;oEAAC,MAAM;;;;;;8EACd,sSAAC;8EAAK;;;;;;;;;;;;;;;;;kEAGV,sSAAC,8HAAA,CAAA,aAAU;wDAAC,OAAM;kEAChB,cAAA,sSAAC;4DAAI,WAAU;;8EACb,sSAAC,6RAAA,CAAA,SAAM;oEAAC,MAAM;;;;;;8EACd,sSAAC;8EAAK;;;;;;;;;;;;;;;;;kEAGV,sSAAC,8HAAA,CAAA,aAAU;wDAAC,OAAM;kEAChB,cAAA,sSAAC;4DAAI,WAAU;;8EACb,sSAAC,iSAAA,CAAA,WAAQ;oEAAC,MAAM;;;;;;8EAChB,sSAAC;8EAAK;;;;;;;;;;;;;;;;;kEAGV,sSAAC,8HAAA,CAAA,aAAU;wDAAC,OAAM;kEAChB,cAAA,sSAAC;4DAAI,WAAU;;8EACb,sSAAC,yRAAA,CAAA,OAAI;oEAAC,MAAM;;;;;;8EACZ,sSAAC;8EAAK;;;;;;;;;;;;;;;;;kEAGV,sSAAC,8HAAA,CAAA,aAAU;wDAAC,OAAM;kEAChB,cAAA,sSAAC;4DAAI,WAAU;;8EACb,sSAAC,yRAAA,CAAA,OAAI;oEAAC,MAAM;;;;;;8EACZ,sSAAC;8EAAK;;;;;;;;;;;;;;;;;kEAGV,sSAAC,8HAAA,CAAA,aAAU;wDAAC,OAAM;kEAChB,cAAA,sSAAC;4DAAI,WAAU;;8EACb,sSAAC,mRAAA,CAAA,IAAC;oEAAC,MAAM;;;;;;8EACT,sSAAC;8EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUpB,sSAAC,4HAAA,CAAA,OAAI;kCACH,cAAA,sSAAC,4HAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,sSAAC;gCAAI,WAAU;;kDACb,sSAAC;wCAAI,WAAU;;0DACb,sSAAC,6HAAA,CAAA,QAAK;gDAAC,WAAU;0DAAsB;;;;;;0DACvC,sSAAC,6HAAA,CAAA,QAAK;gDAAC,SAAQ;;oDAAa,eAAe,MAAM;oDAAC;;;;;;;;;;;;;kDAEpD,sSAAC,8HAAA,CAAA,SAAM;wCACL,OAAO;4CAAC,eAAe,MAAM;yCAAC;wCAC9B,eAAe,CAAC,CAAC,MAAM;4CACrB,MAAM,WAA0B;gDAC9B,GAAG,KAAK;gDACR,OAAO;oDAAC;wDACN,GAAG,IAAI;wDACP,aAAa;4DAAC;gEACZ,GAAG,cAAc;gEACjB,QAAQ;4DACV;yDAAE;oDACJ;iDAAE;4CACJ;4CACA,kBAAkB;wCACpB;wCACA,KAAK;wCACL,KAAK;wCACL,MAAM;wCACN,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAOlB,sSAAC,4HAAA,CAAA,OAAI;kCACH,cAAA,sSAAC,4HAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,sSAAC;gCAAI,WAAU;;kDACb,sSAAC,6HAAA,CAAA,QAAK;wCAAC,WAAU;kDAAsB;;;;;;kDACvC,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAI,WAAU;;kEACb,sSAAC,6HAAA,CAAA,QAAK;wDAAC,WAAU;kEAAgC;;;;;;kEACjD,sSAAC;wDAAI,WAAU;kEACb,cAAA,sSAAC,6HAAA,CAAA,QAAK;4DACJ,MAAK;4DACL,OAAO,eAAe,KAAK;4DAC3B,UAAU,CAAC;gEACT,MAAM,WAA0B;oEAC9B,GAAG,KAAK;oEACR,OAAO;wEAAC;4EACN,GAAG,IAAI;4EACP,aAAa;gFAAC;oFACZ,GAAG,cAAc;oFACjB,OAAO,EAAE,MAAM,CAAC,KAAK;gFACvB;6EAAE;wEACJ;qEAAE;gEACJ;gEACA,kBAAkB;4DACpB;4DACA,WAAU;;;;;;;;;;;;;;;;;0DAIhB,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;wDAAI,WAAU;;0EACb,sSAAC,6HAAA,CAAA,QAAK;gEAAC,WAAU;0EAAgC;;;;;;0EACjD,sSAAC,6HAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAU,WAAU;;oEAChC,KAAK,KAAK,CAAC,eAAe,WAAW,GAAG;oEAAK;;;;;;;;;;;;;kEAGlD,sSAAC,8HAAA,CAAA,SAAM;wDACL,OAAO;4DAAC,eAAe,WAAW;yDAAC;wDACnC,eAAe,CAAC,CAAC,MAAM;4DACrB,MAAM,WAA0B;gEAC9B,GAAG,KAAK;gEACR,OAAO;oEAAC;wEACN,GAAG,IAAI;wEACP,aAAa;4EAAC;gFACZ,GAAG,cAAc;gFACjB,aAAa;4EACf;yEAAE;oEACJ;iEAAE;4DACJ;4DACA,kBAAkB;wDACpB;wDACA,KAAK;wDACL,KAAK;wDACL,MAAM;wDACN,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBASrB,eAAe,WAAW,kBACzB,sSAAC,4HAAA,CAAA,OAAI;kCACH,cAAA,sSAAC,4HAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,sSAAC;gCAAI,WAAU;;kDACb,sSAAC,6HAAA,CAAA,QAAK;wCAAC,WAAU;kDAAsB;;;;;;kDACvC,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAI,WAAU;;kEACb,sSAAC,6HAAA,CAAA,QAAK;wDAAC,WAAU;kEAAgC;;;;;;kEACjD,sSAAC,6HAAA,CAAA,QAAK;wDACJ,MAAK;wDACL,OAAO,eAAe,WAAW;wDACjC,UAAU,CAAC;4DACT,MAAM,WAA0B;gEAC9B,GAAG,KAAK;gEACR,OAAO;oEAAC;wEACN,GAAG,IAAI;wEACP,aAAa;4EAAC;gFACZ,GAAG,cAAc;gFACjB,aAAa,EAAE,MAAM,CAAC,KAAK;4EAC7B;yEAAE;oEACJ;iEAAE;4DACJ;4DACA,kBAAkB;wDACpB;wDACA,WAAU;;;;;;;;;;;;0DAGd,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;wDAAI,WAAU;;0EACb,sSAAC,6HAAA,CAAA,QAAK;gEAAC,WAAU;0EAAgC;;;;;;0EACjD,sSAAC,6HAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAU,WAAU;;oEAChC,eAAe,WAAW,IAAI;oEAAE;;;;;;;;;;;;;kEAGrC,sSAAC,8HAAA,CAAA,SAAM;wDACL,OAAO;4DAAC,eAAe,WAAW,IAAI;yDAAE;wDACxC,eAAe,CAAC,CAAC,MAAM;4DACrB,MAAM,WAA0B;gEAC9B,GAAG,KAAK;gEACR,OAAO;oEAAC;wEACN,GAAG,IAAI;wEACP,aAAa;4EAAC;gFACZ,GAAG,cAAc;gFACjB,aAAa;4EACf;yEAAE;oEACJ;iEAAE;4DACJ;4DACA,kBAAkB;wDACpB;wDACA,KAAK;wDACL,KAAK;wDACL,MAAM;wDACN,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAU9B;QAEA,IAAI,WAAW,IAAI,KAAK,QAAQ;YAC9B,MAAM,iBAAiB;YACvB,qBACE,sSAAC;gBAAI,WAAU;;kCACb,sSAAC,4HAAA,CAAA,OAAI;kCACH,cAAA,sSAAC,4HAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,sSAAC;gCAAI,WAAU;;kDACb,sSAAC,6HAAA,CAAA,QAAK;wCAAC,WAAU;kDAAsB;;;;;;kDACvC,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAI,WAAU;;kEACb,sSAAC,6HAAA,CAAA,QAAK;wDAAC,WAAU;kEAAgC;;;;;;kEACjD,sSAAC,6HAAA,CAAA,QAAK;wDACJ,MAAK;wDACL,OAAO,eAAe,KAAK;wDAC3B,UAAU,CAAC;4DACT,MAAM,WAA0B;gEAC9B,GAAG,KAAK;gEACR,OAAO;oEAAC;wEACN,GAAG,IAAI;wEACP,aAAa;4EAAC;gFACZ,GAAG,cAAc;gFACjB,OAAO,EAAE,MAAM,CAAC,KAAK;4EACvB;yEAAE;oEACJ;iEAAE;4DACJ;4DACA,kBAAkB;wDACpB;wDACA,WAAU;;;;;;;;;;;;0DAGd,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;wDAAI,WAAU;;0EACb,sSAAC,6HAAA,CAAA,QAAK;gEAAC,WAAU;0EAAgC;;;;;;0EACjD,sSAAC,6HAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAU,WAAU;;oEAChC,KAAK,KAAK,CAAC,eAAe,OAAO,GAAG;oEAAK;;;;;;;;;;;;;kEAG9C,sSAAC,8HAAA,CAAA,SAAM;wDACL,OAAO;4DAAC,eAAe,OAAO;yDAAC;wDAC/B,eAAe,CAAC,CAAC,MAAM;4DACrB,MAAM,WAA0B;gEAC9B,GAAG,KAAK;gEACR,OAAO;oEAAC;wEACN,GAAG,IAAI;wEACP,aAAa;4EAAC;gFACZ,GAAG,cAAc;gFACjB,SAAS;4EACX;yEAAE;oEACJ;iEAAE;4DACJ;4DACA,kBAAkB;wDACpB;wDACA,KAAK;wDACL,KAAK;wDACL,MAAM;wDACN,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQtB,sSAAC,4HAAA,CAAA,OAAI;kCACH,cAAA,sSAAC,4HAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,sSAAC;gCAAI,WAAU;;kDACb,sSAAC;wCAAI,WAAU;;0DACb,sSAAC,6HAAA,CAAA,QAAK;gDAAC,WAAU;0DAAsB;;;;;;0DACvC,sSAAC,6HAAA,CAAA,QAAK;gDAAC,SAAQ;;oDAAa,eAAe,KAAK;oDAAC;;;;;;;;;;;;;kDAEnD,sSAAC,8HAAA,CAAA,SAAM;wCACL,OAAO;4CAAC,eAAe,KAAK;yCAAC;wCAC7B,eAAe,CAAC,CAAC,MAAM;4CACrB,MAAM,WAA0B;gDAC9B,GAAG,KAAK;gDACR,OAAO;oDAAC;wDACN,GAAG,IAAI;wDACP,aAAa;4DAAC;gEACZ,GAAG,cAAc;gEACjB,OAAO;4DACT;yDAAE;oDACJ;iDAAE;4CACJ;4CACA,kBAAkB;wCACpB;wCACA,KAAK;wCACL,KAAK;wCACL,MAAM;wCACN,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAOxB;QAEA,IAAI,WAAW,IAAI,KAAK,QAAQ;YAC9B,MAAM,iBAAiB;YACvB,qBACE,sSAAC;gBAAI,WAAU;;kCACb,sSAAC,4HAAA,CAAA,OAAI;kCACH,cAAA,sSAAC,4HAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,sSAAC;gCAAI,WAAU;;kDACb,sSAAC,6HAAA,CAAA,QAAK;wCAAC,WAAU;kDAAsB;;;;;;kDACvC,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAI,WAAU;;kEACb,sSAAC,6HAAA,CAAA,QAAK;wDAAC,WAAU;kEAAgC;;;;;;kEACjD,sSAAC,6HAAA,CAAA,QAAK;wDACJ,MAAK;wDACL,OAAO,eAAe,KAAK;wDAC3B,UAAU,CAAC;4DACT,MAAM,WAA0B;gEAC9B,GAAG,KAAK;gEACR,OAAO;oEAAC;wEACN,GAAG,IAAI;wEACP,aAAa;4EAAC;gFACZ,GAAG,cAAc;gFACjB,OAAO,EAAE,MAAM,CAAC,KAAK;4EACvB;yEAAE;oEACJ;iEAAE;4DACJ;4DACA,kBAAkB;wDACpB;wDACA,WAAU;;;;;;;;;;;;0DAGd,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;wDAAI,WAAU;;0EACb,sSAAC,6HAAA,CAAA,QAAK;gEAAC,WAAU;0EAAgC;;;;;;0EACjD,sSAAC,6HAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAU,WAAU;;oEAChC,KAAK,KAAK,CAAC,eAAe,WAAW,GAAG;oEAAK;;;;;;;;;;;;;kEAGlD,sSAAC,8HAAA,CAAA,SAAM;wDACL,OAAO;4DAAC,eAAe,WAAW;yDAAC;wDACnC,eAAe,CAAC,CAAC,MAAM;4DACrB,MAAM,WAA0B;gEAC9B,GAAG,KAAK;gEACR,OAAO;oEAAC;wEACN,GAAG,IAAI;wEACP,aAAa;4EAAC;gFACZ,GAAG,cAAc;gFACjB,aAAa;4EACf;yEAAE;oEACJ;iEAAE;4DACJ;4DACA,kBAAkB;wDACpB;wDACA,KAAK;wDACL,KAAK;wDACL,MAAM;wDACN,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAQrB,eAAe,YAAY,kBAC1B,sSAAC,4HAAA,CAAA,OAAI;kCACH,cAAA,sSAAC,4HAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,sSAAC;gCAAI,WAAU;;kDACb,sSAAC,6HAAA,CAAA,QAAK;wCAAC,WAAU;kDAAsB;;;;;;kDACvC,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAI,WAAU;;kEACb,sSAAC,6HAAA,CAAA,QAAK;wDAAC,WAAU;kEAAgC;;;;;;kEACjD,sSAAC,6HAAA,CAAA,QAAK;wDACJ,MAAK;wDACL,OAAO,eAAe,YAAY;wDAClC,UAAU,CAAC;4DACT,MAAM,WAA0B;gEAC9B,GAAG,KAAK;gEACR,OAAO;oEAAC;wEACN,GAAG,IAAI;wEACP,aAAa;4EAAC;gFACZ,GAAG,cAAc;gFACjB,cAAc,EAAE,MAAM,CAAC,KAAK;4EAC9B;yEAAE;oEACJ;iEAAE;4DACJ;4DACA,kBAAkB;wDACpB;wDACA,WAAU;;;;;;;;;;;;0DAGd,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;wDAAI,WAAU;;0EACb,sSAAC,6HAAA,CAAA,QAAK;gEAAC,WAAU;0EAAgC;;;;;;0EACjD,sSAAC,6HAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAU,WAAU;;oEAChC,eAAe,YAAY,IAAI;oEAAE;;;;;;;;;;;;;kEAGtC,sSAAC,8HAAA,CAAA,SAAM;wDACL,OAAO;4DAAC,eAAe,YAAY,IAAI;yDAAE;wDACzC,eAAe,CAAC,CAAC,MAAM;4DACrB,MAAM,WAA0B;gEAC9B,GAAG,KAAK;gEACR,OAAO;oEAAC;wEACN,GAAG,IAAI;wEACP,aAAa;4EAAC;gFACZ,GAAG,cAAc;gFACjB,cAAc;4EAChB;yEAAE;oEACJ;iEAAE;4DACJ;4DACA,kBAAkB;wDACpB;wDACA,KAAK;wDACL,KAAK;wDACL,MAAM;wDACN,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAU9B;QAEA,OAAO;IACT;IAEA,qBACE,sSAAC,+HAAA,CAAA,UAAO;QAAC,MAAM;QAAQ,cAAc;;0BACnC,sSAAC,+HAAA,CAAA,iBAAc;gBAAC,OAAO;0BACrB,cAAA,sSAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;8BAEL,cAAA,sSAAC,+RAAA,CAAA,UAAO;wBAAC,MAAM;;;;;;;;;;;;;;;;0BAGnB,sSAAC,+HAAA,CAAA,iBAAc;gBAAC,WAAU;gBAAW,MAAK;gBAAO,OAAM;0BACrD,cAAA,sSAAC;oBAAI,WAAU;;sCAEb,sSAAC;4BAAI,WAAU;;8CACb,sSAAC;oCAAI,WAAU;;sDACb,sSAAC;4CAAG,WAAU;sDAA0B;;;;;;sDACxC,sSAAC;4CAAI,WAAU;;8DACb,sSAAC,6HAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAU,WAAU;8DAChC,aAAa,QAAQ,aAAa,QAAQ;;;;;;8DAE7C,sSAAC,6HAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAY,WAAU;8DAClC,oBAAoB;;;;;;;;;;;;;;;;;;8CAK3B,sSAAC;oCAAI,WAAU;8CACZ,AAAC,MAAc,IAAI,IAAI,MAAM,EAAE;;;;;;gCAIjC,4BACC,sSAAC;oCAAI,WAAU;;wCAEX,WAAW,gBAAgB,aAAa,KAAK,CAAC,MAAM,KAAK,mBACzD,sSAAC;4CAAI,WAAU;sDACb,cAAA,sSAAC,8HAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS;0DACV;;;;;;;;;;;wCAOH,WAAW,gBAAgB,aAAa,KAAK,CAAC,MAAM,GAAG,mBACvD,sSAAC;4CAAI,WAAU;;8DACb,sSAAC,8HAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS;;sEAET,sSAAC,6RAAA,CAAA,SAAM;4DAAC,MAAM;4DAAI,WAAU;;;;;;wDAAS;;;;;;;8DAGvC,sSAAC,8HAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS;8DACV;;;;;;;;;;;;;;;;;;8CAQT,sSAAC,iIAAA,CAAA,YAAS;;;;;;;;;;;sCAIZ,sSAAC;4BAAI,WAAU;;gCACZ,cAAc,WAAW,8BACxB,sSAAC;oCAAI,WAAU;8CACZ,qBAAqB;;;;;;gCAIzB,cAAc,CAAC,CAAC,WAAW,YAAY,mBACtC,sSAAC,4HAAA,CAAA,OAAI;8CACH,cAAA,sSAAC,4HAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,sSAAC;4CAAI,WAAU;;8DACb,sSAAC;oDAAI,WAAU;8DAAsB;;;;;;8DACrC,sSAAC;oDAAI,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;;;;;gCAQtD,CAAC,cAAc,CAAC,4BACf,sSAAC,4HAAA,CAAA,OAAI;8CACH,cAAA,sSAAC,4HAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,sSAAC;4CAAI,WAAU;;8DACb,sSAAC;oDAAI,WAAU;8DAAsB;;;;;;8DACrC,sSAAC;oDAAI,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYnE;GAl+BgB;;QAEO,mIAAA,CAAA,kBAAe;;;KAFtB", "debugId": null}}, {"offset": {"line": 2887, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/map/toc.tsx"], "sourcesContent": ["\"use client\";\r\n\r\n\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { <PERSON>rollArea } from \"@/components/ui/scroll-area\";\r\nimport { Eye, EyeOff, X, GripVertical, Layers, Palette } from \"lucide-react\";\r\nimport { useLayerManager } from \"@/hooks/use-layer-configs\";\r\nimport type { LayerProps } from \"@geon-map/odf\";\r\nimport { LayerStyleEditor } from \"./layer-style-editor\";\r\nimport { DragDropContext, Droppable, Draggable, DropResult } from \"@hello-pangea/dnd\";\r\n\r\ninterface TOCProps {\r\n  map?: any; // ODF Map instance, replace 'any' with actual type if known\r\n  layers: LayerProps[];\r\n}\r\n\r\nexport function TOC({ map, layers }: TOCProps) {\r\n  const layerManager = useLayerManager();\r\n\r\n  // zIndex 기준으로 정렬 (높은 zIndex가 위에 = DnD에서 아래쪽 인덱스)\r\n  // 즉, zIndex가 높을수록 DnD 리스트에서 아래쪽에 위치\r\n  const sortedLayers = [...layers].sort((a, b) => {\r\n    const aZIndex = (a as any).zIndex || 0;\r\n    const bZIndex = (b as any).zIndex || 0;\r\n    return bZIndex - aZIndex; // 높은 zIndex가 먼저 (DnD 리스트 상단)\r\n  });\r\n\r\n  const toggleLayerVisibility = (layerId: string) => {\r\n    layerManager.toggleVisibility(layerId);\r\n  };\r\n\r\n  const removeLayer = (layerId: string) => {\r\n    layerManager.removeLayer(layerId);\r\n  };\r\n\r\n  const updateLayerStyle = (layerId: string, style: any) => {\r\n    // 최적화된 스타일 업데이트 사용\r\n    layerManager.updateStyle(layerId, style);\r\n  };\r\n\r\n  // 드래그 앤 드롭 핸들러\r\n  const handleDragEnd = (result: DropResult) => {\r\n    if (!result.destination) return;\r\n\r\n    const sourceIndex = result.source.index;\r\n    const destinationIndex = result.destination.index;\r\n\r\n    if (sourceIndex === destinationIndex) return;\r\n\r\n    // sortedLayers를 기준으로 순서 변경\r\n    const newOrder = Array.from(sortedLayers);\r\n    const [reorderedItem] = newOrder.splice(sourceIndex, 1);\r\n    newOrder.splice(destinationIndex, 0, reorderedItem);\r\n\r\n    // 새로운 순서의 레이어 ID 배열 생성\r\n    const layerIds = newOrder.map(layer => layer.id).filter(Boolean) as string[];\r\n    layerManager.reorderLayers(layerIds);\r\n  };\r\n\r\n  if (layers.length === 0) {\r\n    return (\r\n      <ScrollArea className=\"w-full rounded-md border p-4\">\r\n        <div className=\"flex flex-col items-center justify-center py-8 text-center\">\r\n          <Layers className=\"h-12 w-12 text-muted-foreground/50 mb-4\" />\r\n          <h3 className=\"font-medium mb-2\">레이어가 없습니다</h3>\r\n          <p className=\"text-sm text-muted-foreground mb-4\">\r\n            새로운 레이어를 추가하여 지도를 구성해보세요\r\n          </p>\r\n          {/* <Button variant=\"outline\" size=\"sm\">\r\n            레이어 추가\r\n          </Button> */}\r\n        </div>\r\n      </ScrollArea>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <ScrollArea className=\"w-full rounded-md border p-4\">\r\n      <div className=\"space-y-4\">\r\n        {/* 스타일 예제 섹션 */}\r\n        {/* <LayerStyleExamples /> */}\r\n\r\n        <DragDropContext onDragEnd={handleDragEnd}>\r\n          <Droppable droppableId=\"layers\">\r\n            {(provided, snapshot) => (\r\n              <div\r\n                {...provided.droppableProps}\r\n                ref={provided.innerRef}\r\n                className={`space-y-2 transition-colors duration-200 ${\r\n                  snapshot.isDraggingOver ? 'bg-blue-50/50 rounded-lg p-2' : ''\r\n                }`}\r\n              >\r\n                {sortedLayers.map((layer, index) => {\r\n                  // 안정적인 ID 생성 - layer.id가 없거나 변경될 수 있는 경우 대비\r\n                  const stableId = layer.id || `layer-${index}`;\r\n                  const layerZIndex = (layer as any).zIndex || 0;\r\n                  return (\r\n                    <Draggable\r\n                      key={stableId}\r\n                      draggableId={stableId}\r\n                      index={index}\r\n                      isDragDisabled={false}\r\n                    >\r\n                    {(provided, snapshot) => (\r\n                      <div\r\n                        ref={provided.innerRef}\r\n                        {...provided.draggableProps}\r\n                        className={`rounded-lg bg-card border transition-all duration-200 ${\r\n                          snapshot.isDragging\r\n                            ? 'shadow-lg scale-105 rotate-2 bg-white border-blue-300'\r\n                            : 'shadow-sm hover:shadow-md'\r\n                        } ${\r\n                          !layer.visible ? 'opacity-60' : ''\r\n                        }`}\r\n                      >\r\n                        <div className=\"p-4\">\r\n                          <div className=\"flex items-center justify-between\">\r\n                            <div className=\"flex items-center space-x-3\">\r\n                              <div\r\n                                {...provided.dragHandleProps}\r\n                                className=\"cursor-grab active:cursor-grabbing p-1 rounded hover:bg-muted/50 transition-colors\"\r\n                              >\r\n                                <GripVertical className=\"h-4 w-4 text-muted-foreground\" />\r\n                              </div>\r\n                              <div className=\"flex flex-col\">\r\n                                <span className=\"font-medium text-sm\">\r\n                                  {(layer as any).name || layer.id || `Layer ${index + 1}`}\r\n                                </span>\r\n                                <span className=\"text-xs text-muted-foreground\">\r\n                                  {(layer as any).geometryType || 'unknown'} • {layer.visible ? '표시됨' : '숨김'} • z:{layerZIndex}\r\n                                </span>\r\n                              </div>\r\n                            </div>\r\n\r\n                            <div className=\"flex items-center space-x-1\">\r\n                              <Button\r\n                                variant=\"ghost\"\r\n                                size=\"icon\"\r\n                                className=\"h-8 w-8\"\r\n                                onClick={() => layer.id && toggleLayerVisibility(layer.id)}\r\n                                title={layer.visible ? \"레이어 숨기기\" : \"레이어 보이기\"}\r\n                              >\r\n                                {layer.visible ? <Eye size={14} /> : <EyeOff size={14} />}\r\n                              </Button>\r\n\r\n                              <LayerStyleEditor\r\n                                layer={layer}\r\n                                onStyleChange={(style) => layer.id && updateLayerStyle(layer.id, style)}\r\n                              />\r\n\r\n                              <Button\r\n                                variant=\"ghost\"\r\n                                size=\"icon\"\r\n                                className=\"h-8 w-8 text-destructive hover:text-destructive hover:bg-destructive/10\"\r\n                                onClick={() => layer.id && removeLayer(layer.id)}\r\n                                title=\"레이어 삭제\"\r\n                              >\r\n                                <X size={14} />\r\n                              </Button>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n                  </Draggable>\r\n                  );\r\n                })}\r\n                {provided.placeholder}\r\n              </div>\r\n            )}\r\n          </Droppable>\r\n        </DragDropContext>\r\n      </div>\r\n    </ScrollArea>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;;;AATA;;;;;;;AAgBO,SAAS,IAAI,EAAE,GAAG,EAAE,MAAM,EAAY;;IAC3C,MAAM,eAAe,CAAA,GAAA,mIAAA,CAAA,kBAAe,AAAD;IAEnC,iDAAiD;IACjD,oCAAoC;IACpC,MAAM,eAAe;WAAI;KAAO,CAAC,IAAI,CAAC,CAAC,GAAG;QACxC,MAAM,UAAU,AAAC,EAAU,MAAM,IAAI;QACrC,MAAM,UAAU,AAAC,EAAU,MAAM,IAAI;QACrC,OAAO,UAAU,SAAS,6BAA6B;IACzD;IAEA,MAAM,wBAAwB,CAAC;QAC7B,aAAa,gBAAgB,CAAC;IAChC;IAEA,MAAM,cAAc,CAAC;QACnB,aAAa,WAAW,CAAC;IAC3B;IAEA,MAAM,mBAAmB,CAAC,SAAiB;QACzC,mBAAmB;QACnB,aAAa,WAAW,CAAC,SAAS;IACpC;IAEA,eAAe;IACf,MAAM,gBAAgB,CAAC;QACrB,IAAI,CAAC,OAAO,WAAW,EAAE;QAEzB,MAAM,cAAc,OAAO,MAAM,CAAC,KAAK;QACvC,MAAM,mBAAmB,OAAO,WAAW,CAAC,KAAK;QAEjD,IAAI,gBAAgB,kBAAkB;QAEtC,2BAA2B;QAC3B,MAAM,WAAW,MAAM,IAAI,CAAC;QAC5B,MAAM,CAAC,cAAc,GAAG,SAAS,MAAM,CAAC,aAAa;QACrD,SAAS,MAAM,CAAC,kBAAkB,GAAG;QAErC,uBAAuB;QACvB,MAAM,WAAW,SAAS,GAAG,CAAC,CAAA,QAAS,MAAM,EAAE,EAAE,MAAM,CAAC;QACxD,aAAa,aAAa,CAAC;IAC7B;IAEA,IAAI,OAAO,MAAM,KAAK,GAAG;QACvB,qBACE,sSAAC,sIAAA,CAAA,aAAU;YAAC,WAAU;sBACpB,cAAA,sSAAC;gBAAI,WAAU;;kCACb,sSAAC,6RAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,sSAAC;wBAAG,WAAU;kCAAmB;;;;;;kCACjC,sSAAC;wBAAE,WAAU;kCAAqC;;;;;;;;;;;;;;;;;IAS1D;IAEA,qBACE,sSAAC,sIAAA,CAAA,aAAU;QAAC,WAAU;kBACpB,cAAA,sSAAC;YAAI,WAAU;sBAIb,cAAA,sSAAC,8QAAA,CAAA,kBAAe;gBAAC,WAAW;0BAC1B,cAAA,sSAAC,8QAAA,CAAA,YAAS;oBAAC,aAAY;8BACpB,CAAC,UAAU,yBACV,sSAAC;4BACE,GAAG,SAAS,cAAc;4BAC3B,KAAK,SAAS,QAAQ;4BACtB,WAAW,CAAC,yCAAyC,EACnD,SAAS,cAAc,GAAG,iCAAiC,IAC3D;;gCAED,aAAa,GAAG,CAAC,CAAC,OAAO;oCACxB,4CAA4C;oCAC5C,MAAM,WAAW,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO;oCAC7C,MAAM,cAAc,AAAC,MAAc,MAAM,IAAI;oCAC7C,qBACE,sSAAC,8QAAA,CAAA,YAAS;wCAER,aAAa;wCACb,OAAO;wCACP,gBAAgB;kDAEjB,CAAC,UAAU,yBACV,sSAAC;gDACC,KAAK,SAAS,QAAQ;gDACrB,GAAG,SAAS,cAAc;gDAC3B,WAAW,CAAC,sDAAsD,EAChE,SAAS,UAAU,GACf,0DACA,4BACL,CAAC,EACA,CAAC,MAAM,OAAO,GAAG,eAAe,IAChC;0DAEF,cAAA,sSAAC;oDAAI,WAAU;8DACb,cAAA,sSAAC;wDAAI,WAAU;;0EACb,sSAAC;gEAAI,WAAU;;kFACb,sSAAC;wEACE,GAAG,SAAS,eAAe;wEAC5B,WAAU;kFAEV,cAAA,sSAAC,6SAAA,CAAA,eAAY;4EAAC,WAAU;;;;;;;;;;;kFAE1B,sSAAC;wEAAI,WAAU;;0FACb,sSAAC;gFAAK,WAAU;0FACb,AAAC,MAAc,IAAI,IAAI,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,QAAQ,GAAG;;;;;;0FAE1D,sSAAC;gFAAK,WAAU;;oFACZ,MAAc,YAAY,IAAI;oFAAU;oFAAI,MAAM,OAAO,GAAG,QAAQ;oFAAK;oFAAM;;;;;;;;;;;;;;;;;;;0EAKvF,sSAAC;gEAAI,WAAU;;kFACb,sSAAC,8HAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,WAAU;wEACV,SAAS,IAAM,MAAM,EAAE,IAAI,sBAAsB,MAAM,EAAE;wEACzD,OAAO,MAAM,OAAO,GAAG,YAAY;kFAElC,MAAM,OAAO,iBAAG,sSAAC,uRAAA,CAAA,MAAG;4EAAC,MAAM;;;;;iGAAS,sSAAC,iSAAA,CAAA,SAAM;4EAAC,MAAM;;;;;;;;;;;kFAGrD,sSAAC,iJAAA,CAAA,mBAAgB;wEACf,OAAO;wEACP,eAAe,CAAC,QAAU,MAAM,EAAE,IAAI,iBAAiB,MAAM,EAAE,EAAE;;;;;;kFAGnE,sSAAC,8HAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,WAAU;wEACV,SAAS,IAAM,MAAM,EAAE,IAAI,YAAY,MAAM,EAAE;wEAC/C,OAAM;kFAEN,cAAA,sSAAC,mRAAA,CAAA,IAAC;4EAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCA3Dd;;;;;gCAoEX;gCACC,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrC;GA/JgB;;QACO,mIAAA,CAAA,kBAAe;;;KADtB", "debugId": null}}, {"offset": {"line": 3198, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/map/location-popup.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from 'react';\r\nimport { Popup } from '@geon-map/odf';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\r\nimport { MapPin, X, Copy, Navigation, Info } from 'lucide-react';\r\nimport { toast } from 'sonner';\r\n\r\ninterface LocationPopupProps {\r\n  position: [number, number];\r\n  latitude: number;\r\n  longitude: number;\r\n  accuracy?: number;\r\n  timestamp?: number;\r\n  onClose: () => void;\r\n}\r\n\r\nexport function LocationPopup({\r\n  position,\r\n  latitude,\r\n  longitude,\r\n  accuracy,\r\n  timestamp,\r\n  onClose\r\n}: LocationPopupProps) {\r\n  const [isPopoverOpen, setIsPopoverOpen] = useState(false);\r\n\r\n  const handleCopyCoordinates = () => {\r\n    const coordText = `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;\r\n    navigator.clipboard.writeText(coordText);\r\n    toast.success(\"좌표가 복사되었습니다\");\r\n  };\r\n\r\n  const handleCopyProjectedCoordinates = () => {\r\n    const coordText = `${position[0].toFixed(2)}, ${position[1].toFixed(2)}`;\r\n    navigator.clipboard.writeText(coordText);\r\n    toast.success(\"투영 좌표가 복사되었습니다\");\r\n  };\r\n\r\n  const formatTimestamp = (timestamp?: number) => {\r\n    if (!timestamp) return null;\r\n    return new Date(timestamp).toLocaleString('ko-KR');\r\n  };\r\n\r\n  return (\r\n    <Popup\r\n      position={position}\r\n      offset={[0, 0]}\r\n      autoPan\r\n      autoPanAnimation={250}\r\n      autoPanMargin={20}      \r\n    >\r\n      <Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>\r\n        <PopoverTrigger asChild>\r\n          <Button\r\n            variant=\"default\"\r\n            size=\"sm\"\r\n            className=\"h-8 w-8 p-0 rounded-full bg-blue-600 hover:bg-blue-700 shadow-lg \"\r\n            onClick={() => setIsPopoverOpen(true)}\r\n          >\r\n            <MapPin className=\"h-10 w-10 text-white\" />\r\n          </Button>\r\n        </PopoverTrigger>\r\n\r\n        <PopoverContent\r\n          className=\"w-80 p-0 shadow-lg border-0 backdrop-blur-sm\"\r\n          side=\"top\"\r\n          align=\"center\"\r\n        >\r\n          <Card className=\"border-0 shadow-none bg-transparent\">\r\n            <CardHeader className=\"pb-3\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <CardTitle className=\"text-sm font-semibold flex items-center gap-2\">\r\n                  <div className=\"p-1.5 bg-blue-100 rounded-full\">\r\n                    <MapPin className=\"h-4 w-4 text-blue-600\" />\r\n                  </div>\r\n                  현재 위치\r\n                </CardTitle>\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  onClick={() => {\r\n                    setIsPopoverOpen(false);\r\n                    onClose();\r\n                  }}\r\n                  className=\"h-6 w-6 p-0 hover:bg-gray-100\"\r\n                >\r\n                  <X className=\"h-3 w-3\" />\r\n                </Button>\r\n              </div>\r\n            </CardHeader>\r\n\r\n            <CardContent className=\"space-y-3\">\r\n              {/* GPS 좌표 */}\r\n              <div className=\"space-y-2\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <span className=\"text-xs font-medium text-gray-600\">GPS 좌표 (WGS84)</span>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"sm\"\r\n                    onClick={handleCopyCoordinates}\r\n                    className=\"h-6 px-2 text-xs hover:bg-gray-100\"\r\n                  >\r\n                    <Copy className=\"h-3 w-3 mr-1\" />\r\n                    복사\r\n                  </Button>\r\n                </div>\r\n                <div className=\"bg-gray-50 rounded-md p-2 font-mono text-xs\">\r\n                  <div>위도: {latitude.toFixed(6)}</div>\r\n                  <div>경도: {longitude.toFixed(6)}</div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* 투영 좌표 */}\r\n              <div className=\"space-y-2\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <span className=\"text-xs font-medium text-gray-600\">투영 좌표</span>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"sm\"\r\n                    onClick={handleCopyProjectedCoordinates}\r\n                    className=\"h-6 px-2 text-xs hover:bg-gray-100\"\r\n                  >\r\n                    <Copy className=\"h-3 w-3 mr-1\" />\r\n                    복사\r\n                  </Button>\r\n                </div>\r\n                <div className=\"bg-gray-50 rounded-md p-2 font-mono text-xs\">\r\n                  <div>X: {position[0].toFixed(2)}</div>\r\n                  <div>Y: {position[1].toFixed(2)}</div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* 정확도 정보 */}\r\n              {accuracy && (\r\n                <div className=\"flex items-center justify-between\">\r\n                  <span className=\"text-xs font-medium text-gray-600\">정확도</span>\r\n                  <Badge variant=\"outline\" className=\"text-xs\">\r\n                    ±{Math.round(accuracy)}m\r\n                  </Badge>\r\n                </div>\r\n              )}\r\n\r\n              {/* 시간 정보 */}\r\n              {timestamp && (\r\n                <div className=\"flex items-center justify-between\">\r\n                  <span className=\"text-xs font-medium text-gray-600\">측정 시간</span>\r\n                  <span className=\"text-xs text-gray-500\">\r\n                    {formatTimestamp(timestamp)}\r\n                  </span>\r\n                </div>\r\n              )}\r\n\r\n              {/* 액션 버튼 */}\r\n              <div className=\"pt-2 border-t\">\r\n                <Button\r\n                  variant=\"outline\"\r\n                  size=\"sm\"\r\n                  onClick={() => {\r\n                    toast.success(\"지도 중심 이동 기능은 지도 컨텍스트에서 처리됩니다\");\r\n                  }}\r\n                  className=\"w-full text-xs\"\r\n                >\r\n                  <Navigation className=\"h-3 w-3 mr-1\" />\r\n                  이 위치로 지도 중심 이동\r\n                </Button>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        </PopoverContent>\r\n      </Popover>\r\n    </Popup>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;AATA;;;;;;;;;AAoBO,SAAS,cAAc,EAC5B,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,SAAS,EACT,OAAO,EACY;;IACnB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,wBAAwB;QAC5B,MAAM,YAAY,GAAG,SAAS,OAAO,CAAC,GAAG,EAAE,EAAE,UAAU,OAAO,CAAC,IAAI;QACnE,UAAU,SAAS,CAAC,SAAS,CAAC;QAC9B,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,iCAAiC;QACrC,MAAM,YAAY,GAAG,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI;QACxE,UAAU,SAAS,CAAC,SAAS,CAAC;QAC9B,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,WAAW,OAAO;QACvB,OAAO,IAAI,KAAK,WAAW,cAAc,CAAC;IAC5C;IAEA,qBACE,sSAAC,gPAAA,CAAA,QAAK;QACJ,UAAU;QACV,QAAQ;YAAC;YAAG;SAAE;QACd,OAAO;QACP,kBAAkB;QAClB,eAAe;kBAEf,cAAA,sSAAC,+HAAA,CAAA,UAAO;YAAC,MAAM;YAAe,cAAc;;8BAC1C,sSAAC,+HAAA,CAAA,iBAAc;oBAAC,OAAO;8BACrB,cAAA,sSAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS,IAAM,iBAAiB;kCAEhC,cAAA,sSAAC,iSAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;;;;;;8BAItB,sSAAC,+HAAA,CAAA,iBAAc;oBACb,WAAU;oBACV,MAAK;oBACL,OAAM;8BAEN,cAAA,sSAAC,4HAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,sSAAC,4HAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,sSAAC;oCAAI,WAAU;;sDACb,sSAAC,4HAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,sSAAC;oDAAI,WAAU;8DACb,cAAA,sSAAC,iSAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;gDACd;;;;;;;sDAGR,sSAAC,8HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;gDACP,iBAAiB;gDACjB;4CACF;4CACA,WAAU;sDAEV,cAAA,sSAAC,mRAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAKnB,sSAAC,4HAAA,CAAA,cAAW;gCAAC,WAAU;;kDAErB,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;wDAAK,WAAU;kEAAoC;;;;;;kEACpD,sSAAC,8HAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;wDACT,WAAU;;0EAEV,sSAAC,yRAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;0DAIrC,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;;4DAAI;4DAAK,SAAS,OAAO,CAAC;;;;;;;kEAC3B,sSAAC;;4DAAI;4DAAK,UAAU,OAAO,CAAC;;;;;;;;;;;;;;;;;;;kDAKhC,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;wDAAK,WAAU;kEAAoC;;;;;;kEACpD,sSAAC,8HAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;wDACT,WAAU;;0EAEV,sSAAC,yRAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;0DAIrC,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;;4DAAI;4DAAI,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC;;;;;;;kEAC7B,sSAAC;;4DAAI;4DAAI,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;oCAKhC,0BACC,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAK,WAAU;0DAAoC;;;;;;0DACpD,sSAAC,6HAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;;oDAAU;oDACzC,KAAK,KAAK,CAAC;oDAAU;;;;;;;;;;;;;oCAM5B,2BACC,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAK,WAAU;0DAAoC;;;;;;0DACpD,sSAAC;gDAAK,WAAU;0DACb,gBAAgB;;;;;;;;;;;;kDAMvB,sSAAC;wCAAI,WAAU;kDACb,cAAA,sSAAC,8HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;gDACP,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;4CAChB;4CACA,WAAU;;8DAEV,sSAAC,qSAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzD;GA5JgB;KAAA", "debugId": null}}, {"offset": {"line": 3624, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/map/origin-popup.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from 'react';\r\nimport { Popup } from '@geon-map/odf';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\r\nimport { MapPin, X, Copy, Navigation, ArrowRight } from 'lucide-react';\r\nimport { toast } from 'sonner';\r\n\r\ninterface OriginPopupProps {\r\n  position: [number, number];\r\n  address: {\r\n    roadAddr: string;\r\n    jibunAddr?: string;\r\n    buildName?: string;\r\n    buildLo: string;\r\n    buildLa: string;\r\n  };\r\n  onClose: () => void;\r\n}\r\n\r\nexport function OriginPopup({\r\n  position,\r\n  address,\r\n  onClose\r\n}: OriginPopupProps) {\r\n  const [isPopoverOpen, setIsPopoverOpen] = useState(false);\r\n\r\n  const handleCopyAddress = () => {\r\n    navigator.clipboard.writeText(address.roadAddr);\r\n    toast.success(\"주소가 복사되었습니다\");\r\n  };\r\n\r\n  const handleCopyCoordinates = () => {\r\n    const coordText = `${address.buildLo}, ${address.buildLa}`;\r\n    navigator.clipboard.writeText(coordText);\r\n    toast.success(\"좌표가 복사되었습니다\");\r\n  };\r\n\r\n  const handleCopyProjectedCoordinates = () => {\r\n    const coordText = `${position[0].toFixed(2)}, ${position[1].toFixed(2)}`;\r\n    navigator.clipboard.writeText(coordText);\r\n    toast.success(\"투영 좌표가 복사되었습니다\");\r\n  };\r\n\r\n  return (\r\n    <Popup\r\n      position={position}\r\n      offset={[0, -10]}\r\n      // autoPan\r\n      // autoPanAnimation={250}\r\n      // autoPanMargin={20}      \r\n    >\r\n      <Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>\r\n        <PopoverTrigger asChild>\r\n          <Button\r\n            variant=\"default\"\r\n            size=\"sm\"\r\n            className=\"h-10 w-10 p-0 rounded-full bg-green-600 hover:bg-green-700 shadow-lg border-2 border-white\"\r\n            onClick={() => setIsPopoverOpen(true)}\r\n          >\r\n            출발\r\n          </Button>\r\n        </PopoverTrigger>\r\n\r\n        <PopoverContent\r\n          className=\"w-80 p-0 shadow-lg border-0 backdrop-blur-sm\"\r\n          side=\"top\"\r\n          align=\"center\"\r\n        >\r\n          <Card className=\"border-0 shadow-none bg-transparent\">\r\n            <CardHeader className=\"pb-3\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <CardTitle className=\"text-sm font-semibold flex items-center gap-2\">\r\n                  <div className=\"p-1.5 bg-green-100 rounded-full\">\r\n                    <ArrowRight className=\"h-4 w-4 text-green-600\" />\r\n                  </div>\r\n                  출발지\r\n                </CardTitle>\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  onClick={() => {\r\n                    setIsPopoverOpen(false);\r\n                    // onClose();\r\n                  }}\r\n                  className=\"h-6 w-6 p-0 hover:bg-gray-100\"\r\n                >\r\n                  <X className=\"h-3 w-3\" />\r\n                </Button>\r\n              </div>\r\n            </CardHeader>\r\n\r\n            <CardContent className=\"space-y-3\">\r\n              {/* 건물명 */}\r\n              {address.buildName && (\r\n                <div className=\"space-y-1\">\r\n                  <span className=\"text-xs font-medium text-gray-600\">건물명</span>\r\n                  <div className=\"bg-green-50 rounded-md p-2 text-sm font-medium text-green-800\">\r\n                    {address.buildName}\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {/* 도로명주소 */}\r\n              <div className=\"space-y-2\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <span className=\"text-xs font-medium text-gray-600\">도로명주소</span>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"sm\"\r\n                    onClick={handleCopyAddress}\r\n                    className=\"h-6 px-2 text-xs hover:bg-gray-100\"\r\n                  >\r\n                    <Copy className=\"h-3 w-3 mr-1\" />\r\n                    복사\r\n                  </Button>\r\n                </div>\r\n                <div className=\"bg-gray-50 rounded-md p-2 text-xs\">\r\n                  {address.roadAddr}\r\n                </div>\r\n              </div>\r\n\r\n              {/* 지번주소 */}\r\n              {address.jibunAddr && address.jibunAddr !== address.roadAddr && (\r\n                <div className=\"space-y-2\">\r\n                  <span className=\"text-xs font-medium text-gray-600\">지번주소</span>\r\n                  <div className=\"bg-gray-50 rounded-md p-2 text-xs\">\r\n                    {address.jibunAddr}\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {/* GPS 좌표 */}\r\n              <div className=\"space-y-2\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <span className=\"text-xs font-medium text-gray-600\">GPS 좌표 (WGS84)</span>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"sm\"\r\n                    onClick={handleCopyCoordinates}\r\n                    className=\"h-6 px-2 text-xs hover:bg-gray-100\"\r\n                  >\r\n                    <Copy className=\"h-3 w-3 mr-1\" />\r\n                    복사\r\n                  </Button>\r\n                </div>\r\n                <div className=\"bg-gray-50 rounded-md p-2 font-mono text-xs\">\r\n                  <div>경도: {address.buildLo}</div>\r\n                  <div>위도: {address.buildLa}</div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* 투영 좌표 */}\r\n              <div className=\"space-y-2\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <span className=\"text-xs font-medium text-gray-600\">투영 좌표</span>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"sm\"\r\n                    onClick={handleCopyProjectedCoordinates}\r\n                    className=\"h-6 px-2 text-xs hover:bg-gray-100\"\r\n                  >\r\n                    <Copy className=\"h-3 w-3 mr-1\" />\r\n                    복사\r\n                  </Button>\r\n                </div>\r\n                <div className=\"bg-gray-50 rounded-md p-2 font-mono text-xs\">\r\n                  <div>X: {position[0].toFixed(2)}</div>\r\n                  <div>Y: {position[1].toFixed(2)}</div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* 출발지 표시 */}\r\n              <div className=\"pt-2 border-t\">\r\n                <Badge variant=\"outline\" className=\"w-full justify-center bg-green-50 text-green-700 border-green-200\">\r\n                  <ArrowRight className=\"h-3 w-3 mr-1\" />\r\n                  경로 탐색 출발지\r\n                </Badge>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        </PopoverContent>\r\n      </Popover>\r\n    </Popup>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;;;AATA;;;;;;;;;AAuBO,SAAS,YAAY,EAC1B,QAAQ,EACR,OAAO,EACP,OAAO,EACU;;IACjB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,oBAAoB;QACxB,UAAU,SAAS,CAAC,SAAS,CAAC,QAAQ,QAAQ;QAC9C,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,wBAAwB;QAC5B,MAAM,YAAY,GAAG,QAAQ,OAAO,CAAC,EAAE,EAAE,QAAQ,OAAO,EAAE;QAC1D,UAAU,SAAS,CAAC,SAAS,CAAC;QAC9B,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,iCAAiC;QACrC,MAAM,YAAY,GAAG,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI;QACxE,UAAU,SAAS,CAAC,SAAS,CAAC;QAC9B,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,qBACE,sSAAC,gPAAA,CAAA,QAAK;QACJ,UAAU;QACV,QAAQ;YAAC;YAAG,CAAC;SAAG;kBAKhB,cAAA,sSAAC,+HAAA,CAAA,UAAO;YAAC,MAAM;YAAe,cAAc;;8BAC1C,sSAAC,+HAAA,CAAA,iBAAc;oBAAC,OAAO;8BACrB,cAAA,sSAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS,IAAM,iBAAiB;kCACjC;;;;;;;;;;;8BAKH,sSAAC,+HAAA,CAAA,iBAAc;oBACb,WAAU;oBACV,MAAK;oBACL,OAAM;8BAEN,cAAA,sSAAC,4HAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,sSAAC,4HAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,sSAAC;oCAAI,WAAU;;sDACb,sSAAC,4HAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,sSAAC;oDAAI,WAAU;8DACb,cAAA,sSAAC,ySAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;gDAClB;;;;;;;sDAGR,sSAAC,8HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;gDACP,iBAAiB;4CACjB,aAAa;4CACf;4CACA,WAAU;sDAEV,cAAA,sSAAC,mRAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAKnB,sSAAC,4HAAA,CAAA,cAAW;gCAAC,WAAU;;oCAEpB,QAAQ,SAAS,kBAChB,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAK,WAAU;0DAAoC;;;;;;0DACpD,sSAAC;gDAAI,WAAU;0DACZ,QAAQ,SAAS;;;;;;;;;;;;kDAMxB,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;wDAAK,WAAU;kEAAoC;;;;;;kEACpD,sSAAC,8HAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;wDACT,WAAU;;0EAEV,sSAAC,yRAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;0DAIrC,sSAAC;gDAAI,WAAU;0DACZ,QAAQ,QAAQ;;;;;;;;;;;;oCAKpB,QAAQ,SAAS,IAAI,QAAQ,SAAS,KAAK,QAAQ,QAAQ,kBAC1D,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAK,WAAU;0DAAoC;;;;;;0DACpD,sSAAC;gDAAI,WAAU;0DACZ,QAAQ,SAAS;;;;;;;;;;;;kDAMxB,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;wDAAK,WAAU;kEAAoC;;;;;;kEACpD,sSAAC,8HAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;wDACT,WAAU;;0EAEV,sSAAC,yRAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;0DAIrC,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;;4DAAI;4DAAK,QAAQ,OAAO;;;;;;;kEACzB,sSAAC;;4DAAI;4DAAK,QAAQ,OAAO;;;;;;;;;;;;;;;;;;;kDAK7B,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;wDAAK,WAAU;kEAAoC;;;;;;kEACpD,sSAAC,8HAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;wDACT,WAAU;;0EAEV,sSAAC,yRAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;0DAIrC,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;;4DAAI;4DAAI,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC;;;;;;;kEAC7B,sSAAC;;4DAAI;4DAAI,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;kDAKjC,sSAAC;wCAAI,WAAU;kDACb,cAAA,sSAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;;8DACjC,sSAAC,ySAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzD;GArKgB;KAAA", "debugId": null}}, {"offset": {"line": 4085, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/map/destination-popup.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from 'react';\r\nimport { Popup } from '@geon-map/odf';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\r\nimport { MapPin, X, Copy, Navigation, Target } from 'lucide-react';\r\nimport { toast } from 'sonner';\r\n\r\ninterface DestinationPopupProps {\r\n  position: [number, number];\r\n  address: {\r\n    roadAddr: string;\r\n    jibunAddr?: string;\r\n    buildName?: string;\r\n    buildLo: string;\r\n    buildLa: string;\r\n  };\r\n  onClose: () => void;\r\n}\r\n\r\nexport function DestinationPopup({\r\n  position,\r\n  address,\r\n  onClose\r\n}: DestinationPopupProps) {\r\n  const [isPopoverOpen, setIsPopoverOpen] = useState(false);\r\n\r\n  const handleCopyAddress = () => {\r\n    navigator.clipboard.writeText(address.roadAddr);\r\n    toast.success(\"주소가 복사되었습니다\");\r\n  };\r\n\r\n  const handleCopyCoordinates = () => {\r\n    const coordText = `${address.buildLo}, ${address.buildLa}`;\r\n    navigator.clipboard.writeText(coordText);\r\n    toast.success(\"좌표가 복사되었습니다\");\r\n  };\r\n\r\n  const handleCopyProjectedCoordinates = () => {\r\n    const coordText = `${position[0].toFixed(2)}, ${position[1].toFixed(2)}`;\r\n    navigator.clipboard.writeText(coordText);\r\n    toast.success(\"투영 좌표가 복사되었습니다\");\r\n  };\r\n\r\n  return (\r\n    <Popup\r\n      position={position}\r\n      offset={[0, -10]}\r\n      // autoPan\r\n      // autoPanAnimation={250}\r\n      // autoPanMargin={20}      \r\n    >\r\n      <Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>\r\n        <PopoverTrigger asChild>\r\n          <Button\r\n            variant=\"default\"\r\n            size=\"sm\"\r\n            className=\"h-10 w-10 p-0 rounded-full bg-red-600 hover:bg-red-700 shadow-lg border-2 border-white\"\r\n            onClick={() => setIsPopoverOpen(true)}\r\n          >\r\n            도착\r\n          </Button>\r\n        </PopoverTrigger>\r\n\r\n        <PopoverContent\r\n          className=\"w-80 p-0 shadow-lg border-0 backdrop-blur-sm\"\r\n          side=\"top\"\r\n          align=\"center\"\r\n        >\r\n          <Card className=\"border-0 shadow-none bg-transparent\">\r\n            <CardHeader className=\"pb-3\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <CardTitle className=\"text-sm font-semibold flex items-center gap-2\">\r\n                  <div className=\"p-1.5 bg-red-100 rounded-full\">\r\n                    <Target className=\"h-4 w-4 text-red-600\" />\r\n                  </div>\r\n                  목적지\r\n                </CardTitle>\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  onClick={() => {\r\n                    setIsPopoverOpen(false);\r\n                  }}\r\n                  className=\"h-6 w-6 p-0 hover:bg-gray-100\"\r\n                >\r\n                  <X className=\"h-3 w-3\" />\r\n                </Button>\r\n              </div>\r\n            </CardHeader>\r\n\r\n            <CardContent className=\"space-y-3\">\r\n              {/* 건물명 */}\r\n              {address.buildName && (\r\n                <div className=\"space-y-1\">\r\n                  <span className=\"text-xs font-medium text-gray-600\">건물명</span>\r\n                  <div className=\"bg-red-50 rounded-md p-2 text-sm font-medium text-red-800\">\r\n                    {address.buildName}\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {/* 도로명주소 */}\r\n              <div className=\"space-y-2\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <span className=\"text-xs font-medium text-gray-600\">도로명주소</span>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"sm\"\r\n                    onClick={handleCopyAddress}\r\n                    className=\"h-6 px-2 text-xs hover:bg-gray-100\"\r\n                  >\r\n                    <Copy className=\"h-3 w-3 mr-1\" />\r\n                    복사\r\n                  </Button>\r\n                </div>\r\n                <div className=\"bg-gray-50 rounded-md p-2 text-xs\">\r\n                  {address.roadAddr}\r\n                </div>\r\n              </div>\r\n\r\n              {/* 지번주소 */}\r\n              {address.jibunAddr && address.jibunAddr !== address.roadAddr && (\r\n                <div className=\"space-y-2\">\r\n                  <span className=\"text-xs font-medium text-gray-600\">지번주소</span>\r\n                  <div className=\"bg-gray-50 rounded-md p-2 text-xs\">\r\n                    {address.jibunAddr}\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {/* GPS 좌표 */}\r\n              <div className=\"space-y-2\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <span className=\"text-xs font-medium text-gray-600\">GPS 좌표 (WGS84)</span>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"sm\"\r\n                    onClick={handleCopyCoordinates}\r\n                    className=\"h-6 px-2 text-xs hover:bg-gray-100\"\r\n                  >\r\n                    <Copy className=\"h-3 w-3 mr-1\" />\r\n                    복사\r\n                  </Button>\r\n                </div>\r\n                <div className=\"bg-gray-50 rounded-md p-2 font-mono text-xs\">\r\n                  <div>경도: {address.buildLo}</div>\r\n                  <div>위도: {address.buildLa}</div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* 투영 좌표 */}\r\n              <div className=\"space-y-2\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <span className=\"text-xs font-medium text-gray-600\">투영 좌표</span>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"sm\"\r\n                    onClick={handleCopyProjectedCoordinates}\r\n                    className=\"h-6 px-2 text-xs hover:bg-gray-100\"\r\n                  >\r\n                    <Copy className=\"h-3 w-3 mr-1\" />\r\n                    복사\r\n                  </Button>\r\n                </div>\r\n                <div className=\"bg-gray-50 rounded-md p-2 font-mono text-xs\">\r\n                  <div>X: {position[0].toFixed(2)}</div>\r\n                  <div>Y: {position[1].toFixed(2)}</div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* 목적지 표시 */}\r\n              <div className=\"pt-2 border-t\">\r\n                <Badge variant=\"outline\" className=\"w-full justify-center bg-red-50 text-red-700 border-red-200\">\r\n                  <Target className=\"h-3 w-3 mr-1\" />\r\n                  경로 탐색 목적지\r\n                </Badge>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        </PopoverContent>\r\n      </Popover>\r\n    </Popup>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;;;AATA;;;;;;;;;AAuBO,SAAS,iBAAiB,EAC/B,QAAQ,EACR,OAAO,EACP,OAAO,EACe;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,oBAAoB;QACxB,UAAU,SAAS,CAAC,SAAS,CAAC,QAAQ,QAAQ;QAC9C,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,wBAAwB;QAC5B,MAAM,YAAY,GAAG,QAAQ,OAAO,CAAC,EAAE,EAAE,QAAQ,OAAO,EAAE;QAC1D,UAAU,SAAS,CAAC,SAAS,CAAC;QAC9B,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,iCAAiC;QACrC,MAAM,YAAY,GAAG,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI;QACxE,UAAU,SAAS,CAAC,SAAS,CAAC;QAC9B,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,qBACE,sSAAC,gPAAA,CAAA,QAAK;QACJ,UAAU;QACV,QAAQ;YAAC;YAAG,CAAC;SAAG;kBAKhB,cAAA,sSAAC,+HAAA,CAAA,UAAO;YAAC,MAAM;YAAe,cAAc;;8BAC1C,sSAAC,+HAAA,CAAA,iBAAc;oBAAC,OAAO;8BACrB,cAAA,sSAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS,IAAM,iBAAiB;kCACjC;;;;;;;;;;;8BAKH,sSAAC,+HAAA,CAAA,iBAAc;oBACb,WAAU;oBACV,MAAK;oBACL,OAAM;8BAEN,cAAA,sSAAC,4HAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,sSAAC,4HAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,sSAAC;oCAAI,WAAU;;sDACb,sSAAC,4HAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,sSAAC;oDAAI,WAAU;8DACb,cAAA,sSAAC,6RAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;gDACd;;;;;;;sDAGR,sSAAC,8HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;gDACP,iBAAiB;4CACnB;4CACA,WAAU;sDAEV,cAAA,sSAAC,mRAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAKnB,sSAAC,4HAAA,CAAA,cAAW;gCAAC,WAAU;;oCAEpB,QAAQ,SAAS,kBAChB,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAK,WAAU;0DAAoC;;;;;;0DACpD,sSAAC;gDAAI,WAAU;0DACZ,QAAQ,SAAS;;;;;;;;;;;;kDAMxB,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;wDAAK,WAAU;kEAAoC;;;;;;kEACpD,sSAAC,8HAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;wDACT,WAAU;;0EAEV,sSAAC,yRAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;0DAIrC,sSAAC;gDAAI,WAAU;0DACZ,QAAQ,QAAQ;;;;;;;;;;;;oCAKpB,QAAQ,SAAS,IAAI,QAAQ,SAAS,KAAK,QAAQ,QAAQ,kBAC1D,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAK,WAAU;0DAAoC;;;;;;0DACpD,sSAAC;gDAAI,WAAU;0DACZ,QAAQ,SAAS;;;;;;;;;;;;kDAMxB,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;wDAAK,WAAU;kEAAoC;;;;;;kEACpD,sSAAC,8HAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;wDACT,WAAU;;0EAEV,sSAAC,yRAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;0DAIrC,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;;4DAAI;4DAAK,QAAQ,OAAO;;;;;;;kEACzB,sSAAC;;4DAAI;4DAAK,QAAQ,OAAO;;;;;;;;;;;;;;;;;;;kDAK7B,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;wDAAK,WAAU;kEAAoC;;;;;;kEACpD,sSAAC,8HAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;wDACT,WAAU;;0EAEV,sSAAC,yRAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;0DAIrC,sSAAC;gDAAI,WAAU;;kEACb,sSAAC;;4DAAI;4DAAI,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC;;;;;;;kEAC7B,sSAAC;;4DAAI;4DAAI,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;kDAKjC,sSAAC;wCAAI,WAAU;kDACb,cAAA,sSAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;;8DACjC,sSAAC,6RAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrD;GApKgB;KAAA", "debugId": null}}]}