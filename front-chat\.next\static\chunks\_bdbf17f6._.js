(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/providers/basemap-provider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "BasemapProvider": (()=>BasemapProvider),
    "useBasemap": (()=>useBasemap)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.3_@opentelemetry+_a8516a9e8193861921635ddb2ae04d26/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.3_@opentelemetry+_a8516a9e8193861921635ddb2ae04d26/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
"use client";
;
const BasemapContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(null);
function BasemapProvider({ children, mapState }) {
    _s();
    const [currentBasemap, setCurrentBasemap] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('eMapBasic');
    // ODF 맵이 초기화되면 배경지도 변경 콜백 설정
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "BasemapProvider.useEffect": ()=>{
            if (!mapState?.map) return;
            const map = mapState.map;
            // basemapControl이 있는지 확인하고 콜백 설정
            if (map.basemapControl && typeof map.basemapControl.setSwitchBaseLayerCallback === 'function') {
                map.basemapControl.setSwitchBaseLayerCallback({
                    "BasemapProvider.useEffect": (beforeLayer, afterLayer)=>{
                        setCurrentBasemap(afterLayer);
                    }
                }["BasemapProvider.useEffect"]);
            }
            // 초기 배경지도 상태 설정 (가능한 경우)
            if (map.basemapControl && typeof map.basemapControl.getPresentBaseGrpKey === 'function') {
                try {
                    const currentKey = map.basemapControl.getPresentBaseGrpKey();
                    if (currentKey && [
                        'eMapBasic',
                        'eMapAIR',
                        'eMapColor',
                        'eMapWhite'
                    ].includes(currentKey)) {
                        setCurrentBasemap(currentKey);
                    }
                } catch (error) {
                    console.warn('Failed to get current basemap:', error);
                }
            }
        }
    }["BasemapProvider.useEffect"], [
        mapState?.map
    ]);
    const changeBasemap = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "BasemapProvider.useCallback[changeBasemap]": (basemap)=>{
            if (!mapState?.view?.setBasemap) {
                console.error("map.setBasemap is not available or map not ready.");
                return;
            }
            // ODF 맵에서 배경지도 변경 (콜백에서 상태 업데이트됨)
            mapState.view.setBasemap(basemap);
            setCurrentBasemap(basemap);
        }
    }["BasemapProvider.useCallback[changeBasemap]"], [
        mapState
    ]);
    const contextValue = {
        currentBasemap,
        setCurrentBasemap,
        changeBasemap
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(BasemapContext.Provider, {
        value: contextValue,
        children: children
    }, void 0, false, {
        fileName: "[project]/providers/basemap-provider.tsx",
        lineNumber: 68,
        columnNumber: 5
    }, this);
}
_s(BasemapProvider, "8XB48m6U9w8eMrgUgeJ7MhrnvXc=");
_c = BasemapProvider;
function useBasemap() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(BasemapContext);
    if (!context) {
        throw new Error('useBasemap must be used within a BasemapProvider');
    }
    return context;
}
_s1(useBasemap, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_context__.k.register(_c, "BasemapProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/providers/tool-invocation-provider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ToolInvocationProvider": (()=>ToolInvocationProvider),
    "useFilteredLayers": (()=>useFilteredLayers),
    "useLayerById": (()=>useLayerById),
    "useLayerManager": (()=>useLayerManager),
    "useLocation": (()=>useLocation),
    "useToolResults": (()=>useToolResults)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.3_@opentelemetry+_a8516a9e8193861921635ddb2ae04d26/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.3_@opentelemetry+_a8516a9e8193861921635ddb2ae04d26/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$types$2f$layer$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/types/layer-manager.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$types$2f$layer$2d$style$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/types/layer-style.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$map$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/map-utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$1_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/sonner@1.7.1_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$providers$2f$basemap$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/providers/basemap-provider.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature(), _s4 = __turbopack_context__.k.signature(), _s5 = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
// 단순한 스타일 속성을 WMS 스타일 구조로 변환하는 함수
const convertSimpleStyleToWMS = (simpleStyle, layerInfo)=>{
    let baseStyle;
    // 기존 스타일이 있다면 그것을 베이스로 사용
    if (layerInfo?.currentStyle && layerInfo.currentStyle.rules) {
        baseStyle = {
            ...layerInfo.currentStyle
        };
    } else {
        // 지오메트리 타입에 따라 적절한 기본 스타일 선택
        const geometryType = layerInfo?.geometryType || 'point';
        switch(geometryType){
            case 'polygon':
            case '3':
                baseStyle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$types$2f$layer$2d$style$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createDefaultPolygonStyle"])();
                break;
            case 'line':
            case '2':
                baseStyle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$types$2f$layer$2d$style$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createDefaultLineStyle"])();
                break;
            case 'point':
            case '1':
            default:
                baseStyle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$types$2f$layer$2d$style$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createDefaultPointStyle"])();
                break;
        }
    }
    // 기존 스타일을 복사하여 수정
    const updatedStyle = JSON.parse(JSON.stringify(baseStyle));
    const rule = updatedStyle.rules[0];
    const symbolizer = rule.symbolizers[0];
    // 단순 스타일 속성을 WMS 구조에 매핑
    if (simpleStyle.color) {
        symbolizer.color = simpleStyle.color;
    }
    if (simpleStyle.fillOpacity !== undefined) {
        symbolizer.fillOpacity = simpleStyle.fillOpacity;
    }
    if (simpleStyle.strokeColor) {
        if (symbolizer.kind === 'Fill') {
            symbolizer.outlineColor = simpleStyle.strokeColor;
        } else {
            symbolizer.strokeColor = simpleStyle.strokeColor;
        }
    }
    if (simpleStyle.strokeWidth !== undefined) {
        if (symbolizer.kind === 'Fill') {
            symbolizer.outlineWidth = simpleStyle.strokeWidth;
        } else {
            symbolizer.strokeWidth = simpleStyle.strokeWidth;
        }
    }
    if (simpleStyle.radius !== undefined && symbolizer.kind === 'Mark') {
        symbolizer.radius = simpleStyle.radius;
    }
    if (simpleStyle.width !== undefined && symbolizer.kind === 'Line') {
        symbolizer.width = simpleStyle.width;
    }
    if (simpleStyle.symbol && symbolizer.kind === 'Mark') {
        symbolizer.wellKnownName = simpleStyle.symbol;
    }
    return updatedStyle;
};
const ToolContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])({});
const LayerManagerContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(null);
const LocationContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])({
    currentLocation: null,
    setCurrentLocation: ()=>{},
    originPoint: null,
    setOriginPoint: ()=>{},
    destinationPoint: null,
    setDestinationPoint: ()=>{}
});
// 레이어 상태 관리 리듀서
function layerReducer(state, action) {
    switch(action.type){
        case 'ADD_LAYER':
            // 중복 방지: 같은 ID가 있으면 업데이트
            const existingIndex = state.findIndex((layer)=>layer.id === action.payload.id);
            if (existingIndex >= 0) {
                const newState = [
                    ...state
                ];
                newState[existingIndex] = {
                    ...newState[existingIndex],
                    ...action.payload
                };
                return newState;
            }
            // 새 레이어는 가장 높은 zIndex를 가져야 함 (가장 위에 표시)
            const maxZIndex = state.length > 0 ? Math.max(...state.map((layer)=>layer.zIndex || 0)) : 0;
            const newLayer = {
                ...action.payload,
                zIndex: maxZIndex + 1
            };
            return [
                ...state,
                newLayer
            ];
        case 'UPDATE_LAYER':
            return state.map((layer)=>layer.id === action.payload.id ? {
                    ...layer,
                    ...action.payload.updates
                } : layer);
        case 'REMOVE_LAYER':
            return state.filter((layer)=>layer.id !== action.payload);
        case 'TOGGLE_VISIBILITY':
            return state.map((layer)=>layer.id === action.payload ? {
                    ...layer,
                    visible: !layer.visible,
                    userModified: true
                } : layer);
        case 'UPDATE_FILTER':
            return state.map((layer)=>layer.id === action.payload.id ? {
                    ...layer,
                    filter: action.payload.filter
                } : layer);
        case 'UPDATE_STYLE':
            return state.map((layer)=>layer.id === action.payload.id ? {
                    ...layer,
                    style: action.payload.style
                } : layer);
        case 'UPDATE_Z_INDEX':
            return state.map((layer)=>layer.id === action.payload.id ? {
                    ...layer,
                    zIndex: action.payload.zIndex
                } : layer);
        case 'REORDER_LAYERS':
            const { layerIds } = action.payload;
            // 기존 state를 복사하여 객체 참조를 최대한 유지
            return state.map((layer)=>{
                const layerIndex = layerIds.indexOf(layer.id);
                if (layerIndex >= 0) {
                    // DnD 인덱스와 zIndex는 반대 관계
                    // DnD에서 index 0 = 가장 아래 = 가장 낮은 zIndex
                    // DnD에서 마지막 index = 가장 위 = 가장 높은 zIndex
                    const newZIndex = layerIds.length - layerIndex;
                    // zIndex만 변경되었을 때만 새 객체 생성
                    if (layer.zIndex !== newZIndex) {
                        return {
                            ...layer,
                            zIndex: newZIndex
                        };
                    }
                    return layer; // zIndex가 같으면 기존 객체 그대로 반환
                }
                // 순서에 포함되지 않은 레이어는 zIndex 0으로 설정
                if (layer.zIndex !== 0) {
                    return {
                        ...layer,
                        zIndex: 0
                    };
                }
                return layer; // 변경사항이 없으면 기존 객체 그대로 반환
            });
        case 'SET_LAYERS':
            return action.payload;
        default:
            return state;
    }
}
function ToolInvocationProvider({ messages, children, enableSmartNavigation = true, mapState }) {
    _s();
    const [resultMap, setResultMap] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const [layers, dispatch] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useReducer"])(layerReducer, []);
    const [currentLocation, setCurrentLocation] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [originPoint, setOriginPoint] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [destinationPoint, setDestinationPoint] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const processedToolCallsRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(new Set());
    const processedSmartNavigationRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(new Set()); // 스마트 네비게이션 처리 추적
    const previousMessagesRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])([]); // 이전 메시지 상태 추적
    // 배경지도 상태 관리를 위한 useBasemap 훅
    const { setCurrentBasemap } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$providers$2f$basemap$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBasemap"])();
    // 지연된 배치 처리를 위한 상태
    const pendingAddressesRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])([]);
    const addressBatchTimeoutRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const BATCH_DELAY = 1000; // 2초 지연
    // 배치 처리된 주소들을 지도에 표시하는 함수
    const processBatchedAddresses = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ToolInvocationProvider.useCallback[processBatchedAddresses]": ()=>{
            if (!enableSmartNavigation || !mapState || pendingAddressesRef.current.length === 0) {
                return;
            }
            try {
                const addresses = pendingAddressesRef.current;
                if (addresses.length === 1) {
                    // 단일 주소인 경우 개선된 함수 사용
                    const { address } = addresses[0];
                    const success = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$map$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["highlightPointOnMap"])(mapState, parseFloat(address.buildLo), parseFloat(address.buildLa), address.buildGeom || address.geom, 13, {
                        clearPrevious: true,
                        useZIndexUp: true,
                        fitPadding: 1000
                    });
                    if (success) {
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$1_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(`${address.roadAddr}로 자동 이동했습니다`);
                    }
                } else {
                    // 여러 주소인 경우 모든 주소를 포함하는 영역으로 이동
                    const coordinates = addresses.map({
                        "ToolInvocationProvider.useCallback[processBatchedAddresses].coordinates": ({ address })=>({
                                lng: parseFloat(address.buildLo),
                                lat: parseFloat(address.buildLa),
                                geometry: address.buildGeom || address.geom
                            })
                    }["ToolInvocationProvider.useCallback[processBatchedAddresses].coordinates"]);
                    // 모든 포인트를 하이라이트 (개선된 함수 사용)
                    coordinates.forEach({
                        "ToolInvocationProvider.useCallback[processBatchedAddresses]": (coord, index)=>{
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$map$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["highlightPointOnMap"])(mapState, coord.lng, coord.lat, coord.geometry, 16, {
                                clearPrevious: index === 0,
                                useZIndexUp: true,
                                fitPadding: 1000
                            });
                        }
                    }["ToolInvocationProvider.useCallback[processBatchedAddresses]"]);
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$1_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(`${addresses.length}개 위치를 지도에 표시했습니다`);
                }
                // 처리 완료 후 초기화
                pendingAddressesRef.current = [];
            } catch (error) {
                console.error('배치 주소 처리 중 오류:', error);
                pendingAddressesRef.current = [];
            }
        }
    }["ToolInvocationProvider.useCallback[processBatchedAddresses]"], [
        enableSmartNavigation,
        mapState
    ]);
    // 스마트 지도 제어 함수들
    const handleSmartNavigation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ToolInvocationProvider.useCallback[handleSmartNavigation]": (toolName, result, toolCallId)=>{
            if (!enableSmartNavigation || !mapState) return;
            const smartNavKey = `${toolName}-${toolCallId}`;
            if (processedSmartNavigationRef.current.has(smartNavKey)) {
                return; // 이미 처리됨
            }
            try {
                switch(toolName){
                    case "searchAddress":
                        if (result.result?.jusoList?.length > 0) {
                            const firstAddress = result.result.jusoList[0];
                            if (firstAddress.buildLo && firstAddress.buildLa) {
                                // 주소를 배치 처리 큐에 추가
                                pendingAddressesRef.current.push({
                                    address: firstAddress,
                                    toolCallId
                                });
                                // 기존 타이머 취소
                                if (addressBatchTimeoutRef.current) {
                                    clearTimeout(addressBatchTimeoutRef.current);
                                }
                                // 새 타이머 설정
                                addressBatchTimeoutRef.current = setTimeout({
                                    "ToolInvocationProvider.useCallback[handleSmartNavigation]": ()=>{
                                        processBatchedAddresses();
                                        addressBatchTimeoutRef.current = null;
                                    }
                                }["ToolInvocationProvider.useCallback[handleSmartNavigation]"], BATCH_DELAY);
                            }
                        }
                        break;
                    case "searchOrigin":
                        if (result.result?.jusoList?.length > 0) {
                            const firstAddress = result.result.jusoList[0];
                            if (firstAddress.buildLo && firstAddress.buildLa && mapState?.map) {
                                const projection = mapState.map.getProjection();
                                const projectedCoord = projection.project([
                                    parseFloat(firstAddress.buildLo),
                                    parseFloat(firstAddress.buildLa)
                                ], "4326");
                                // 출발지 정보 설정
                                setOriginPoint({
                                    address: firstAddress,
                                    projectedCoord: projectedCoord,
                                    toolCallId
                                });
                            }
                        }
                        break;
                    case "searchDestination":
                        if (result.result?.jusoList?.length > 0) {
                            const firstAddress = result.result.jusoList[0];
                            if (firstAddress.buildLo && firstAddress.buildLa && mapState?.map) {
                                const projection = mapState.map.getProjection();
                                const projectedCoord = projection.project([
                                    parseFloat(firstAddress.buildLo),
                                    parseFloat(firstAddress.buildLa)
                                ], "4326");
                                // 목적지 정보 설정
                                setDestinationPoint({
                                    address: firstAddress,
                                    projectedCoord: projectedCoord,
                                    toolCallId
                                });
                            }
                        }
                        break;
                    case "searchDirections":
                        if (result.routes?.length > 0 && result.routes[0].result_code === 0) {
                            // 새로운 타입에서는 레이어 변환기를 통해 자동으로 지도에 표시됨
                            // 별도의 showRouteOnMap 호출 불필요
                            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$1_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("경로를 지도에 자동으로 표시했습니다");
                        }
                        break;
                    case "getLocation":
                        if (!mapState?.map) return;
                        const prj = mapState.map.getProjection();
                        if (result.latitude && result.longitude) {
                            const projectedCoord = prj.project([
                                result.longitude,
                                result.latitude
                            ], "4326");
                            // 위치 정보를 상태에 저장
                            const locationInfo = {
                                latitude: result.latitude,
                                longitude: result.longitude,
                                accuracy: result.accuracy,
                                timestamp: result.timestamp,
                                projectedCoord: projectedCoord
                            };
                            setCurrentLocation(locationInfo);
                            mapState.map.setZoom(14);
                            mapState.map.setCenter(projectedCoord);
                        }
                        break;
                    case "getLayer":
                        // 서울 건물통합정보 레이어인 경우 서울 중심 좌표로 지도 이동 (EPSG:5186 표준 서울 중심좌표)
                        if (result.lyrId === "LR0000004299" || result.name === "GIS건물통합정보_서울") {
                            if (!mapState?.map) return;
                            const center = new odf.Coordinate(955156.7761, 1951925.0984);
                            const newZoom = 11;
                            mapState.map.setCenter(center);
                            mapState.map.setZoom(newZoom);
                            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$1_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(`${result.name || result.id} 레이어를 지도에 추가했습니다`);
                        } else if (result.bbox && Array.isArray(result.bbox) && result.bbox.length >= 4) {
                            const [minX, minY, maxX, maxY] = result.bbox;
                            const centerX = (minX + maxX) / 2;
                            const centerY = (minY + maxY) / 2;
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$map$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["highlightPointOnMap"])(mapState, centerX, centerY, undefined, 12);
                            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$1_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(`${result.name || result.id} 레이어 영역으로 지도를 이동했습니다`);
                        } else {
                            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$1_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(`${result.name || result.id} 레이어를 지도에 추가했습니다`);
                        }
                        break;
                    case "changeBasemap":
                        if (result.basemap && mapState?.view?.setBasemap) {
                            // 1. ODF 맵에서 배경지도 변경
                            mapState.view.setBasemap(result.basemap);
                            // 2. 전역 상태 업데이트 (ODF 콜백에서 자동으로 처리되지만 보험용)
                            setCurrentBasemap(result.basemap);
                            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$1_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(`배경지도가 변경되었습니다`);
                        }
                        break;
                    case "setMapCenter":
                        if (result.center && mapState?.view?.setCenter) {
                            const [longitude, latitude] = result.center;
                            mapState.view.setCenter([
                                longitude,
                                latitude
                            ]);
                            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$1_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(result.message || `지도 중심점이 이동되었습니다`);
                        }
                        break;
                    case "setMapZoom":
                        if (result.zoom !== undefined && mapState?.view?.setZoom) {
                            if (result.zoomType === "relative") {
                                // 상대적 확대/축소인 경우 현재 줌 레벨 기준으로 계산
                                const currentZoom = mapState.view.getZoom();
                                const zoomChange = result.zoomDirection === "in" ? result.zoom : -result.zoom;
                                const newZoom = Math.max(1, Math.min(20, currentZoom + zoomChange));
                                mapState.view.setZoom(newZoom);
                            } else {
                                // 절대적 확대/축소
                                mapState.view.setZoom(result.zoom);
                            }
                            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$1_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(result.message || `지도 확대/축소 레벨이 변경되었습니다`);
                        }
                        break;
                    case "moveMapByDirection":
                        if (result.deltaX !== undefined && result.deltaY !== undefined && mapState?.map?.setCenter && mapState?.map?.getCenter) {
                            const currentCenter = mapState.map.getCenter();
                            // EPSG:5186 좌표계에서는 X, Y 좌표를 직접 사용
                            const newCenter = [
                                currentCenter[0] + result.deltaX,
                                currentCenter[1] + result.deltaY // Y축 (남북 방향)
                            ];
                            mapState.map.setCenter(newCenter);
                            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$sonner$40$1$2e$7$2e$1_react$2d$dom$40$19$2e$0$2e$0_react$40$19$2e$0$2e$0_$5f$react$40$19$2e$0$2e$0$2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(result.message || `지도가 이동되었습니다`);
                        }
                        break;
                    default:
                        break;
                }
                processedSmartNavigationRef.current.add(smartNavKey);
            } catch (error) {
                console.error(`Error handling smart navigation for ${toolName}:`, error);
            }
        }
    }["ToolInvocationProvider.useCallback[handleSmartNavigation]"], [
        enableSmartNavigation,
        mapState
    ]);
    // Tool 결과를 파싱하여 resultMap 업데이트
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ToolInvocationProvider.useEffect": ()=>{
            const next = {};
            messages.forEach({
                "ToolInvocationProvider.useEffect": (msg)=>{
                    msg.parts?.forEach({
                        "ToolInvocationProvider.useEffect": (part)=>{
                            if (part.type !== "tool-invocation") return;
                            const { toolName, state } = part.toolInvocation;
                            if (state !== "result" || !('result' in part.toolInvocation)) return;
                            (next[toolName] ??= []).push(part.toolInvocation);
                        }
                    }["ToolInvocationProvider.useEffect"]);
                }
            }["ToolInvocationProvider.useEffect"]);
            setResultMap(next);
        }
    }["ToolInvocationProvider.useEffect"], [
        messages
    ]);
    // 메시지가 변경될 때 (이전 대화 로드 시) 처리 상태 초기화
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ToolInvocationProvider.useEffect": ()=>{
            const previousMessages = previousMessagesRef.current;
            // 새로운 대화를 불러온 경우를 감지
            const isNewConversation = // 이전에 메시지가 없었고 현재 메시지가 있는 경우 (초기 로드)
            previousMessages.length === 0 && messages.length > 0 || previousMessages.length > 0 && messages.length > 0 && previousMessages[0]?.id !== messages[0]?.id || previousMessages.length > messages.length && messages.length > 0;
            if (isNewConversation) {
                // 이전 대화를 불러올 때 처리 상태를 초기화하여 스타일 등이 다시 적용되도록 함
                processedToolCallsRef.current.clear();
                processedSmartNavigationRef.current.clear();
                console.log('🔄 New conversation detected - clearing processed tool calls for style reapplication');
            }
            // 현재 메시지 상태를 저장
            previousMessagesRef.current = [
                ...messages
            ];
        }
    }["ToolInvocationProvider.useEffect"], [
        messages
    ]);
    // Tool 결과를 기반으로 레이어 상태 업데이트
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ToolInvocationProvider.useEffect": ()=>{
            const newLayers = [];
            Object.entries(resultMap).forEach({
                "ToolInvocationProvider.useEffect": ([toolName, invocations])=>{
                    const transformer = __TURBOPACK__imported__module__$5b$project$5d2f$types$2f$layer$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["layerTransformers"][toolName];
                    invocations.forEach({
                        "ToolInvocationProvider.useEffect": (invocation)=>{
                            if (invocation.state === 'result' && 'result' in invocation) {
                                // 이미 처리된 tool call인지 확인
                                const toolCallKey = `${toolName}-${invocation.toolCallId}`;
                                if (processedToolCallsRef.current.has(toolCallKey)) {
                                    return; // 이미 처리됨, 건너뛰기
                                }
                                try {
                                    if (toolName === 'createLayerFilter') {
                                        // 필터 업데이트는 기존 레이어에 적용
                                        const filterResult = invocation.result;
                                        if (filterResult.lyr_id && filterResult.filter) {
                                            dispatch({
                                                type: 'UPDATE_FILTER',
                                                payload: {
                                                    id: filterResult.lyr_id,
                                                    filter: filterResult.filter
                                                }
                                            });
                                            processedToolCallsRef.current.add(toolCallKey);
                                        }
                                    } else if (toolName === 'updateLayerStyle') {
                                        // 스타일 업데이트는 기존 레이어에 적용
                                        const styleResult = invocation.result;
                                        console.log('Style result:', styleResult);
                                        if (styleResult.layerId && styleResult.styleUpdate && styleResult.success) {
                                            // 현재 레이어 정보 가져오기
                                            const currentLayer = layers.find({
                                                "ToolInvocationProvider.useEffect.currentLayer": (layer)=>layer.id === styleResult.layerId
                                            }["ToolInvocationProvider.useEffect.currentLayer"]);
                                            const layerInfo = currentLayer ? {
                                                geometryType: currentLayer.geometryType,
                                                currentStyle: currentLayer.style || currentLayer.renderOptions?.style
                                            } : undefined;
                                            console.log('Current layer info:', layerInfo);
                                            // 단순한 스타일 속성을 WMS 스타일 구조로 변환
                                            const convertedStyle = convertSimpleStyleToWMS(styleResult.styleUpdate, layerInfo);
                                            console.log('Converted style:', convertedStyle);
                                            dispatch({
                                                type: 'UPDATE_STYLE',
                                                payload: {
                                                    id: styleResult.layerId,
                                                    style: convertedStyle
                                                }
                                            });
                                            processedToolCallsRef.current.add(toolCallKey);
                                        }
                                    } else if (toolName === 'generateCategoricalStyle') {
                                        // 유형별 스타일 생성 결과 처리
                                        const categoricalResult = invocation.result;
                                        console.log('Categorical style result:', categoricalResult);
                                        if (categoricalResult.layerId && categoricalResult.styleRules && categoricalResult.success) {
                                            // 현재 레이어 정보 가져오기
                                            const currentLayer = layers.find({
                                                "ToolInvocationProvider.useEffect.currentLayer": (layer)=>layer.id === categoricalResult.layerId
                                            }["ToolInvocationProvider.useEffect.currentLayer"]);
                                            const geometryType = currentLayer?.geometryType || 'point';
                                            // 지오메트리 타입에 따른 심볼라이저 생성 함수
                                            const createSymbolizerForGeometry = {
                                                "ToolInvocationProvider.useEffect.createSymbolizerForGeometry": (color)=>{
                                                    if (geometryType === 'point' || geometryType === '1') {
                                                        return {
                                                            kind: 'Mark',
                                                            wellKnownName: 'circle',
                                                            radius: 6,
                                                            color: color,
                                                            fillOpacity: 1,
                                                            strokeColor: '#000000',
                                                            strokeWidth: 1,
                                                            strokeOpacity: 1
                                                        };
                                                    } else if (geometryType === 'line' || geometryType === '2') {
                                                        return {
                                                            kind: 'Line',
                                                            color: color,
                                                            width: 2,
                                                            opacity: 1,
                                                            cap: 'round',
                                                            join: 'round'
                                                        };
                                                    } else {
                                                        return {
                                                            kind: 'Fill',
                                                            color: color,
                                                            fillOpacity: 0.7,
                                                            outlineColor: '#000000',
                                                            outlineWidth: 1
                                                        };
                                                    }
                                                }
                                            }["ToolInvocationProvider.useEffect.createSymbolizerForGeometry"];
                                            // 단일 조건 필터 생성 함수
                                            const createSingleFilter = {
                                                "ToolInvocationProvider.useEffect.createSingleFilter": (condition, value, attributeName)=>{
                                                    switch(condition){
                                                        case 'like':
                                                            return [
                                                                '*=',
                                                                attributeName,
                                                                `*${value}*`
                                                            ];
                                                        case 'equal':
                                                            return [
                                                                '==',
                                                                attributeName,
                                                                value
                                                            ];
                                                        case 'greater':
                                                            return [
                                                                '>',
                                                                attributeName,
                                                                value
                                                            ];
                                                        case 'less':
                                                            return [
                                                                '<',
                                                                attributeName,
                                                                value
                                                            ];
                                                        case 'greaterEqual':
                                                            return [
                                                                '>=',
                                                                attributeName,
                                                                value
                                                            ];
                                                        case 'lessEqual':
                                                            return [
                                                                '<=',
                                                                attributeName,
                                                                value
                                                            ];
                                                        case 'default':
                                                        default:
                                                            return null; // 기본 스타일은 필터 없음
                                                    }
                                                }
                                            }["ToolInvocationProvider.useEffect.createSingleFilter"];
                                            // 복합 조건 필터 생성 함수
                                            const createComplexFilter = {
                                                "ToolInvocationProvider.useEffect.createComplexFilter": (conditions, logicalOperator = 'AND')=>{
                                                    if (!conditions || conditions.length === 0) {
                                                        return null; // 기본 스타일은 필터 없음
                                                    }
                                                    if (conditions.length === 1) {
                                                        const cond = conditions[0];
                                                        return createSingleFilter(cond.condition, cond.value, cond.attributeName);
                                                    }
                                                    // 두 개 이상의 조건 처리
                                                    const filterConditions = conditions.map({
                                                        "ToolInvocationProvider.useEffect.createComplexFilter.filterConditions": (cond)=>createSingleFilter(cond.condition, cond.value, cond.attributeName)
                                                    }["ToolInvocationProvider.useEffect.createComplexFilter.filterConditions"]).filter({
                                                        "ToolInvocationProvider.useEffect.createComplexFilter.filterConditions": (filter)=>filter !== null
                                                    }["ToolInvocationProvider.useEffect.createComplexFilter.filterConditions"]);
                                                    if (filterConditions.length === 0) {
                                                        return null;
                                                    }
                                                    if (filterConditions.length === 1) {
                                                        return filterConditions[0];
                                                    }
                                                    // 논리 연산자에 따라 필터 조합
                                                    const operator = logicalOperator === 'OR' ? '||' : '&&';
                                                    return [
                                                        operator,
                                                        ...filterConditions
                                                    ];
                                                }
                                            }["ToolInvocationProvider.useEffect.createComplexFilter"];
                                            // 규칙 순서 조정: default 규칙을 첫 번째로, 구체적인 조건들을 나중에
                                            const sortedStyleRules = [
                                                ...categoricalResult.styleRules
                                            ].sort({
                                                "ToolInvocationProvider.useEffect.sortedStyleRules": (a, b)=>{
                                                    // default 조건을 첫 번째로 (conditions가 비어있거나 default 조건이 있는 경우)
                                                    const aIsDefault = !a.conditions || a.conditions.length === 0 || a.conditions.some({
                                                        "ToolInvocationProvider.useEffect.sortedStyleRules": (cond)=>cond.condition === 'default'
                                                    }["ToolInvocationProvider.useEffect.sortedStyleRules"]);
                                                    const bIsDefault = !b.conditions || b.conditions.length === 0 || b.conditions.some({
                                                        "ToolInvocationProvider.useEffect.sortedStyleRules": (cond)=>cond.condition === 'default'
                                                    }["ToolInvocationProvider.useEffect.sortedStyleRules"]);
                                                    if (aIsDefault && !bIsDefault) return -1;
                                                    if (bIsDefault && !aIsDefault) return 1;
                                                    // 나머지는 원래 순서 유지
                                                    return 0;
                                                }
                                            }["ToolInvocationProvider.useEffect.sortedStyleRules"]);
                                            // 유형별 스타일 규칙들을 WMS SLD 형식으로 변환
                                            const sldRules = sortedStyleRules.map({
                                                "ToolInvocationProvider.useEffect.sldRules": (rule, index)=>{
                                                    // AI가 생성한 헥스 색상 그대로 사용
                                                    const hexColor = rule.color || '#808080';
                                                    const sldRule = {
                                                        name: rule.description || `Rule ${index + 1}`,
                                                        symbolizers: [
                                                            createSymbolizerForGeometry(hexColor)
                                                        ]
                                                    };
                                                    // 복합 조건 필터 생성
                                                    const filter = createComplexFilter(rule.conditions, rule.logicalOperator);
                                                    if (filter) {
                                                        sldRule.filter = filter;
                                                    }
                                                    return sldRule;
                                                }
                                            }["ToolInvocationProvider.useEffect.sldRules"]);
                                            const wmsStyle = {
                                                rules: sldRules
                                            };
                                            console.log('Converted categorical style:', wmsStyle);
                                            dispatch({
                                                type: 'UPDATE_STYLE',
                                                payload: {
                                                    id: categoricalResult.layerId,
                                                    style: wmsStyle
                                                }
                                            });
                                            processedToolCallsRef.current.add(toolCallKey);
                                        }
                                    } else if (toolName === 'removeLayer') {
                                        // 레이어 삭제 처리
                                        const removeResult = invocation.result;
                                        console.log('Remove layer result:', removeResult);
                                        if (removeResult.layerId && removeResult.success) {
                                            dispatch({
                                                type: 'REMOVE_LAYER',
                                                payload: removeResult.layerId
                                            });
                                            processedToolCallsRef.current.add(toolCallKey);
                                        }
                                    } else if (transformer) {
                                        // transformer 사용 (mapState 전달)
                                        const layerProps = transformer(invocation.result, invocation.toolCallId, mapState);
                                        // 스마트 네비게이션이 비활성화된 경우 레이어를 숨김 상태로 생성
                                        if (!enableSmartNavigation) {
                                            layerProps.visible = false;
                                            console.log('🚫 Smart navigation disabled - hiding layer:', layerProps.id);
                                        } else {
                                            console.log('✅ Smart navigation enabled - showing layer:', layerProps.id);
                                        }
                                        newLayers.push(layerProps);
                                        processedToolCallsRef.current.add(toolCallKey);
                                        // 스마트 네비게이션 수행 (새로운 도구 결과에 대해서만 1회 실행)
                                        handleSmartNavigation(toolName, invocation.result, invocation.toolCallId);
                                    } else {
                                        // 레이어를 생성하지 않는 도구들에 대한 스마트 네비게이션 처리
                                        handleSmartNavigation(toolName, invocation.result, invocation.toolCallId);
                                        processedToolCallsRef.current.add(toolCallKey);
                                    }
                                } catch (error) {
                                    console.error(`Error transforming ${toolName} result:`, error);
                                }
                            }
                        }
                    }["ToolInvocationProvider.useEffect"]);
                }
            }["ToolInvocationProvider.useEffect"]);
            // 새 레이어들 추가
            newLayers.forEach({
                "ToolInvocationProvider.useEffect": (layer)=>{
                    dispatch({
                        type: 'ADD_LAYER',
                        payload: layer
                    });
                }
            }["ToolInvocationProvider.useEffect"]);
        }
    }["ToolInvocationProvider.useEffect"], [
        resultMap,
        enableSmartNavigation,
        mapState
    ]);
    // 스마트 네비게이션 상태 변경 시 기존 레이어들의 가시성 업데이트
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ToolInvocationProvider.useEffect": ()=>{
            layers.forEach({
                "ToolInvocationProvider.useEffect": (layer)=>{
                    // 도구 결과로 생성된 레이어들만 제어 (toolCallId가 있는 레이어)
                    // 단, 사용자가 수동으로 수정한 레이어는 제외
                    if (layer.toolCallId && !layer.userModified) {
                        const shouldBeVisible = enableSmartNavigation;
                        if (layer.visible !== shouldBeVisible) {
                            console.log(`🔄 Auto-updating layer visibility: ${layer.id} -> ${shouldBeVisible} (smart navigation)`);
                            dispatch({
                                type: 'UPDATE_LAYER',
                                payload: {
                                    id: layer.id,
                                    updates: {
                                        visible: shouldBeVisible
                                    }
                                }
                            });
                        }
                    } else if (layer.userModified) {
                        console.log(`⏭️ Skipping auto-update for user-modified layer: ${layer.id}`);
                    }
                }
            }["ToolInvocationProvider.useEffect"]);
        }
    }["ToolInvocationProvider.useEffect"], [
        enableSmartNavigation,
        layers
    ]);
    // 컴포넌트 언마운트 시 타이머 정리
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ToolInvocationProvider.useEffect": ()=>{
            return ({
                "ToolInvocationProvider.useEffect": ()=>{
                    if (addressBatchTimeoutRef.current) {
                        clearTimeout(addressBatchTimeoutRef.current);
                        addressBatchTimeoutRef.current = null;
                    }
                    pendingAddressesRef.current = [];
                }
            })["ToolInvocationProvider.useEffect"];
        }
    }["ToolInvocationProvider.useEffect"], []);
    // 레이어 관리 컨텍스트 값
    const layerManagerValue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "ToolInvocationProvider.useMemo[layerManagerValue]": ()=>({
                layers,
                addLayer: ({
                    "ToolInvocationProvider.useMemo[layerManagerValue]": (layer)=>dispatch({
                            type: 'ADD_LAYER',
                            payload: layer
                        })
                })["ToolInvocationProvider.useMemo[layerManagerValue]"],
                updateLayer: ({
                    "ToolInvocationProvider.useMemo[layerManagerValue]": (id, updates)=>dispatch({
                            type: 'UPDATE_LAYER',
                            payload: {
                                id,
                                updates
                            }
                        })
                })["ToolInvocationProvider.useMemo[layerManagerValue]"],
                removeLayer: ({
                    "ToolInvocationProvider.useMemo[layerManagerValue]": (id)=>dispatch({
                            type: 'REMOVE_LAYER',
                            payload: id
                        })
                })["ToolInvocationProvider.useMemo[layerManagerValue]"],
                toggleVisibility: ({
                    "ToolInvocationProvider.useMemo[layerManagerValue]": (id)=>dispatch({
                            type: 'TOGGLE_VISIBILITY',
                            payload: id
                        })
                })["ToolInvocationProvider.useMemo[layerManagerValue]"],
                updateFilter: ({
                    "ToolInvocationProvider.useMemo[layerManagerValue]": (id, filter)=>dispatch({
                            type: 'UPDATE_FILTER',
                            payload: {
                                id,
                                filter
                            }
                        })
                })["ToolInvocationProvider.useMemo[layerManagerValue]"],
                updateStyle: ({
                    "ToolInvocationProvider.useMemo[layerManagerValue]": (id, style)=>dispatch({
                            type: 'UPDATE_STYLE',
                            payload: {
                                id,
                                style
                            }
                        })
                })["ToolInvocationProvider.useMemo[layerManagerValue]"],
                updateZIndex: ({
                    "ToolInvocationProvider.useMemo[layerManagerValue]": (id, zIndex)=>dispatch({
                            type: 'UPDATE_Z_INDEX',
                            payload: {
                                id,
                                zIndex
                            }
                        })
                })["ToolInvocationProvider.useMemo[layerManagerValue]"],
                reorderLayers: ({
                    "ToolInvocationProvider.useMemo[layerManagerValue]": (layerIds)=>dispatch({
                            type: 'REORDER_LAYERS',
                            payload: {
                                layerIds
                            }
                        })
                })["ToolInvocationProvider.useMemo[layerManagerValue]"],
                getLayerById: ({
                    "ToolInvocationProvider.useMemo[layerManagerValue]": (id)=>layers.find({
                            "ToolInvocationProvider.useMemo[layerManagerValue]": (layer)=>layer.id === id
                        }["ToolInvocationProvider.useMemo[layerManagerValue]"])
                })["ToolInvocationProvider.useMemo[layerManagerValue]"]
            })
    }["ToolInvocationProvider.useMemo[layerManagerValue]"], [
        layers
    ]);
    // 위치 컨텍스트 값
    const locationValue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "ToolInvocationProvider.useMemo[locationValue]": ()=>({
                currentLocation,
                setCurrentLocation,
                originPoint,
                setOriginPoint,
                destinationPoint,
                setDestinationPoint
            })
    }["ToolInvocationProvider.useMemo[locationValue]"], [
        currentLocation,
        originPoint,
        destinationPoint
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ToolContext.Provider, {
        value: resultMap,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(LayerManagerContext.Provider, {
            value: layerManagerValue,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(LocationContext.Provider, {
                value: locationValue,
                children: children
            }, void 0, false, {
                fileName: "[project]/providers/tool-invocation-provider.tsx",
                lineNumber: 856,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/providers/tool-invocation-provider.tsx",
            lineNumber: 855,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/providers/tool-invocation-provider.tsx",
        lineNumber: 854,
        columnNumber: 5
    }, this);
}
_s(ToolInvocationProvider, "dnclKHuf/Xdp0+zhZAPphVjzeJE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$providers$2f$basemap$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useBasemap"]
    ];
});
_c = ToolInvocationProvider;
function useToolResults(toolName, transform) {
    _s1();
    const map = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(ToolContext);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useToolResults.useMemo": ()=>(map[toolName] ?? []).filter({
                "useToolResults.useMemo": (ti)=>ti.state === "result" && 'result' in ti
            }["useToolResults.useMemo"]).map({
                "useToolResults.useMemo": (ti)=>transform ? transform(ti.result) : ti.result
            }["useToolResults.useMemo"])
    }["useToolResults.useMemo"], [
        map,
        toolName,
        transform
    ]);
}
_s1(useToolResults, "0tRW3h4ltjLAEd3u4z6tYVPac80=");
function useLayerManager() {
    _s2();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(LayerManagerContext);
    if (!context) {
        throw new Error('useLayerManager must be used within a ToolInvocationProvider');
    }
    return context;
}
_s2(useLayerManager, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
function useLayerById(id) {
    _s3();
    const { getLayerById } = useLayerManager();
    return getLayerById(id);
}
_s3(useLayerById, "PK19gJOZ/WOWru5OZBMsAHDPaKw=", false, function() {
    return [
        useLayerManager
    ];
});
function useFilteredLayers() {
    _s4();
    const { layers } = useLayerManager();
    return layers.filter((layer)=>layer.filter);
}
_s4(useFilteredLayers, "fTHJF86XHWpcPwJBp5EHemdfnhU=", false, function() {
    return [
        useLayerManager
    ];
});
function useLocation() {
    _s5();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(LocationContext);
    if (!context) {
        throw new Error('useLocation must be used within a ToolInvocationProvider');
    }
    return context;
}
_s5(useLocation, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_context__.k.register(_c, "ToolInvocationProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/types/layer-manager.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "layerTransformers": (()=>layerTransformers)
});
const layerTransformers = {
    getLayer: (result, toolCallId, mapState)=>{
        // 서울 건물통합정보 레이어인 경우 autoFit을 false로 설정 (수동으로 지도 이동)
        const isSeoulBuildingLayer = result.lyrId === "LR0000004299" || result.name === "GIS건물통합정보_서울" || result.lyrNm === "GIS건물통합정보_서울";
        return {
            id: result.lyrId || result.id || toolCallId,
            name: result.lyrNm || result.name,
            type: result.type || 'geoserver',
            service: result.service,
            server: result.server,
            layer: result.layer,
            crtfckey: result.crtfckey,
            bbox: result.bbox,
            autoFit: isSeoulBuildingLayer ? false : true,
            method: result.method,
            projection: result.projection,
            geometryType: result.geometryType,
            serviceTy: result.serviceTy,
            visible: result.visible !== false,
            opacity: result.opacity ?? 1,
            zIndex: result.zIndex ?? 0,
            filter: result.filter,
            style: result.style,
            source: 'tool-result',
            toolCallId,
            info: result.info || {
                lyrId: result.lyrId || result.id || toolCallId,
                lyrNm: result.lyrNm || result.name || 'Unknown Layer',
                description: result.description,
                metadata: result.metadata
            }
        };
    },
    performDensityAnalysis: (result, toolCallId)=>({
            id: `density-${toolCallId}`,
            name: `밀도 분석 - ${result.layerName || 'Unknown'}`,
            type: "geojson",
            data: {
                ...result
            },
            service: "heatmap",
            dataProjectionCode: "EPSG:5186",
            featureProjectionCode: "EPSG:5179",
            visible: true,
            opacity: 0.8,
            zIndex: 100,
            source: 'tool-result',
            toolCallId,
            info: {
                lyrId: `density-${toolCallId}`,
                lyrNm: `밀도 분석 - ${result.layerName || 'Unknown'}`,
                description: '밀도 분석 결과 레이어'
            }
        }),
    searchDirections: (result, toolCallId, mapState)=>{
        const route = result.routes?.[0];
        // 거리와 시간 정보 포맷팅
        const distanceText = route?.summary?.distance ? `${(route.summary.distance / 1000).toFixed(1)}km` : '거리 정보 없음';
        const durationText = route?.summary?.duration ? `${Math.floor(route.summary.duration / 60)}분` : '시간 정보 없음';
        // 기본 레이어 속성 설정
        const layerProps = {
            id: `route-${toolCallId}`,
            name: `경로 - ${distanceText}`,
            type: "geojson",
            visible: true,
            opacity: 0.8,
            zIndex: 50,
            source: 'tool-result',
            toolCallId,
            style: {
                stroke: {
                    color: '#2563eb',
                    width: 8
                },
                fill: {
                    color: [
                        255,
                        255,
                        255,
                        0.4
                    ]
                }
            },
            info: {
                lyrId: `route-${toolCallId}`,
                lyrNm: `경로 - ${distanceText}`,
                description: `길찾기 결과 경로 (${durationText})`
            }
        };
        // mapState가 있고 유효한 경로 데이터가 있는 경우 GeoJSON 생성
        if (mapState?.map && route && route.result_code === 0 && route.sections) {
            try {
                const coordinates = [];
                const projection = mapState.map.getProjection();
                // 모든 섹션의 도로 정보에서 좌표 추출 및 변환
                route.sections.forEach((section)=>{
                    section.roads?.forEach((road)=>{
                        if (road.vertexes && Array.isArray(road.vertexes) && road.vertexes.length >= 2) {
                            // vertexes는 [x1, y1, x2, y2, ...] 형태의 배열
                            for(let i = 0; i < road.vertexes.length; i += 2){
                                const lon = road.vertexes[i];
                                const lat = road.vertexes[i + 1];
                                if (typeof lon === 'number' && typeof lat === 'number') {
                                    // 좌표계 변환: EPSG:4326 -> 지도 좌표계
                                    const projectedCoord = projection.project([
                                        lon,
                                        lat
                                    ], "4326");
                                    if (Array.isArray(projectedCoord) && projectedCoord.length >= 2) {
                                        coordinates.push([
                                            projectedCoord[0],
                                            projectedCoord[1]
                                        ]);
                                    }
                                }
                            }
                        }
                    });
                });
                // GeoJSON LineString 생성 (타입 안전성 보장)
                if (coordinates.length > 0) {
                    const geoJsonData = {
                        type: "FeatureCollection",
                        features: [
                            {
                                type: "Feature",
                                geometry: {
                                    type: "LineString",
                                    coordinates: coordinates
                                },
                                properties: {
                                    name: "경로",
                                    distance: route.summary?.distance || 0,
                                    duration: route.summary?.duration || 0,
                                    toolCallId
                                }
                            }
                        ]
                    };
                    layerProps.data = geoJsonData;
                }
            } catch (error) {
                layerProps.data = result;
            }
        } else {
            // mapState가 없거나 유효하지 않은 경우 원본 데이터 사용
            layerProps.data = result;
        }
        return layerProps;
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/types/layer-style.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// WMS/WFS 레이어 스타일 타입 정의
// 기본 필터 타입
__turbopack_context__.s({
    "createDefaultLineStyle": (()=>createDefaultLineStyle),
    "createDefaultPointStyle": (()=>createDefaultPointStyle),
    "createDefaultPolygonStyle": (()=>createDefaultPolygonStyle),
    "createDefaultWFSLineStyle": (()=>createDefaultWFSLineStyle),
    "createDefaultWFSPointStyle": (()=>createDefaultWFSPointStyle),
    "createDefaultWFSPolygonStyle": (()=>createDefaultWFSPolygonStyle)
});
const createDefaultPointStyle = (options)=>({
        rules: [
            {
                name: 'Default Point Rule',
                symbolizers: [
                    {
                        kind: 'Mark',
                        wellKnownName: 'circle',
                        radius: 6,
                        color: '#FF0000',
                        fillOpacity: 0.8,
                        strokeColor: '#000000',
                        strokeWidth: 1,
                        strokeOpacity: 1,
                        ...options
                    }
                ]
            }
        ]
    });
const createDefaultLineStyle = (options)=>({
        rules: [
            {
                name: 'Default Line Rule',
                symbolizers: [
                    {
                        kind: 'Line',
                        color: '#0000FF',
                        width: 2,
                        opacity: 1,
                        cap: 'round',
                        join: 'round',
                        ...options
                    }
                ]
            }
        ]
    });
const createDefaultPolygonStyle = (options)=>({
        rules: [
            {
                name: 'Default Polygon Rule',
                symbolizers: [
                    {
                        kind: 'Fill',
                        color: '#AAAAAA',
                        fillOpacity: 0.5,
                        outlineColor: '#000000',
                        outlineWidth: 1,
                        outlineOpacity: 1,
                        ...options
                    }
                ]
            }
        ]
    });
const createDefaultWFSPointStyle = (options)=>({
        'circle-radius': 6,
        'circle-fill-color': '#FF0000',
        'circle-fill-opacity': 0.8,
        'circle-stroke-color': '#000000',
        'circle-stroke-width': 1,
        'circle-stroke-opacity': 1,
        ...options
    });
const createDefaultWFSLineStyle = (options)=>({
        'stroke-color': '#0000FF',
        'stroke-width': 2,
        'stroke-opacity': 1,
        ...options
    });
const createDefaultWFSPolygonStyle = (options)=>({
        'fill-color': '#00FF00',
        'fill-opacity': 0.5,
        'stroke-color': '#000000',
        'stroke-width': 1,
        'stroke-opacity': 1,
        ...options
    });
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/map-utils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
//@ts-nocheck
__turbopack_context__.s({
    "addLayerToMap": (()=>addLayerToMap),
    "calculateZoomLevel": (()=>calculateZoomLevel),
    "clearMapHighlights": (()=>clearMapHighlights),
    "highlightPointOnMap": (()=>highlightPointOnMap),
    "removeHighlightLayer": (()=>removeHighlightLayer),
    "showRouteOnMap": (()=>showRouteOnMap)
});
// 하이라이트 레이어 관리를 위한 전역 변수
let HIGHLIGHT_LAYER_ID = null;
const highlightPointOnMap = (mapState, longitude, latitude, geometry, zoomLevel = 16, options)=>{
    if (!mapState?.map) {
        return false;
    }
    try {
        const odf = window.odf;
        if (!odf) {
            return false;
        }
        // 하이라이트 레이어 재사용 또는 생성 (레거시 패턴)
        let highlightLayer = HIGHLIGHT_LAYER_ID ? mapState.map.findLayer(HIGHLIGHT_LAYER_ID) : null;
        if (!highlightLayer) {
            highlightLayer = odf.LayerFactory.produce("empty", {});
            highlightLayer.setMap(mapState.map);
            HIGHLIGHT_LAYER_ID = highlightLayer.getODFId();
            // 기본 스타일 적용
            const defaultStyle = options?.customStyle || {
                image: {
                    circle: {
                        radius: 10,
                        fill: {
                            color: [
                                255,
                                255,
                                255,
                                0.4
                            ]
                        },
                        stroke: {
                            color: [
                                237,
                                116,
                                116,
                                0.82
                            ],
                            width: 2
                        }
                    }
                },
                fill: {
                    color: [
                        255,
                        255,
                        255,
                        0.4
                    ]
                },
                stroke: {
                    color: [
                        237,
                        116,
                        116,
                        0.82
                    ],
                    width: 2
                }
            };
            highlightLayer.setStyle(odf.StyleFactory.produce(defaultStyle));
        }
        // 기존 하이라이트 제거 (옵션)
        if (options?.clearPrevious !== false) {
            highlightLayer.clear();
        }
        // 지오메트리가 있는 경우 사용, 없으면 포인트 생성
        let wkt = geometry;
        if (!wkt) {
            wkt = `POINT(${longitude} ${latitude})`;
        }
        // WKT에서 피처 생성
        let feature = odf.FeatureFactory.fromWKT(wkt);
        // 좌표계 변환 처리 (레거시 패턴)
        const mapProjectionCode = mapState.map.getView().getProjection().getCode();
        const targetSrid = mapProjectionCode.replace(/[^0-9]/gi, "");
        if (targetSrid !== "4326") {
            feature = mapState.map.getProjection().projectGeom(feature, "4326");
        }
        highlightLayer.addFeature(feature);
        // Z-Index 관리 (레거시 패턴)
        if (options?.useZIndexUp) {
            const maxZIndex = mapState.map.getMaxZIndex();
            mapState.map.setZIndex(HIGHLIGHT_LAYER_ID, maxZIndex + 1);
        }
        // 지오메트리 타입별 줌 레벨 조정 (레거시 패턴 개선)
        const geometryType = feature.getGeometry().getType();
        if (geometry) {
            // 복잡한 지오메트리는 fit 사용
            const padding = options?.fitPadding || 1000;
            highlightLayer.fit();
        }
        mapState.map.setZoom(mapState.map.getZoom() > 13 ? 13 : mapState.map.getZoom()); //바로 e맵 영상 최대 레벨 19
        return true;
    } catch (error) {
        console.error("지도 하이라이트 중 오류:", error);
        return false;
    }
};
const showRouteOnMap = (mapState, origin, destination, waypoints, routeData // 실제 경로 데이터 (sections 포함)
)=>{
    if (!mapState?.map) {
        return false;
    }
    try {
        const odf = window.odf;
        if (!odf) {
            return false;
        }
        // 하이라이트 레이어 재사용 또는 생성
        let highlightLayer = HIGHLIGHT_LAYER_ID ? mapState.map.findLayer(HIGHLIGHT_LAYER_ID) : null;
        if (!highlightLayer) {
            highlightLayer = odf.LayerFactory.produce("empty", {});
            highlightLayer.setMap(mapState.map);
            HIGHLIGHT_LAYER_ID = highlightLayer.getODFId();
        }
        // 기존 하이라이트 제거
        highlightLayer.clear();
        const projection = mapState.map.getProjection();
        // 실제 경로 라인 그리기 (제공된 예제 참고)
        if (routeData && routeData.routes && routeData.routes[0] && routeData.routes[0].sections) {
            const coordinates = [];
            // 첫 번째 섹션의 모든 도로를 순회
            routeData.routes[0].sections.forEach((section)=>{
                if (section.roads) {
                    section.roads.forEach((router)=>{
                        router.vertexes.forEach((vertex, index)=>{
                            if (index % 2 === 0) {
                                coordinates.push(projection.project([
                                    router.vertexes[index],
                                    router.vertexes[index + 1]
                                ], "4326"));
                            }
                        });
                    });
                }
            });
            // GeoJSON LineString으로 경로 표시
            if (coordinates.length > 1) {
                const lineWKT = `LINESTRING(${coordinates.map((coord)=>`${coord[0]} ${coord[1]}`).join(", ")})`;
                let lineFeature = odf.FeatureFactory.fromWKT(lineWKT);
                highlightLayer.addFeature(lineFeature);
            }
        }
        // 출발지 표시 (파란색 원)
        const originWKT = `POINT(${origin.x} ${origin.y})`;
        let originFeature = odf.FeatureFactory.fromWKT(originWKT);
        originFeature = projection.projectGeom(originFeature, "4326");
        highlightLayer.addFeature(originFeature);
        // 도착지 표시 (빨간색 원)
        const destWKT = `POINT(${destination.x} ${destination.y})`;
        let destFeature = odf.FeatureFactory.fromWKT(destWKT);
        destFeature = projection.projectGeom(destFeature, "4326");
        highlightLayer.addFeature(destFeature);
        // 경유지가 있는 경우 표시
        if (waypoints && waypoints.length > 0) {
            waypoints.forEach((waypoint)=>{
                const waypointWKT = `POINT(${waypoint.x} ${waypoint.y})`;
                let waypointFeature = odf.FeatureFactory.fromWKT(waypointWKT);
                waypointFeature = projection.projectGeom(waypointFeature, "4326");
                highlightLayer.addFeature(waypointFeature);
            });
        }
        // 전체 경로가 보이도록 fit
        highlightLayer.fit();
        return true;
    } catch (error) {
        console.error("경로 표시 중 오류:", error);
        return false;
    }
};
const addLayerToMap = async (mapState, layerInfo, layerConfig)=>{
    if (!mapState?.layer) {
        return false;
    }
    try {
        const addedLayer = await mapState.layer.add(layerInfo, layerConfig);
        return !!addedLayer;
    } catch (error) {
        console.error("레이어 추가 중 오류:", error);
        return false;
    }
};
const calculateZoomLevel = (distance)=>{
    if (distance > 50000) return 8;
    if (distance > 20000) return 10;
    if (distance > 5000) return 12;
    if (distance > 1000) return 14;
    return 16;
};
const clearMapHighlights = (mapState)=>{
    if (!mapState?.map) {
        return false;
    }
    try {
        if (HIGHLIGHT_LAYER_ID) {
            const highlightLayer = mapState.map.findLayer(HIGHLIGHT_LAYER_ID);
            if (highlightLayer) {
                highlightLayer.clear();
            }
        }
        return true;
    } catch (error) {
        console.error("하이라이트 초기화 중 오류:", error);
        return false;
    }
};
const removeHighlightLayer = (mapState)=>{
    if (!mapState?.map) {
        return false;
    }
    try {
        if (HIGHLIGHT_LAYER_ID) {
            const highlightLayer = mapState.map.findLayer(HIGHLIGHT_LAYER_ID);
            if (highlightLayer) {
                highlightLayer.removeMap(mapState.map);
                HIGHLIGHT_LAYER_ID = null;
            }
        }
        return true;
    } catch (error) {
        console.error("하이라이트 레이어 제거 중 오류:", error);
        return false;
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/hooks/use-scroll-to-bottom.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useScrollToBottom": (()=>useScrollToBottom)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.3_@opentelemetry+_a8516a9e8193861921635ddb2ae04d26/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
;
function useScrollToBottom() {
    _s();
    const containerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const endRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const shouldScrollRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(true);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useScrollToBottom.useEffect": ()=>{
            const container = containerRef.current;
            const end = endRef.current;
            if (container && end) {
                // Initial scroll
                end.scrollIntoView({
                    behavior: "instant",
                    block: "end"
                });
                // Check if user has scrolled up
                const handleScroll = {
                    "useScrollToBottom.useEffect.handleScroll": ()=>{
                        if (!container) return;
                        const isAtBottom = Math.abs(container.scrollHeight - container.scrollTop - container.clientHeight) < 10;
                        shouldScrollRef.current = isAtBottom;
                    }
                }["useScrollToBottom.useEffect.handleScroll"];
                const observer = new MutationObserver({
                    "useScrollToBottom.useEffect": ()=>{
                        // Only scroll if we're at the bottom or it's a new message
                        if (shouldScrollRef.current) {
                            end.scrollIntoView({
                                behavior: "instant",
                                block: "end"
                            });
                        }
                    }
                }["useScrollToBottom.useEffect"]);
                observer.observe(container, {
                    childList: true,
                    subtree: true,
                    characterData: true
                });
                // Add scroll listener
                container.addEventListener('scroll', handleScroll);
                return ({
                    "useScrollToBottom.useEffect": ()=>{
                        observer.disconnect();
                        container.removeEventListener('scroll', handleScroll);
                    }
                })["useScrollToBottom.useEffect"];
            }
        }
    }["useScrollToBottom.useEffect"], []);
    return [
        containerRef,
        endRef
    ];
}
_s(useScrollToBottom, "U5QtxxWN3VAP83/5XNfG9ZBoMos=");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/hooks/use-preview.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "usePreview": (()=>usePreview)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.3_@opentelemetry+_a8516a9e8193861921635ddb2ae04d26/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$swr$40$2$2e$2$2e$5_react$40$19$2e$0$2e$0$2f$node_modules$2f$swr$2f$dist$2f$core$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/swr@2.2.5_react@19.0.0/node_modules/swr/dist/core/index.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$swr$40$2$2e$2$2e$5_react$40$19$2e$0$2e$0$2f$node_modules$2f$swr$2f$dist$2f$core$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/swr@2.2.5_react@19.0.0/node_modules/swr/dist/core/index.mjs [app-client] (ecmascript) <locals>");
var _s = __turbopack_context__.k.signature();
;
;
const initialPreviewData = {
    documentId: 'init',
    content: '',
    kind: 'text',
    title: '',
    status: 'idle',
    isVisible: false,
    boundingBox: {
        top: 0,
        left: 0,
        width: 0,
        height: 0
    }
};
function usePreview() {
    _s();
    const { data: localPreview, mutate: setLocalPreview } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$swr$40$2$2e$2$2e$5_react$40$19$2e$0$2e$0$2f$node_modules$2f$swr$2f$dist$2f$core$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"])('preview', null, {
        fallbackData: initialPreviewData
    });
    const preview = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "usePreview.useMemo[preview]": ()=>{
            if (!localPreview) return initialPreviewData;
            return localPreview;
        }
    }["usePreview.useMemo[preview]"], [
        localPreview
    ]);
    const setPreview = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "usePreview.useCallback[setPreview]": (updaterFn)=>{
            setLocalPreview({
                "usePreview.useCallback[setPreview]": (currentPreview)=>{
                    const previewToUpdate = currentPreview || initialPreviewData;
                    if (typeof updaterFn === 'function') {
                        return updaterFn(previewToUpdate);
                    }
                    return updaterFn;
                }
            }["usePreview.useCallback[setPreview]"]);
        }
    }["usePreview.useCallback[setPreview]"], [
        setLocalPreview
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "usePreview.useMemo": ()=>({
                preview,
                setPreview
            })
    }["usePreview.useMemo"], [
        preview,
        setPreview
    ]);
}
_s(usePreview, "L1HM/G8egCHHQn28sGL5QKwirDo=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$swr$40$2$2e$2$2e$5_react$40$19$2e$0$2e$0$2f$node_modules$2f$swr$2f$dist$2f$core$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/hooks/use-user-message-id.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useUserMessageId": (()=>useUserMessageId)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$swr$40$2$2e$2$2e$5_react$40$19$2e$0$2e$0$2f$node_modules$2f$swr$2f$dist$2f$core$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/swr@2.2.5_react@19.0.0/node_modules/swr/dist/core/index.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$swr$40$2$2e$2$2e$5_react$40$19$2e$0$2e$0$2f$node_modules$2f$swr$2f$dist$2f$core$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/swr@2.2.5_react@19.0.0/node_modules/swr/dist/core/index.mjs [app-client] (ecmascript) <locals>");
var _s = __turbopack_context__.k.signature();
'use client';
;
function useUserMessageId() {
    _s();
    const { data: userMessageIdFromServer, mutate: setUserMessageIdFromServer } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$swr$40$2$2e$2$2e$5_react$40$19$2e$0$2e$0$2f$node_modules$2f$swr$2f$dist$2f$core$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"])('userMessageIdFromServer', null);
    return {
        userMessageIdFromServer,
        setUserMessageIdFromServer
    };
}
_s(useUserMessageId, "ACJgNFW44g+hX4cAH2Vg3eWZffQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$swr$40$2$2e$2$2e$5_react$40$19$2e$0$2e$0$2f$node_modules$2f$swr$2f$dist$2f$core$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/design-tokens.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * 전문적인 디자인 토큰 시스템
 * 일관성 있는 UI/UX를 위한 디자인 시스템
 */ __turbopack_context__.s({
    "componentStyles": (()=>componentStyles),
    "designTokens": (()=>designTokens),
    "getColorClass": (()=>getColorClass),
    "getSpacingClass": (()=>getSpacingClass)
});
const designTokens = {
    // 색상 시스템 - 전문적이고 세련된 팔레트
    colors: {
        // Primary - 신뢰감 있는 블루 계열
        primary: {
            50: '#f0f9ff',
            100: '#e0f2fe',
            200: '#bae6fd',
            300: '#7dd3fc',
            400: '#38bdf8',
            500: '#0ea5e9',
            600: '#0284c7',
            700: '#0369a1',
            800: '#075985',
            900: '#0c4a6e'
        },
        // Success - 자연스러운 그린
        success: {
            50: '#f0fdf4',
            100: '#dcfce7',
            200: '#bbf7d0',
            300: '#86efac',
            400: '#4ade80',
            500: '#22c55e',
            600: '#16a34a',
            700: '#15803d',
            800: '#166534',
            900: '#14532d'
        },
        // Warning - 따뜻한 오렌지
        warning: {
            50: '#fffbeb',
            100: '#fef3c7',
            200: '#fde68a',
            300: '#fcd34d',
            400: '#fbbf24',
            500: '#f59e0b',
            600: '#d97706',
            700: '#b45309',
            800: '#92400e',
            900: '#78350f'
        },
        // Error - 부드러운 레드
        error: {
            50: '#fef2f2',
            100: '#fee2e2',
            200: '#fecaca',
            300: '#fca5a5',
            400: '#f87171',
            500: '#ef4444',
            600: '#dc2626',
            700: '#b91c1c',
            800: '#991b1b',
            900: '#7f1d1d'
        },
        // Neutral - 세련된 그레이 스케일
        neutral: {
            50: '#fafafa',
            100: '#f5f5f5',
            200: '#e5e5e5',
            300: '#d4d4d4',
            400: '#a3a3a3',
            500: '#737373',
            600: '#525252',
            700: '#404040',
            800: '#262626',
            900: '#171717'
        }
    },
    // 타이포그래피 시스템
    typography: {
        fontFamily: {
            sans: [
                'Inter',
                'system-ui',
                'sans-serif'
            ],
            mono: [
                'JetBrains Mono',
                'monospace'
            ]
        },
        fontSize: {
            xs: [
                '0.75rem',
                {
                    lineHeight: '1rem'
                }
            ],
            sm: [
                '0.875rem',
                {
                    lineHeight: '1.25rem'
                }
            ],
            base: [
                '1rem',
                {
                    lineHeight: '1.5rem'
                }
            ],
            lg: [
                '1.125rem',
                {
                    lineHeight: '1.75rem'
                }
            ],
            xl: [
                '1.25rem',
                {
                    lineHeight: '1.75rem'
                }
            ],
            '2xl': [
                '1.5rem',
                {
                    lineHeight: '2rem'
                }
            ],
            '3xl': [
                '1.875rem',
                {
                    lineHeight: '2.25rem'
                }
            ]
        },
        fontWeight: {
            normal: '400',
            medium: '500',
            semibold: '600',
            bold: '700'
        }
    },
    // 간격 시스템
    spacing: {
        xs: '0.25rem',
        sm: '0.5rem',
        md: '0.75rem',
        lg: '1rem',
        xl: '1.5rem',
        '2xl': '2rem',
        '3xl': '3rem'
    },
    // 반지름 시스템
    borderRadius: {
        sm: '0.375rem',
        md: '0.5rem',
        lg: '0.75rem',
        xl: '1rem',
        '2xl': '1.5rem'
    },
    // 그림자 시스템 - 깊이감 있는 전문적 그림자
    shadows: {
        sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
        md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
        lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
        xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
        inner: 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)'
    },
    // 애니메이션 시스템
    animation: {
        duration: {
            fast: '150ms',
            normal: '200ms',
            slow: '300ms'
        },
        easing: {
            ease: 'cubic-bezier(0.4, 0, 0.2, 1)',
            easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
            easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
            easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)'
        }
    }
};
const componentStyles = {
    // 카드 스타일
    card: {
        base: `
      bg-white/80 backdrop-blur-sm border border-neutral-200/60 
      rounded-xl shadow-md hover:shadow-lg transition-all duration-200
    `,
        interactive: `
      hover:border-neutral-300/80 hover:-translate-y-0.5 
      cursor-pointer active:scale-[0.98]
    `,
        success: `
      bg-gradient-to-br from-success-50/90 to-success-100/70 
      border-success-200/60 hover:border-success-300/80
    `,
        warning: `
      bg-gradient-to-br from-warning-50/90 to-warning-100/70 
      border-warning-200/60 hover:border-warning-300/80
    `,
        error: `
      bg-gradient-to-br from-error-50/90 to-error-100/70 
      border-error-200/60 hover:border-error-300/80
    `
    },
    // 버튼 스타일
    button: {
        primary: `
      bg-primary-500 hover:bg-primary-600 text-white 
      shadow-md hover:shadow-lg active:scale-95
      transition-all duration-200 font-medium
    `,
        secondary: `
      bg-neutral-100 hover:bg-neutral-200 text-neutral-700 
      border border-neutral-300 hover:border-neutral-400
      transition-all duration-200 font-medium
    `,
        ghost: `
      bg-transparent hover:bg-neutral-100 text-neutral-600 hover:text-neutral-800
      transition-all duration-200 font-medium
    `
    },
    // 배지 스타일
    badge: {
        success: `
      bg-success-100 text-success-700 border border-success-200
      font-medium text-xs px-2 py-1 rounded-md
    `,
        warning: `
      bg-warning-100 text-warning-700 border border-warning-200
      font-medium text-xs px-2 py-1 rounded-md
    `,
        info: `
      bg-primary-100 text-primary-700 border border-primary-200
      font-medium text-xs px-2 py-1 rounded-md
    `,
        neutral: `
      bg-neutral-100 text-neutral-700 border border-neutral-200
      font-medium text-xs px-2 py-1 rounded-md
    `
    },
    // 아이콘 컨테이너
    iconContainer: {
        sm: `
      flex h-6 w-6 items-center justify-center rounded-full
      bg-neutral-100 text-neutral-600
    `,
        md: `
      flex h-8 w-8 items-center justify-center rounded-full
      bg-neutral-100 text-neutral-600
    `,
        lg: `
      flex h-10 w-10 items-center justify-center rounded-full
      bg-neutral-100 text-neutral-600
    `
    }
};
const getColorClass = (color, shade = 500)=>{
    return `${color}-${shade}`;
};
const getSpacingClass = (size)=>{
    return designTokens.spacing[size];
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/hooks/use-server-health.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useServerHealth": (()=>useServerHealth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.3_@opentelemetry+_a8516a9e8193861921635ddb2ae04d26/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
"use client";
;
const HEALTH_CHECK_URL = '/api/health';
const CHECK_INTERVAL = 30000; // 30초마다 체크
const TIMEOUT_DURATION = 5000; // 5초 타임아웃
function useServerHealth(enabled = true, modelId) {
    _s();
    const [status, setStatus] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        isHealthy: false,
        isLoading: true,
        error: null,
        lastChecked: null,
        responseTime: null
    });
    const checkHealth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useServerHealth.useCallback[checkHealth]": async ()=>{
            if (!enabled) return;
            setStatus({
                "useServerHealth.useCallback[checkHealth]": (prev)=>({
                        ...prev,
                        isLoading: true,
                        error: null
                    })
            }["useServerHealth.useCallback[checkHealth]"]);
            const startTime = Date.now();
            try {
                const controller = new AbortController();
                const timeoutId = setTimeout({
                    "useServerHealth.useCallback[checkHealth].timeoutId": ()=>controller.abort()
                }["useServerHealth.useCallback[checkHealth].timeoutId"], TIMEOUT_DURATION);
                // 모델 ID가 있으면 쿼리 파라미터로 전달
                const url = modelId ? `${HEALTH_CHECK_URL}?modelId=${encodeURIComponent(modelId)}` : HEALTH_CHECK_URL;
                const response = await fetch(url, {
                    method: 'GET',
                    signal: controller.signal,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                clearTimeout(timeoutId);
                const clientResponseTime = Date.now() - startTime;
                if (response.ok) {
                    const data = await response.json();
                    setStatus({
                        isHealthy: data.status === 'healthy',
                        isLoading: false,
                        error: null,
                        lastChecked: new Date(),
                        responseTime: data.responseTime || clientResponseTime
                    });
                } else {
                    const errorData = await response.json().catch({
                        "useServerHealth.useCallback[checkHealth]": ()=>({})
                    }["useServerHealth.useCallback[checkHealth]"]);
                    throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                const responseTime = Date.now() - startTime;
                let errorMessage = 'Unknown error';
                if (error instanceof Error) {
                    if (error.name === 'AbortError') {
                        errorMessage = 'Request timeout';
                    } else if (error.message.includes('fetch')) {
                        errorMessage = 'Network error';
                    } else {
                        errorMessage = error.message;
                    }
                }
                setStatus({
                    isHealthy: false,
                    isLoading: false,
                    error: errorMessage,
                    lastChecked: new Date(),
                    responseTime
                });
            }
        }
    }["useServerHealth.useCallback[checkHealth]"], [
        enabled,
        modelId
    ]);
    // 초기 체크 및 주기적 체크
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useServerHealth.useEffect": ()=>{
            if (!enabled) return;
            // 즉시 체크
            checkHealth();
            // 주기적 체크
            const interval = setInterval(checkHealth, CHECK_INTERVAL);
            return ({
                "useServerHealth.useEffect": ()=>{
                    clearInterval(interval);
                }
            })["useServerHealth.useEffect"];
        }
    }["useServerHealth.useEffect"], [
        checkHealth,
        enabled
    ]);
    // 수동 새로고침
    const refresh = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useServerHealth.useCallback[refresh]": ()=>{
            checkHealth();
        }
    }["useServerHealth.useCallback[refresh]"], [
        checkHealth
    ]);
    return {
        ...status,
        refresh
    };
}
_s(useServerHealth, "VTMhpLUL+dxvy5ecpZNfbFlX6lw=");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/hooks/use-layer-configs.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useLayerConfigs": (()=>useLayerConfigs),
    "useLayerManager": (()=>useLayerManager)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$providers$2f$tool$2d$invocation$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/providers/tool-invocation-provider.tsx [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
;
const useLayerConfigs = ()=>{
    _s();
    const { layers } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$providers$2f$tool$2d$invocation$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLayerManager"])();
    // ManagedLayerProps를 LayerProps로 안전하게 변환
    return layers.map((layer)=>{
        const { source, toolCallId, ...baseProps } = layer;
        // 레이어 타입에 따른 적절한 변환
        if (layer.type === "geojson") {
            return {
                id: baseProps.id,
                type: baseProps.type,
                data: baseProps.data,
                visible: baseProps.visible ?? true,
                service: baseProps.service,
                opacity: baseProps.opacity,
                zIndex: baseProps.zIndex,
                bbox: baseProps.bbox,
                autoFit: baseProps.autoFit,
                renderOptions: baseProps.style ? {
                    style: baseProps.style
                } : undefined,
                // service: baseProps.service || "geojson",
                dataProjectionCode: baseProps.dataProjectionCode || "EPSG:5186",
                featureProjectionCode: baseProps.featureProjectionCode || "EPSG:5186"
            };
        }
        // geoserver 레이어의 경우
        if (layer.type === "geoserver") {
            return {
                id: baseProps.id,
                type: "geoserver",
                server: baseProps.server || "",
                layer: baseProps.layer || "",
                service: baseProps.service || "wfs",
                info: baseProps.info || {
                    lyrId: baseProps.id,
                    lyrNm: baseProps.name || "Unknown Layer"
                },
                name: baseProps.name,
                visible: baseProps.visible ?? true,
                opacity: baseProps.opacity,
                zIndex: baseProps.zIndex,
                filter: baseProps.filter,
                bbox: baseProps.bbox,
                autoFit: baseProps.autoFit,
                method: baseProps.method,
                crtfckey: baseProps.crtfckey,
                projection: baseProps.projection,
                geometryType: baseProps.geometryType,
                serviceTy: baseProps.serviceTy,
                renderOptions: baseProps.style ? {
                    style: baseProps.style
                } : baseProps.renderOptions
            };
        }
        // 기타 레이어 타입들 (api, kml, csv 등)
        return {
            ...baseProps,
            // 필수 속성들 보장
            type: baseProps.type,
            id: baseProps.id,
            visible: baseProps.visible ?? true,
            autoFit: baseProps.autoFit,
            fitDuration: 100
        };
    });
};
_s(useLayerConfigs, "ferHedVkG7rhc1vucKvabxq2AZ8=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$providers$2f$tool$2d$invocation$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLayerManager"]
    ];
});
const useLayerManager = ()=>{
    _s1();
    const baseManager = (0, __TURBOPACK__imported__module__$5b$project$5d2f$providers$2f$tool$2d$invocation$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLayerManager"])();
    // 스타일 업데이트 시 리마운트 방지를 위한 개선된 updateStyle 함수
    const updateStyleOptimized = (id, style)=>{
        const layer = baseManager.getLayerById(id);
        if (!layer) return;
        // 현재 스타일과 비교하여 실제 변경이 있는 경우에만 업데이트
        const currentStyle = layer.style || layer.renderOptions?.style;
        const styleChanged = JSON.stringify(currentStyle) !== JSON.stringify(style);
        if (styleChanged) {
            // 스타일만 업데이트하고 다른 속성은 유지
            baseManager.updateLayer(id, {
                style,
                // renderOptions도 함께 업데이트하여 일관성 보장
                renderOptions: {
                    style
                }
            });
        }
    };
    // 레이어 상태 정보를 포함한 확장된 관리 기능
    const getLayerWithState = (id)=>{
        const layer = baseManager.getLayerById(id);
        if (!layer) return null;
        return {
            ...layer,
            // 현재 스타일 상태 정보
            currentStyle: layer.style || layer.renderOptions?.style,
            // 스타일 타입 정보
            styleType: layer.service === 'wms' ? 'wms' : layer.service === 'wfs' ? 'wfs' : 'default',
            // 지오메트리 타입 정보
            geometryType: layer.geometryType || 'point',
            // 수정 가능 여부
            isStyleEditable: layer.service === 'wms' || layer.service === 'wfs'
        };
    };
    // 스타일 관련 레이어들만 필터링
    const getStyleableLayers = ()=>{
        return baseManager.layers.filter((layer)=>layer.service === 'wms' || layer.service === 'wfs');
    };
    return {
        ...baseManager,
        // 기존 updateStyle을 최적화된 버전으로 교체
        updateStyle: updateStyleOptimized,
        // 새로운 확장 기능들
        getLayerWithState,
        getStyleableLayers
    };
};
_s1(useLayerManager, "JZoLcEXtwgHCg1pHGNE5B86bhWk=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$providers$2f$tool$2d$invocation$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLayerManager"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/(map)/data:e2e97d [app-client] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"40764eb81a775d0486660fff2140578c0e1f884c4e":"deleteTrailingMessages"},"app/(map)/actions.ts",""] */ __turbopack_context__.s({
    "deleteTrailingMessages": (()=>deleteTrailingMessages)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.3_@opentelemetry+_a8516a9e8193861921635ddb2ae04d26/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-client] (ecmascript)");
"use turbopack no side effects";
;
var deleteTrailingMessages = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("40764eb81a775d0486660fff2140578c0e1f884c4e", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$3_$40$opentelemetry$2b$_a8516a9e8193861921635ddb2ae04d26$2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "deleteTrailingMessages"); //# sourceMappingURL=data:application/json;base64,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
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=_bdbf17f6._.js.map