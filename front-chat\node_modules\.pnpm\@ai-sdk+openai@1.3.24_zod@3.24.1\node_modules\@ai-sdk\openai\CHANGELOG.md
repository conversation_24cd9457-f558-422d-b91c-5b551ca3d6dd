# @ai-sdk/openai

## 1.3.24

### Patch Changes

- 5b20d4b: feat (provider/openai): gpt 5 support

## 1.3.23

### Patch Changes

- c3f5106: fix (provider/openai): handle responses api errors

## 1.3.22

### Patch Changes

- Updated dependencies [d87b9d1]
  - @ai-sdk/provider-utils@2.2.8

## 1.3.21

### Patch Changes

- 5caac29: fix(providers/openai): zod parse error with function

## 1.3.20

### Patch Changes

- dd5450e: feat(provider/openai): add o3 & o4-mini with developer systemMessageMode

## 1.3.19

### Patch Changes

- 3cabda9: feat (providers/openai): add gpt-image-1 model id to image settings

## 1.3.18

### Patch Changes

- 74cd391: feat (providers/openai): support gpt-image-1 image generation

## 1.3.17

### Patch Changes

- ca7bce3: feat (providers/openai): add support for reasoning summaries

## 1.3.16

### Patch Changes

- bd6e457: feat (provider/openai): o4 updates for responses api

## 1.3.15

### Patch Changes

- 98d954e: feat (providers/openai): add o3 and o4-mini models

## 1.3.14

### Patch Changes

- 980141c: fix (openai): structure output for responses model

## 1.3.13

### Patch Changes

- 75b9849: adding support for gpt-4o-search-preview and handling unsupported parameters

## 1.3.12

### Patch Changes

- 575339f: feat (providers/openai): add gpt-4.1 models

## 1.3.11

### Patch Changes

- beef951: feat: add speech with experimental_generateSpeech
- Updated dependencies [beef951]
  - @ai-sdk/provider@1.1.3
  - @ai-sdk/provider-utils@2.2.7

## 1.3.10

### Patch Changes

- dbe53e7: adding support for gpt-4o-search-preview and handling unsupported parameters
- 84ffaba: fix: propagate openai transcription fixes

## 1.3.9

### Patch Changes

- 013faa8: core (ai): change transcription model mimeType to mediaType
- 013faa8: fix (provider/openai): increase transcription model resilience
- Updated dependencies [013faa8]
  - @ai-sdk/provider@1.1.2
  - @ai-sdk/provider-utils@2.2.6

## 1.3.8

### Patch Changes

- c21fa6d: feat: add transcription with experimental_transcribe
- Updated dependencies [c21fa6d]
  - @ai-sdk/provider-utils@2.2.5
  - @ai-sdk/provider@1.1.1

## 1.3.7

### Patch Changes

- Updated dependencies [2c19b9a]
  - @ai-sdk/provider-utils@2.2.4

## 1.3.6

### Patch Changes

- Updated dependencies [28be004]
  - @ai-sdk/provider-utils@2.2.3

## 1.3.5

### Patch Changes

- 52ed95f: fix (provider/openai): force web search tool
- Updated dependencies [b01120e]
  - @ai-sdk/provider-utils@2.2.2

## 1.3.4

### Patch Changes

- b520dba: feat (provider/openai): add chatgpt-4o-latest model

## 1.3.3

### Patch Changes

- 24befd8: feat (provider/openai): add instructions to providerOptions

## 1.3.2

### Patch Changes

- db15028: feat (provider/openai): expose type for validating OpenAI responses provider options

## 1.3.1

### Patch Changes

- Updated dependencies [f10f0fa]
  - @ai-sdk/provider-utils@2.2.1

## 1.3.0

### Minor Changes

- 5bc638d: AI SDK 4.2

### Patch Changes

- Updated dependencies [5bc638d]
  - @ai-sdk/provider@1.1.0
  - @ai-sdk/provider-utils@2.2.0

## 1.2.8

### Patch Changes

- 9f4f1bc: feat (provider/openai): pdf support for chat language models

## 1.2.7

### Patch Changes

- Updated dependencies [d0c4659]
  - @ai-sdk/provider-utils@2.1.15

## 1.2.6

### Patch Changes

- Updated dependencies [0bd5bc6]
  - @ai-sdk/provider@1.0.12
  - @ai-sdk/provider-utils@2.1.14

## 1.2.5

### Patch Changes

- 2e1101a: feat (provider/openai): pdf input support
- Updated dependencies [2e1101a]
  - @ai-sdk/provider@1.0.11
  - @ai-sdk/provider-utils@2.1.13

## 1.2.4

### Patch Changes

- 523f128: feat (provider/openai): add strictSchemas option to responses model

## 1.2.3

### Patch Changes

- Updated dependencies [1531959]
  - @ai-sdk/provider-utils@2.1.12

## 1.2.2

### Patch Changes

- e3a389e: feat (provider/openai): support responses api

## 1.2.1

### Patch Changes

- e1d3d42: feat (ai): expose raw response body in generateText and generateObject
- Updated dependencies [e1d3d42]
  - @ai-sdk/provider@1.0.10
  - @ai-sdk/provider-utils@2.1.11

## 1.2.0

### Minor Changes

- ede6d1b: feat (provider/azure): Add Azure image model support

## 1.1.15

### Patch Changes

- d8216f8: feat (provider/openai): add gpt-4.5-preview to model id set

## 1.1.14

### Patch Changes

- Updated dependencies [ddf9740]
  - @ai-sdk/provider@1.0.9
  - @ai-sdk/provider-utils@2.1.10

## 1.1.13

### Patch Changes

- Updated dependencies [2761f06]
  - @ai-sdk/provider@1.0.8
  - @ai-sdk/provider-utils@2.1.9

## 1.1.12

### Patch Changes

- ea159cb: chore (provider/openai): remove default streaming simulation for o1

## 1.1.11

### Patch Changes

- Updated dependencies [2e898b4]
  - @ai-sdk/provider-utils@2.1.8

## 1.1.10

### Patch Changes

- Updated dependencies [3ff4ef8]
  - @ai-sdk/provider-utils@2.1.7

## 1.1.9

### Patch Changes

- c55b81a: fix (provider/openai): fix o3-mini streaming

## 1.1.8

### Patch Changes

- 161be90: fix (provider/openai): fix model id typo

## 1.1.7

### Patch Changes

- 0a2f026: feat (provider/openai): add o3-mini

## 1.1.6

### Patch Changes

- d89c3b9: feat (provider): add image model support to provider specification
- Updated dependencies [d89c3b9]
  - @ai-sdk/provider@1.0.7
  - @ai-sdk/provider-utils@2.1.6

## 1.1.5

### Patch Changes

- Updated dependencies [3a602ca]
  - @ai-sdk/provider-utils@2.1.5

## 1.1.4

### Patch Changes

- Updated dependencies [066206e]
  - @ai-sdk/provider-utils@2.1.4

## 1.1.3

### Patch Changes

- Updated dependencies [39e5c1f]
  - @ai-sdk/provider-utils@2.1.3

## 1.1.2

### Patch Changes

- 3a58a2e: feat (ai/core): throw NoImageGeneratedError from generateImage when no predictions are returned.
- Updated dependencies [ed012d2]
- Updated dependencies [3a58a2e]
  - @ai-sdk/provider-utils@2.1.2
  - @ai-sdk/provider@1.0.6

## 1.1.1

### Patch Changes

- e7a9ec9: feat (provider-utils): include raw value in json parse results
- Updated dependencies [e7a9ec9]
- Updated dependencies [0a699f1]
  - @ai-sdk/provider-utils@2.1.1
  - @ai-sdk/provider@1.0.5

## 1.1.0

### Minor Changes

- 62ba5ad: release: AI SDK 4.1

### Patch Changes

- Updated dependencies [62ba5ad]
  - @ai-sdk/provider-utils@2.1.0

## 1.0.20

### Patch Changes

- Updated dependencies [00114c5]
  - @ai-sdk/provider-utils@2.0.8

## 1.0.19

### Patch Changes

- 218d001: feat (provider): Add maxImagesPerCall setting to all image providers.

## 1.0.18

### Patch Changes

- fe816e4: fix (provider/openai): streamObject with o1

## 1.0.17

### Patch Changes

- ba62cf2: feat (provider/openai): automatically map maxTokens to max_completion_tokens for reasoning models
- 3c3fae8: fix (provider/openai): add o1-mini-2024-09-12 and o1-preview-2024-09-12 configurations

## 1.0.16

### Patch Changes

- Updated dependencies [90fb95a]
- Updated dependencies [e6dfef4]
- Updated dependencies [6636db6]
  - @ai-sdk/provider-utils@2.0.7

## 1.0.15

### Patch Changes

- f8c6acb: feat (provider/openai): automatically simulate streaming for reasoning models
- d0041f7: feat (provider/openai): improved system message support for reasoning models
- 4d2f97b: feat (provider/openai): improve automatic setting removal for reasoning models

## 1.0.14

### Patch Changes

- 19a2ce7: feat (ai/core): add aspectRatio and seed options to generateImage
- 6337688: feat: change image generation errors to warnings
- Updated dependencies [19a2ce7]
- Updated dependencies [19a2ce7]
- Updated dependencies [6337688]
  - @ai-sdk/provider@1.0.4
  - @ai-sdk/provider-utils@2.0.6

## 1.0.13

### Patch Changes

- b19aa82: feat (provider/openai): add predicted outputs token usage

## 1.0.12

### Patch Changes

- a4241ff: feat (provider/openai): add o3 reasoning model support

## 1.0.11

### Patch Changes

- 5ed5e45: chore (config): Use ts-library.json tsconfig for no-UI libs.
- Updated dependencies [5ed5e45]
  - @ai-sdk/provider-utils@2.0.5
  - @ai-sdk/provider@1.0.3

## 1.0.10

### Patch Changes

- d4fad4e: fix (provider/openai): fix reasoning model detection

## 1.0.9

### Patch Changes

- 3fab0fb: feat (provider/openai): support reasoning_effort setting
- e956eed: feat (provider/openai): update model list and add o1
- 6faab13: feat (provider/openai): simulated streaming setting

## 1.0.8

### Patch Changes

- 09a9cab: feat (ai/core): add experimental generateImage function
- Updated dependencies [09a9cab]
  - @ai-sdk/provider@1.0.2
  - @ai-sdk/provider-utils@2.0.4

## 1.0.7

### Patch Changes

- Updated dependencies [0984f0b]
  - @ai-sdk/provider-utils@2.0.3

## 1.0.6

### Patch Changes

- a9a19cb: fix (provider/openai,groq): prevent sending duplicate tool calls

## 1.0.5

### Patch Changes

- fc18132: feat (ai/core): experimental output for generateText

## 1.0.4

### Patch Changes

- Updated dependencies [b446ae5]
  - @ai-sdk/provider@1.0.1
  - @ai-sdk/provider-utils@2.0.2

## 1.0.3

### Patch Changes

- b748dfb: feat (providers): update model lists

## 1.0.2

### Patch Changes

- Updated dependencies [c3ab5de]
  - @ai-sdk/provider-utils@2.0.1

## 1.0.1

### Patch Changes

- 5e6419a: feat (provider/openai): support streaming for reasoning models

## 1.0.0

### Major Changes

- 66060f7: chore (release): bump major version to 4.0
- 79644e9: chore (provider/openai): remove OpenAI facade
- 0d3d3f5: chore (providers): remove baseUrl option

### Patch Changes

- Updated dependencies [b469a7e]
- Updated dependencies [dce4158]
- Updated dependencies [c0ddc24]
- Updated dependencies [b1da952]
- Updated dependencies [dce4158]
- Updated dependencies [8426f55]
- Updated dependencies [db46ce5]
  - @ai-sdk/provider-utils@2.0.0
  - @ai-sdk/provider@1.0.0

## 1.0.0-canary.3

### Patch Changes

- Updated dependencies [8426f55]
  - @ai-sdk/provider-utils@2.0.0-canary.3

## 1.0.0-canary.2

### Patch Changes

- Updated dependencies [dce4158]
- Updated dependencies [dce4158]
  - @ai-sdk/provider-utils@2.0.0-canary.2

## 1.0.0-canary.1

### Major Changes

- 79644e9: chore (provider/openai): remove OpenAI facade
- 0d3d3f5: chore (providers): remove baseUrl option

### Patch Changes

- Updated dependencies [b1da952]
  - @ai-sdk/provider-utils@2.0.0-canary.1

## 1.0.0-canary.0

### Major Changes

- 66060f7: chore (release): bump major version to 4.0

### Patch Changes

- Updated dependencies [b469a7e]
- Updated dependencies [c0ddc24]
- Updated dependencies [db46ce5]
  - @ai-sdk/provider-utils@2.0.0-canary.0
  - @ai-sdk/provider@1.0.0-canary.0

## 0.0.72

### Patch Changes

- 0bc4115: feat (provider/openai): support predicted outputs

## 0.0.71

### Patch Changes

- 54a3a59: fix (provider/openai): support object-json mode without schema

## 0.0.70

### Patch Changes

- 3b1b69a: feat: provider-defined tools
- Updated dependencies [aa98cdb]
- Updated dependencies [1486128]
- Updated dependencies [7b937c5]
- Updated dependencies [3b1b69a]
- Updated dependencies [811a317]
  - @ai-sdk/provider-utils@1.0.22
  - @ai-sdk/provider@0.0.26

## 0.0.69

### Patch Changes

- b9b0d7b: feat (ai): access raw request body
- Updated dependencies [b9b0d7b]
  - @ai-sdk/provider@0.0.25
  - @ai-sdk/provider-utils@1.0.21

## 0.0.68

### Patch Changes

- 741ca51: feat (provider/openai): support mp3 and wav audio inputs

## 0.0.67

### Patch Changes

- 39fccee: feat (provider/openai): provider name can be changed for 3rd party openai compatible providers

## 0.0.66

### Patch Changes

- 3f29c10: feat (provider/openai): support metadata field for distillation

## 0.0.65

### Patch Changes

- e8aed44: Add OpenAI cached prompt tokens to experimental_providerMetadata for generateText and streamText

## 0.0.64

### Patch Changes

- 5aa576d: feat (provider/openai): support store parameter for distillation

## 0.0.63

### Patch Changes

- Updated dependencies [d595d0d]
  - @ai-sdk/provider@0.0.24
  - @ai-sdk/provider-utils@1.0.20

## 0.0.62

### Patch Changes

- 7efa867: feat (provider/openai): simulated streaming for reasoning models

## 0.0.61

### Patch Changes

- 8132a60: feat (provider/openai): support reasoning token usage and max_completion_tokens

## 0.0.60

### Patch Changes

- Updated dependencies [273f696]
  - @ai-sdk/provider-utils@1.0.19

## 0.0.59

### Patch Changes

- a0991ec: feat (provider/openai): add o1-preview and o1-mini models

## 0.0.58

### Patch Changes

- e0c36bd: feat (provider/openai): support image detail

## 0.0.57

### Patch Changes

- d1aaeae: feat (provider/openai): support ai sdk image download

## 0.0.56

### Patch Changes

- 03313cd: feat (ai): expose response id, response model, response timestamp in telemetry and api
- Updated dependencies [03313cd]
- Updated dependencies [3be7c1c]
  - @ai-sdk/provider-utils@1.0.18
  - @ai-sdk/provider@0.0.23

## 0.0.55

### Patch Changes

- 28cbf2e: fix (provider/openai): support tool call deltas when arguments are sent in the first chunk

## 0.0.54

### Patch Changes

- 26515cb: feat (ai/provider): introduce ProviderV1 specification
- Updated dependencies [26515cb]
  - @ai-sdk/provider@0.0.22
  - @ai-sdk/provider-utils@1.0.17

## 0.0.53

### Patch Changes

- Updated dependencies [09f895f]
  - @ai-sdk/provider-utils@1.0.16

## 0.0.52

### Patch Changes

- d5b6a15: feat (provider/openai): support partial usage information

## 0.0.51

### Patch Changes

- Updated dependencies [d67fa9c]
  - @ai-sdk/provider-utils@1.0.15

## 0.0.50

### Patch Changes

- Updated dependencies [f2c025e]
  - @ai-sdk/provider@0.0.21
  - @ai-sdk/provider-utils@1.0.14

## 0.0.49

### Patch Changes

- f42d9bd: fix (provider/openai): support OpenRouter streaming errors

## 0.0.48

### Patch Changes

- Updated dependencies [6ac355e]
  - @ai-sdk/provider@0.0.20
  - @ai-sdk/provider-utils@1.0.13

## 0.0.47

### Patch Changes

- 4ffbaee: fix (provider/openai): fix strict flag for structured outputs with tools
- dd712ac: fix: use FetchFunction type to prevent self-reference
- Updated dependencies [dd712ac]
  - @ai-sdk/provider-utils@1.0.12

## 0.0.46

### Patch Changes

- 89b18ca: fix (ai/provider): send finish reason 'unknown' by default
- Updated dependencies [dd4a0f5]
  - @ai-sdk/provider@0.0.19
  - @ai-sdk/provider-utils@1.0.11

## 0.0.45

### Patch Changes

- Updated dependencies [4bd27a9]
- Updated dependencies [845754b]
  - @ai-sdk/provider-utils@1.0.10
  - @ai-sdk/provider@0.0.18

## 0.0.44

### Patch Changes

- 029af4c: feat (ai/core): support schema name & description in generateObject & streamObject
- Updated dependencies [029af4c]
  - @ai-sdk/provider@0.0.17
  - @ai-sdk/provider-utils@1.0.9

## 0.0.43

### Patch Changes

- d58517b: feat (ai/openai): structured outputs
- c0a73ee: feat (provider/openai): add gpt-4o-2024-08-06 to list of supported models
- Updated dependencies [d58517b]
  - @ai-sdk/provider@0.0.16
  - @ai-sdk/provider-utils@1.0.8

## 0.0.42

### Patch Changes

- Updated dependencies [96aed25]
  - @ai-sdk/provider@0.0.15
  - @ai-sdk/provider-utils@1.0.7

## 0.0.41

### Patch Changes

- 7a2eb27: feat (provider/openai): make role nullish to enhance provider support
- Updated dependencies [9614584]
- Updated dependencies [0762a22]
  - @ai-sdk/provider-utils@1.0.6

## 0.0.40

### Patch Changes

- Updated dependencies [a8d1c9e9]
  - @ai-sdk/provider-utils@1.0.5
  - @ai-sdk/provider@0.0.14

## 0.0.39

### Patch Changes

- Updated dependencies [4f88248f]
  - @ai-sdk/provider-utils@1.0.4

## 0.0.38

### Patch Changes

- 2b9da0f0: feat (core): support stopSequences setting.
- 909b9d27: feat (ai/openai): Support legacy function calls
- a5b58845: feat (core): support topK setting
- 4aa8deb3: feat (provider): support responseFormat setting in provider api
- 13b27ec6: chore (ai/core): remove grammar mode
- Updated dependencies [2b9da0f0]
- Updated dependencies [a5b58845]
- Updated dependencies [4aa8deb3]
- Updated dependencies [13b27ec6]
  - @ai-sdk/provider@0.0.13
  - @ai-sdk/provider-utils@1.0.3

## 0.0.37

### Patch Changes

- 89947fc5: chore (provider/openai): update model list for type-ahead support

## 0.0.36

### Patch Changes

- b7290943: feat (ai/core): add token usage to embed and embedMany
- Updated dependencies [b7290943]
  - @ai-sdk/provider@0.0.12
  - @ai-sdk/provider-utils@1.0.2

## 0.0.35

### Patch Changes

- Updated dependencies [d481729f]
  - @ai-sdk/provider-utils@1.0.1

## 0.0.34

### Patch Changes

- 5edc6110: feat (ai/core): add custom request header support
- Updated dependencies [5edc6110]
- Updated dependencies [5edc6110]
- Updated dependencies [5edc6110]
  - @ai-sdk/provider@0.0.11
  - @ai-sdk/provider-utils@1.0.0

## 0.0.33

### Patch Changes

- Updated dependencies [02f6a088]
  - @ai-sdk/provider-utils@0.0.16

## 0.0.32

### Patch Changes

- 1b37b8b9: fix (@ai-sdk/openai): only send logprobs settings when logprobs are requested

## 0.0.31

### Patch Changes

- eba071dd: feat (@ai-sdk/azure): add azure openai completion support
- 1ea890fe: feat (@ai-sdk/azure): add azure openai completion support

## 0.0.30

### Patch Changes

- Updated dependencies [85712895]
- Updated dependencies [85712895]
  - @ai-sdk/provider-utils@0.0.15

## 0.0.29

### Patch Changes

- 4728c37f: feat (core): add text embedding model support to provider registry
- 7910ae84: feat (providers): support custom fetch implementations
- Updated dependencies [7910ae84]
  - @ai-sdk/provider-utils@0.0.14

## 0.0.28

### Patch Changes

- f9db8fd6: feat (@ai-sdk/openai): add parallelToolCalls setting

## 0.0.27

### Patch Changes

- fc9552ec: fix (@ai-sdk/azure): allow for nullish delta

## 0.0.26

### Patch Changes

- 7530f861: fix (@ai-sdk/openai): add internal dist to bundle

## 0.0.25

### Patch Changes

- 8b1362a7: chore (@ai-sdk/openai): expose models under /internal for reuse in other providers

## 0.0.24

### Patch Changes

- 0e78960c: fix (@ai-sdk/openai): make function name and arguments nullish

## 0.0.23

### Patch Changes

- a68fe74a: fix (@ai-sdk/openai): allow null tool_calls value.

## 0.0.22

### Patch Changes

- Updated dependencies [102ca22f]
  - @ai-sdk/provider@0.0.10
  - @ai-sdk/provider-utils@0.0.13

## 0.0.21

### Patch Changes

- fca7d026: feat (provider/openai): support streaming tool calls that are sent in one chunk
- Updated dependencies [09295e2e]
- Updated dependencies [09295e2e]
- Updated dependencies [043a5de2]
  - @ai-sdk/provider@0.0.9
  - @ai-sdk/provider-utils@0.0.12

## 0.0.20

### Patch Changes

- a1d08f3e: fix (provider/openai): handle error chunks when streaming

## 0.0.19

### Patch Changes

- beb8b739: fix (provider/openai): return unknown finish reasons as unknown

## 0.0.18

### Patch Changes

- fb42e760: feat (provider/openai): send user message content as text when possible

## 0.0.17

### Patch Changes

- f39c0dd2: feat (provider): implement toolChoice support
- Updated dependencies [f39c0dd2]
  - @ai-sdk/provider@0.0.8
  - @ai-sdk/provider-utils@0.0.11

## 0.0.16

### Patch Changes

- 2b18fa11: fix (provider/openai): remove object type validation

## 0.0.15

### Patch Changes

- 24683b72: fix (providers): Zod is required dependency
- Updated dependencies [8e780288]
  - @ai-sdk/provider@0.0.7
  - @ai-sdk/provider-utils@0.0.10

## 0.0.14

### Patch Changes

- Updated dependencies [6a50ac4]
- Updated dependencies [6a50ac4]
  - @ai-sdk/provider@0.0.6
  - @ai-sdk/provider-utils@0.0.9

## 0.0.13

### Patch Changes

- 4e3c922: fix (provider/openai): introduce compatibility mode in which "stream_options" are not sent

## 0.0.12

### Patch Changes

- 6f48839: feat (provider/openai): add gpt-4o to the list of supported models
- 1009594: feat (provider/openai): set stream_options/include_usage to true when streaming
- 0f6bc4e: feat (ai/core): add embed function
- Updated dependencies [0f6bc4e]
  - @ai-sdk/provider@0.0.5
  - @ai-sdk/provider-utils@0.0.8

## 0.0.11

### Patch Changes

- Updated dependencies [325ca55]
  - @ai-sdk/provider@0.0.4
  - @ai-sdk/provider-utils@0.0.7

## 0.0.10

### Patch Changes

- Updated dependencies [276f22b]
  - @ai-sdk/provider-utils@0.0.6

## 0.0.9

### Patch Changes

- Updated dependencies [41d5736]
  - @ai-sdk/provider@0.0.3
  - @ai-sdk/provider-utils@0.0.5

## 0.0.8

### Patch Changes

- Updated dependencies [56ef84a]
  - @ai-sdk/provider-utils@0.0.4

## 0.0.7

### Patch Changes

- 0833e19: Allow optional content to support Fireworks function calling.

## 0.0.6

### Patch Changes

- d6431ae: ai/core: add logprobs support (thanks @SamStenner for the contribution)
- 25f3350: ai/core: add support for getting raw response headers.
- Updated dependencies [d6431ae]
- Updated dependencies [25f3350]
  - @ai-sdk/provider@0.0.2
  - @ai-sdk/provider-utils@0.0.3

## 0.0.5

### Patch Changes

- eb150a6: ai/core: remove scaling of setting values (breaking change). If you were using the temperature, frequency penalty, or presence penalty settings, you need to update the providers and adjust the setting values.
- Updated dependencies [eb150a6]
  - @ai-sdk/provider-utils@0.0.2
  - @ai-sdk/provider@0.0.1

## 0.0.4

### Patch Changes

- c6fc35b: Add custom header and OpenAI project support.

## 0.0.3

### Patch Changes

- ab60b18: Simplified model construction by directly calling provider functions. Add create... functions to create provider instances.

## 0.0.2

### Patch Changes

- 2bff460: Fix build for release.

## 0.0.1

### Patch Changes

- 7b8791d: Support streams with 'chat.completion' objects.
- 7b8791d: Rename baseUrl to baseURL. Automatically remove trailing slashes.
- Updated dependencies [7b8791d]
  - @ai-sdk/provider-utils@0.0.1
