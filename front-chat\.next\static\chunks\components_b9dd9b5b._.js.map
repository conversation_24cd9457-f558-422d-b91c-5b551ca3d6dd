{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/preview-attachment.tsx"], "sourcesContent": ["import { Attachment } from \"ai\";\r\n\r\nimport { Loader2 } from \"lucide-react\";\r\nimport React from \"react\";\r\n\r\nexport const PreviewAttachment = ({\r\n\tattachment,\r\n\tisUploading = false,\r\n\tremoveFile\r\n}: {\r\n\tattachment: Attachment,\r\n\tisUploading?: boolean,\r\n\tremoveFile?: (url: string) => void\r\n}) => {\r\n\tconst { name, url, contentType } = attachment;\r\n\r\n\treturn (\r\n\t\t(<div className=\"flex flex-col gap-2 max-w-16\">\r\n\t\t\t<div className=\"h-16 w-16 bg-muted rounded-md relative flex flex-col items-center justify-center\">\r\n\t\t\t\t{contentType ? (\r\n\t\t\t\t\tcontentType.startsWith(\"image\") ? (\r\n\t\t\t\t\t\t// NOTE: it is recommended to use next/image for images\r\n\t\t\t\t\t\t// eslint-disable-next-line @next/next/no-img-element\r\n\t\t\t\t\t\t(<img\r\n\t\t\t\t\t\t\tkey={url}\r\n\t\t\t\t\t\t\tsrc={url}\r\n\t\t\t\t\t\t\talt={name ?? \"An image attachment\"}\r\n\t\t\t\t\t\t\tclassName=\"rounded-md size-full object-cover\"\r\n\t\t\t\t\t\t/>)\r\n\t\t\t\t\t) : (\r\n\t\t\t\t\t\t<div className=\"\"></div>\r\n\t\t\t\t\t)\r\n\t\t\t\t) : (\r\n\t\t\t\t\t<div className=\"\"></div>\r\n\t\t\t\t)}\r\n\r\n\t\t\t\t{isUploading && (\r\n\t\t\t\t\t<div className=\"animate-spin absolute text-zinc-500\">\r\n\t\t\t\t\t\t<Loader2 className=\"h-4 w-4 animate-spin text-gray-500\" />\r\n\t\t\t\t\t</div>\r\n\t\t\t\t)}\r\n\t\t\t\t{removeFile &&\r\n\t\t\t\t\t<button\r\n\t\t\t\t\t\ttype=\"button\"\r\n\t\t\t\t\t\tonClick={() => removeFile(url)}\r\n\t\t\t\t\t\tclassName=\"absolute top-1 right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t×\r\n\t\t\t\t\t</button>\r\n\t\t\t\t}\r\n\t\t\t</div>\r\n\t\t\t<div className=\"text-xs  max-w-16 truncate\">{name}</div>\r\n\t\t</div>)\r\n\t);\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAGO,MAAM,oBAAoB,CAAC,EACjC,UAAU,EACV,cAAc,KAAK,EACnB,UAAU,EAKV;IACA,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,WAAW,EAAE,GAAG;IAEnC,qBACE,sSAAC;QAAI,WAAU;;0BACf,sSAAC;gBAAI,WAAU;;oBACb,cACA,YAAY,UAAU,CAAC,yBAGrB,sSAAC;wBAED,KAAK;wBACL,KAAK,QAAQ;wBACb,WAAU;uBAHL;;;;6CAMN,sSAAC;wBAAI,WAAU;;;;;6CAGhB,sSAAC;wBAAI,WAAU;;;;;;oBAGf,6BACA,sSAAC;wBAAI,WAAU;kCACd,cAAA,sSAAC,wSAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;;oBAGpB,4BACA,sSAAC;wBACA,MAAK;wBACL,SAAS,IAAM,WAAW;wBAC1B,WAAU;kCACV;;;;;;;;;;;;0BAKH,sSAAC;gBAAI,WAAU;0BAA8B;;;;;;;;;;;;AAGhD;KAjDa", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/magicui/magic-card.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion, useMotionTemplate, useMotionValue } from \"motion/react\";\r\nimport React, { useCallback, useEffect, useRef } from \"react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface MagicCardProps {\r\n  children?: React.ReactNode;\r\n  className?: string;\r\n  gradientSize?: number;\r\n  gradientColor?: string;\r\n  gradientOpacity?: number;\r\n  gradientFrom?: string;\r\n  gradientTo?: string;\r\n}\r\n\r\nexport function MagicCard({\r\n  children,\r\n  className,\r\n  gradientSize = 200,\r\n  gradientColor = \"#262626\",\r\n  gradientOpacity = 0.8,\r\n  gradientFrom = \"#9E7AFF\",\r\n  gradientTo = \"#FE8BBB\",\r\n}: MagicCardProps) {\r\n  const cardRef = useRef<HTMLDivElement>(null);\r\n  const mouseX = useMotionValue(-gradientSize);\r\n  const mouseY = useMotionValue(-gradientSize);\r\n\r\n  const handleMouseMove = useCallback(\r\n    (e: MouseEvent) => {\r\n      if (cardRef.current) {\r\n        const { left, top } = cardRef.current.getBoundingClientRect();\r\n        const clientX = e.clientX;\r\n        const clientY = e.clientY;\r\n        mouseX.set(clientX - left);\r\n        mouseY.set(clientY - top);\r\n      }\r\n    },\r\n    [mouseX, mouseY],\r\n  );\r\n\r\n  const handleMouseOut = useCallback(\r\n    (e: MouseEvent) => {\r\n      if (!e.relatedTarget) {\r\n        document.removeEventListener(\"mousemove\", handleMouseMove);\r\n        mouseX.set(-gradientSize);\r\n        mouseY.set(-gradientSize);\r\n      }\r\n    },\r\n    [handleMouseMove, mouseX, gradientSize, mouseY],\r\n  );\r\n\r\n  const handleMouseEnter = useCallback(() => {\r\n    document.addEventListener(\"mousemove\", handleMouseMove);\r\n    mouseX.set(-gradientSize);\r\n    mouseY.set(-gradientSize);\r\n  }, [handleMouseMove, mouseX, gradientSize, mouseY]);\r\n\r\n  useEffect(() => {\r\n    document.addEventListener(\"mousemove\", handleMouseMove);\r\n    document.addEventListener(\"mouseout\", handleMouseOut);\r\n    document.addEventListener(\"mouseenter\", handleMouseEnter);\r\n\r\n    return () => {\r\n      document.removeEventListener(\"mousemove\", handleMouseMove);\r\n      document.removeEventListener(\"mouseout\", handleMouseOut);\r\n      document.removeEventListener(\"mouseenter\", handleMouseEnter);\r\n    };\r\n  }, [handleMouseEnter, handleMouseMove, handleMouseOut]);\r\n\r\n  useEffect(() => {\r\n    mouseX.set(-gradientSize);\r\n    mouseY.set(-gradientSize);\r\n  }, [gradientSize, mouseX, mouseY]);\r\n\r\n  return (\r\n    <div\r\n      ref={cardRef}\r\n      className={cn(\"group relative rounded-[inherit]\", className)}\r\n    >\r\n      <motion.div\r\n        className=\"pointer-events-none absolute inset-0 rounded-[inherit] bg-border duration-300 group-hover:opacity-100\"\r\n        style={{\r\n          background: useMotionTemplate`\r\n          radial-gradient(${gradientSize}px circle at ${mouseX}px ${mouseY}px,\r\n          ${gradientFrom}, \r\n          ${gradientTo}, \r\n          hsl(var(--border)) 100%\r\n          )\r\n          `,\r\n        }}\r\n      />\r\n      <div className=\"absolute inset-px rounded-[inherit] bg-background\" />\r\n      <motion.div\r\n        className=\"pointer-events-none absolute inset-px rounded-[inherit] opacity-0 transition-opacity duration-300 group-hover:opacity-100\"\r\n        style={{\r\n          background: useMotionTemplate`\r\n            radial-gradient(${gradientSize}px circle at ${mouseX}px ${mouseY}px, ${gradientColor}, transparent 100%)\r\n          `,\r\n          opacity: gradientOpacity,\r\n        }}\r\n      />\r\n      <div className=\"relative\">{children}</div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AAEA;;;AALA;;;;AAiBO,SAAS,UAAU,EACxB,QAAQ,EACR,SAAS,EACT,eAAe,GAAG,EAClB,gBAAgB,SAAS,EACzB,kBAAkB,GAAG,EACrB,eAAe,SAAS,EACxB,aAAa,SAAS,EACP;;IACf,MAAM,UAAU,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAAkB;IACvC,MAAM,SAAS,CAAA,GAAA,+UAAA,CAAA,iBAAc,AAAD,EAAE,CAAC;IAC/B,MAAM,SAAS,CAAA,GAAA,+UAAA,CAAA,iBAAc,AAAD,EAAE,CAAC;IAE/B,MAAM,kBAAkB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;kDAChC,CAAC;YACC,IAAI,QAAQ,OAAO,EAAE;gBACnB,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,QAAQ,OAAO,CAAC,qBAAqB;gBAC3D,MAAM,UAAU,EAAE,OAAO;gBACzB,MAAM,UAAU,EAAE,OAAO;gBACzB,OAAO,GAAG,CAAC,UAAU;gBACrB,OAAO,GAAG,CAAC,UAAU;YACvB;QACF;iDACA;QAAC;QAAQ;KAAO;IAGlB,MAAM,iBAAiB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;iDAC/B,CAAC;YACC,IAAI,CAAC,EAAE,aAAa,EAAE;gBACpB,SAAS,mBAAmB,CAAC,aAAa;gBAC1C,OAAO,GAAG,CAAC,CAAC;gBACZ,OAAO,GAAG,CAAC,CAAC;YACd;QACF;gDACA;QAAC;QAAiB;QAAQ;QAAc;KAAO;IAGjD,MAAM,mBAAmB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;mDAAE;YACnC,SAAS,gBAAgB,CAAC,aAAa;YACvC,OAAO,GAAG,CAAC,CAAC;YACZ,OAAO,GAAG,CAAC,CAAC;QACd;kDAAG;QAAC;QAAiB;QAAQ;QAAc;KAAO;IAElD,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;+BAAE;YACR,SAAS,gBAAgB,CAAC,aAAa;YACvC,SAAS,gBAAgB,CAAC,YAAY;YACtC,SAAS,gBAAgB,CAAC,cAAc;YAExC;uCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;oBAC1C,SAAS,mBAAmB,CAAC,YAAY;oBACzC,SAAS,mBAAmB,CAAC,cAAc;gBAC7C;;QACF;8BAAG;QAAC;QAAkB;QAAiB;KAAe;IAEtD,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;+BAAE;YACR,OAAO,GAAG,CAAC,CAAC;YACZ,OAAO,GAAG,CAAC,CAAC;QACd;8BAAG;QAAC;QAAc;QAAQ;KAAO;IAEjC,qBACE,sSAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;;0BAElD,sSAAC,uVAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBACL,YAAY,kVAAA,CAAA,oBAAiB,CAAC;0BACd,EAAE,aAAa,aAAa,EAAE,OAAO,GAAG,EAAE,OAAO;UACjE,EAAE,aAAa;UACf,EAAE,WAAW;;;UAGb,CAAC;gBACH;;;;;;0BAEF,sSAAC;gBAAI,WAAU;;;;;;0BACf,sSAAC,uVAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBACL,YAAY,kVAAA,CAAA,oBAAiB,CAAC;4BACZ,EAAE,aAAa,aAAa,EAAE,OAAO,GAAG,EAAE,OAAO,IAAI,EAAE,cAAc;UACvF,CAAC;oBACD,SAAS;gBACX;;;;;;0BAEF,sSAAC;gBAAI,WAAU;0BAAY;;;;;;;;;;;;AAGjC;GA1FgB;;QAUC,+UAAA,CAAA,iBAAc;QACd,+UAAA,CAAA,iBAAc;;;KAXf", "debugId": null}}, {"offset": {"line": 261, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/magicui/animated-shiny-text.tsx"], "sourcesContent": ["import { ComponentPropsWithoutRef, CSSProperties, FC } from \"react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nexport interface AnimatedShinyTextProps\r\n  extends ComponentPropsWithoutRef<\"span\"> {\r\n  shimmerWidth?: number;\r\n}\r\n\r\nexport const AnimatedShinyText: FC<AnimatedShinyTextProps> = ({\r\n  children,\r\n  className,\r\n  shimmerWidth = 100,\r\n  ...props\r\n}) => {\r\n  return (\r\n    <span\r\n      style={\r\n        {\r\n          \"--shiny-width\": `${shimmerWidth}px`,\r\n        } as CSSProperties\r\n      }\r\n      className={cn(\r\n        \"max-w-md text-neutral-600/70 dark:text-neutral-400/70\",\r\n\r\n        // Shine effect\r\n        \"animate-shiny-text bg-clip-text bg-no-repeat [background-position:0_0] [background-size:var(--shiny-width)_100%] [transition:background-position_1s_cubic-bezier(.6,.6,0,1)_infinite]\",\r\n\r\n        // Shine gradient\r\n        \"bg-gradient-to-r from-transparent via-black/80 via-50% to-transparent  dark:via-white/80\",\r\n\r\n        className,\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n    </span>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAOO,MAAM,oBAAgD,CAAC,EAC5D,QAAQ,EACR,SAAS,EACT,eAAe,GAAG,EAClB,GAAG,OACJ;IACC,qBACE,sSAAC;QACC,OACE;YACE,iBAAiB,GAAG,aAAa,EAAE,CAAC;QACtC;QAEF,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,yDAEA,eAAe;QACf,yLAEA,iBAAiB;QACjB,4FAEA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;KA7Ba", "debugId": null}}, {"offset": {"line": 296, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/code-editor.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { EditorView, lineNumbers as createLineNumbers } from '@codemirror/view';\r\nimport { EditorState, Transaction } from '@codemirror/state';\r\nimport { javascript } from '@codemirror/lang-javascript';\r\nimport { python } from '@codemirror/lang-python';\r\nimport { html } from '@codemirror/lang-html';\r\nimport { oneDark } from '@codemirror/theme-one-dark';\r\nimport { minimalSetup } from 'codemirror';\r\nimport React, { memo, useEffect, useRef } from 'react';\r\nimport { useTheme } from 'next-themes';\r\n\r\n// 에디터 테마 스타일 확장\r\nconst modernThemeLight = EditorView.theme({\r\n  '&': {\r\n    backgroundColor: '#fafafa',\r\n    height: '100%',\r\n    fontSize: '14px',\r\n    borderRadius: '6px',\r\n  },\r\n  '.cm-content': {\r\n    fontFamily: 'ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace',\r\n    padding: '1rem',\r\n  },\r\n  '.cm-line': {\r\n    padding: '0 4px',\r\n    lineHeight: '1.6',\r\n  },\r\n  '.cm-matchingBracket': {\r\n    backgroundColor: '#e2e8f0',\r\n    color: '#1e293b',\r\n  },\r\n  '.cm-activeLine': {\r\n    backgroundColor: '#f8fafc',\r\n  },\r\n});\r\n\r\nconst modernThemeDark = EditorView.theme({\r\n  '&': {\r\n    backgroundColor: '#18181b',\r\n    height: '100%',\r\n    fontSize: '14px',\r\n    borderRadius: '6px',\r\n  },\r\n  '.cm-content': {\r\n    fontFamily: 'ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace',\r\n    padding: '1rem',\r\n  },\r\n  '.cm-line': {\r\n    padding: '0 4px',\r\n    lineHeight: '1.6',\r\n  },\r\n  '.cm-matchingBracket': {\r\n    backgroundColor: '#27272a',\r\n    color: '#e4e4e7',\r\n  },\r\n  '.cm-activeLine': {\r\n    backgroundColor: '#1f1f23',\r\n  },\r\n});\r\n\r\nconst customSetup = [\r\n  minimalSetup,\r\n  EditorView.lineWrapping,\r\n  EditorState.allowMultipleSelections.of(true),\r\n  EditorView.contentAttributes.of({ autocomplete: 'off' }),\r\n];\r\n\r\ntype EditorProps = {\r\n  content: string;\r\n  status: 'streaming' | 'idle';\r\n  language?: string;\r\n  mode?: 'view' | 'edit';\r\n  lineNumbers?: boolean;\r\n  onChange?: (value: string) => void;\r\n};\r\n\r\nfunction PureCodeEditor({ \r\n  content, \r\n  status, \r\n  language = 'javascript',\r\n  mode = 'view',\r\n  lineNumbers = false,\r\n  onChange,\r\n}: EditorProps) {\r\n  const containerRef = useRef<HTMLDivElement>(null);\r\n  const editorRef = useRef<EditorView | null>(null);\r\n  const { resolvedTheme } = useTheme();\r\n\r\n  const getLanguageExtension = (lang: EditorProps['language']) => {\r\n    switch (lang) {\r\n      case 'html':\r\n        return html();\r\n      case 'javascript':\r\n      case 'typescript':\r\n        return javascript();\r\n      case 'python':\r\n        return python();\r\n        \r\n      default:\r\n        return javascript();\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (containerRef.current && !editorRef.current) {\r\n      const startState = EditorState.create({\r\n        doc: content,\r\n        extensions: [\r\n          ...customSetup,\r\n          getLanguageExtension(language),\r\n          resolvedTheme === 'dark' ? [oneDark, modernThemeDark] : modernThemeLight,\r\n          mode === 'view' ? EditorView.editable.of(false) : [],\r\n          lineNumbers ? createLineNumbers() : [],\r\n          EditorView.updateListener.of(update => {\r\n            if (update.docChanged && onChange) {\r\n              onChange(update.state.doc.toString());\r\n            }\r\n          }),\r\n        ],\r\n      });\r\n\r\n      editorRef.current = new EditorView({\r\n        state: startState,\r\n        parent: containerRef.current,\r\n      });\r\n    }\r\n\r\n    return () => {\r\n      if (editorRef.current) {\r\n        editorRef.current.destroy();\r\n        editorRef.current = null;\r\n      }\r\n    };\r\n  }, [language, resolvedTheme, mode, lineNumbers]);\r\n\r\n  useEffect(() => {\r\n    if (editorRef.current && content) {\r\n      const currentContent = editorRef.current.state.doc.toString();\r\n\r\n      if (status === 'streaming' || currentContent !== content) {\r\n        const transaction = editorRef.current.state.update({\r\n          changes: {\r\n            from: 0,\r\n            to: currentContent.length,\r\n            insert: content,\r\n          },\r\n          annotations: [Transaction.remote.of(true)],\r\n        });\r\n\r\n        editorRef.current.dispatch(transaction);\r\n      }\r\n    }\r\n  }, [content, status]);\r\n\r\n  return (\r\n    <div\r\n      className=\"relative w-full overflow-hidden rounded-lg border border-zinc-200 dark:border-zinc-800 bg-zinc-50 dark:bg-zinc-900\"\r\n      ref={containerRef}\r\n    />\r\n  );\r\n}\r\n\r\nfunction areEqual(prevProps: EditorProps, nextProps: EditorProps) {\r\n  if (prevProps.status === 'streaming' && nextProps.status === 'streaming')\r\n    return false;\r\n  if (prevProps.content !== nextProps.content) return false;\r\n  if (prevProps.language !== nextProps.language) return false;\r\n  if (prevProps.mode !== nextProps.mode) return false;\r\n  if (prevProps.lineNumbers !== nextProps.lineNumbers) return false;\r\n\r\n  return true;\r\n}\r\n\r\nexport const CodeEditor = memo(PureCodeEditor, areEqual);"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;AAYA,gBAAgB;AAChB,MAAM,mBAAmB,0NAAA,CAAA,aAAU,CAAC,KAAK,CAAC;IACxC,KAAK;QACH,iBAAiB;QACjB,QAAQ;QACR,UAAU;QACV,cAAc;IAChB;IACA,eAAe;QACb,YAAY;QACZ,SAAS;IACX;IACA,YAAY;QACV,SAAS;QACT,YAAY;IACd;IACA,uBAAuB;QACrB,iBAAiB;QACjB,OAAO;IACT;IACA,kBAAkB;QAChB,iBAAiB;IACnB;AACF;AAEA,MAAM,kBAAkB,0NAAA,CAAA,aAAU,CAAC,KAAK,CAAC;IACvC,KAAK;QACH,iBAAiB;QACjB,QAAQ;QACR,UAAU;QACV,cAAc;IAChB;IACA,eAAe;QACb,YAAY;QACZ,SAAS;IACX;IACA,YAAY;QACV,SAAS;QACT,YAAY;IACd;IACA,uBAAuB;QACrB,iBAAiB;QACjB,OAAO;IACT;IACA,kBAAkB;QAChB,iBAAiB;IACnB;AACF;AAEA,MAAM,cAAc;IAClB,qNAAA,CAAA,eAAY;IACZ,0NAAA,CAAA,aAAU,CAAC,YAAY;IACvB,2NAAA,CAAA,cAAW,CAAC,uBAAuB,CAAC,EAAE,CAAC;IACvC,0NAAA,CAAA,aAAU,CAAC,iBAAiB,CAAC,EAAE,CAAC;QAAE,cAAc;IAAM;CACvD;AAWD,SAAS,eAAe,EACtB,OAAO,EACP,MAAM,EACN,WAAW,YAAY,EACvB,OAAO,MAAM,EACb,cAAc,KAAK,EACnB,QAAQ,EACI;;IACZ,MAAM,eAAe,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,YAAY,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,4PAAA,CAAA,WAAQ,AAAD;IAEjC,MAAM,uBAAuB,CAAC;QAC5B,OAAQ;YACN,KAAK;gBACH,OAAO,CAAA,GAAA,yOAAA,CAAA,OAAI,AAAD;YACZ,KAAK;YACL,KAAK;gBACH,OAAO,CAAA,GAAA,qPAAA,CAAA,aAAU,AAAD;YAClB,KAAK;gBACH,OAAO,CAAA,GAAA,6OAAA,CAAA,SAAM,AAAD;YAEd;gBACE,OAAO,CAAA,GAAA,qPAAA,CAAA,aAAU,AAAD;QACpB;IACF;IAEA,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,aAAa,OAAO,IAAI,CAAC,UAAU,OAAO,EAAE;gBAC9C,MAAM,aAAa,2NAAA,CAAA,cAAW,CAAC,MAAM,CAAC;oBACpC,KAAK;oBACL,YAAY;2BACP;wBACH,qBAAqB;wBACrB,kBAAkB,SAAS;4BAAC,yPAAA,CAAA,UAAO;4BAAE;yBAAgB,GAAG;wBACxD,SAAS,SAAS,0NAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,EAAE,CAAC,SAAS,EAAE;wBACpD,cAAc,CAAA,GAAA,0NAAA,CAAA,cAAiB,AAAD,MAAM,EAAE;wBACtC,0NAAA,CAAA,aAAU,CAAC,cAAc,CAAC,EAAE;mEAAC,CAAA;gCAC3B,IAAI,OAAO,UAAU,IAAI,UAAU;oCACjC,SAAS,OAAO,KAAK,CAAC,GAAG,CAAC,QAAQ;gCACpC;4BACF;;qBACD;gBACH;gBAEA,UAAU,OAAO,GAAG,IAAI,0NAAA,CAAA,aAAU,CAAC;oBACjC,OAAO;oBACP,QAAQ,aAAa,OAAO;gBAC9B;YACF;YAEA;4CAAO;oBACL,IAAI,UAAU,OAAO,EAAE;wBACrB,UAAU,OAAO,CAAC,OAAO;wBACzB,UAAU,OAAO,GAAG;oBACtB;gBACF;;QACF;mCAAG;QAAC;QAAU;QAAe;QAAM;KAAY;IAE/C,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,UAAU,OAAO,IAAI,SAAS;gBAChC,MAAM,iBAAiB,UAAU,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ;gBAE3D,IAAI,WAAW,eAAe,mBAAmB,SAAS;oBACxD,MAAM,cAAc,UAAU,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC;wBACjD,SAAS;4BACP,MAAM;4BACN,IAAI,eAAe,MAAM;4BACzB,QAAQ;wBACV;wBACA,aAAa;4BAAC,2NAAA,CAAA,cAAW,CAAC,MAAM,CAAC,EAAE,CAAC;yBAAM;oBAC5C;oBAEA,UAAU,OAAO,CAAC,QAAQ,CAAC;gBAC7B;YACF;QACF;mCAAG;QAAC;QAAS;KAAO;IAEpB,qBACE,sSAAC;QACC,WAAU;QACV,KAAK;;;;;;AAGX;GApFS;;QAUmB,4PAAA,CAAA,WAAQ;;;KAV3B;AAsFT,SAAS,SAAS,SAAsB,EAAE,SAAsB;IAC9D,IAAI,UAAU,MAAM,KAAK,eAAe,UAAU,MAAM,KAAK,aAC3D,OAAO;IACT,IAAI,UAAU,OAAO,KAAK,UAAU,OAAO,EAAE,OAAO;IACpD,IAAI,UAAU,QAAQ,KAAK,UAAU,QAAQ,EAAE,OAAO;IACtD,IAAI,UAAU,IAAI,KAAK,UAAU,IAAI,EAAE,OAAO;IAC9C,IAAI,UAAU,WAAW,KAAK,UAAU,WAAW,EAAE,OAAO;IAE5D,OAAO;AACT;AAEO,MAAM,2BAAa,CAAA,GAAA,sQAAA,CAAA,OAAI,AAAD,EAAE,gBAAgB", "debugId": null}}, {"offset": {"line": 497, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/code-block.tsx"], "sourcesContent": ["'use client';\r\n\r\n// components/code-block.tsx\r\nimport { usePreview } from '@/lib/hooks/use-preview';\r\nimport { Button } from './ui/button';\r\nimport { CopyIcon, FullscreenIcon } from 'lucide-react';\r\nimport { toast } from 'sonner';\r\nimport { CodeEditor } from './code-editor';\r\nimport { useCopyToClipboard } from 'usehooks-ts';\r\nimport { cn } from '@/lib/utils';\r\nimport { BetterTooltip } from './ui/tooltip';\r\n\r\ninterface CodeBlockProps {\r\n  node: any;\r\n  inline: boolean;\r\n  className: string;\r\n  children: any;\r\n}\r\n\r\nexport function CodeBlock({\r\n  node,\r\n  inline,\r\n  className,\r\n  children,\r\n  ...props\r\n}: CodeBlockProps) {\r\n  const match = /language-(\\w+)/.exec(className || '');\r\n  const [_, copyToClipboard] = useCopyToClipboard();\r\n  const { setPreview } = usePreview();\r\n\r\n  if (inline || !match) {\r\n    return (\r\n      <code className={cn(\"font-mono\", \"rounded-md\", {\r\n        \"bg-zinc-100 dark:bg-zinc-800 py-0.5 px-1.5 text-sm\": inline,\r\n        \"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 text-sm py-1 px-2.5 mx-1\": !match\r\n      })} {...props}>\r\n        {children}\r\n      </code>\r\n    );\r\n  }\r\n\r\n  const language = match[1].toLowerCase();\r\n  const content = String(children);\r\n\r\n  const handlePreviewClick = () => {\r\n    setPreview(prev => ({\r\n      ...prev,\r\n      isVisible: true,\r\n      content,\r\n      kind: language,\r\n      title: 'HTML Preview',\r\n      status: 'idle'\r\n    }));\r\n  };\r\n\r\n  return (\r\n    <div className=\"overflow-hidden rounded-xl border border-zinc-200 dark:border-zinc-700\">\r\n      <div className=\"flex items-center justify-between border-b border-zinc-200 dark:border-zinc-700 bg-zinc-50 dark:bg-zinc-900 px-4 py-2\">\r\n        <span className=\"text-xs font-medium text-zinc-500\">\r\n          {language.toUpperCase()}\r\n        </span>\r\n        <div className=\"flex items-center space-x-2\">\r\n          {language === 'html' && (\r\n            <BetterTooltip content=\"미리보기 (완성 후 클릭하세요)\">\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"h-6 w-6\"\r\n                onClick={handlePreviewClick}\r\n              >\r\n                <FullscreenIcon className=\"h-4 w-4\" />\r\n                <span className=\"sr-only\">미리보기</span>\r\n              </Button>\r\n            </BetterTooltip>\r\n\r\n          )}\r\n          <BetterTooltip content=\"복사하기\">\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"icon\"\r\n              className=\"h-6 w-6\"\r\n              onClick={() => {\r\n                copyToClipboard(content);\r\n                toast.success('클립보드에 복사되었습니다.');\r\n              }}\r\n            >\r\n              <CopyIcon className=\"h-4 w-4\" />\r\n              <span className=\"sr-only\">복사하기</span>\r\n            </Button>\r\n          </BetterTooltip>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"p-3 bg-background max-h-[350px] overflow-auto styled-scrollbar\">\r\n        <CodeEditor\r\n          content={content}\r\n          language={language}\r\n          mode=\"view\"\r\n          status=\"streaming\"\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA,4BAA4B;AAC5B;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;AAmBO,SAAS,UAAU,EACxB,IAAI,EACJ,MAAM,EACN,SAAS,EACT,QAAQ,EACR,GAAG,OACY;;IACf,MAAM,QAAQ,iBAAiB,IAAI,CAAC,aAAa;IACjD,MAAM,CAAC,GAAG,gBAAgB,GAAG,CAAA,GAAA,mOAAA,CAAA,qBAAkB,AAAD;IAC9C,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,aAAU,AAAD;IAEhC,IAAI,UAAU,CAAC,OAAO;QACpB,qBACE,sSAAC;YAAK,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,aAAa,cAAc;gBAC7C,sDAAsD;gBACtD,mHAAmH,CAAC;YACtH;YAAK,GAAG,KAAK;sBACV;;;;;;IAGP;IAEA,MAAM,WAAW,KAAK,CAAC,EAAE,CAAC,WAAW;IACrC,MAAM,UAAU,OAAO;IAEvB,MAAM,qBAAqB;QACzB,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,WAAW;gBACX;gBACA,MAAM;gBACN,OAAO;gBACP,QAAQ;YACV,CAAC;IACH;IAEA,qBACE,sSAAC;QAAI,WAAU;;0BACb,sSAAC;gBAAI,WAAU;;kCACb,sSAAC;wBAAK,WAAU;kCACb,SAAS,WAAW;;;;;;kCAEvB,sSAAC;wBAAI,WAAU;;4BACZ,aAAa,wBACZ,sSAAC,+HAAA,CAAA,gBAAa;gCAAC,SAAQ;0CACrB,cAAA,sSAAC,8HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;;sDAET,sSAAC,ySAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;sDAC1B,sSAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;0CAKhC,sSAAC,+HAAA,CAAA,gBAAa;gCAAC,SAAQ;0CACrB,cAAA,sSAAC,8HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;wCACP,gBAAgB;wCAChB,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oCAChB;;sDAEA,sSAAC,6RAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,sSAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMlC,sSAAC;gBAAI,WAAU;0BACb,cAAA,sSAAC,gIAAA,CAAA,aAAU;oBACT,SAAS;oBACT,UAAU;oBACV,MAAK;oBACL,QAAO;;;;;;;;;;;;;;;;;AAKjB;GApFgB;;QAQe,mOAAA,CAAA,qBAAkB;QACxB,iIAAA,CAAA,aAAU;;;KATnB", "debugId": null}}, {"offset": {"line": 695, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/markdown.tsx"], "sourcesContent": ["import Link from 'next/link';\r\nimport React, { memo } from 'react';\r\nimport ReactMarkdown, { type Components } from 'react-markdown';\r\nimport remarkGfm from 'remark-gfm';\r\nimport { CodeBlock } from './code-block';\r\n\r\nconst components: Partial<Components> = {\r\n  code: CodeBlock,\r\n  p: ({ node, children, ...props }) => {\r\n    return (\r\n      <p className=\"px-2\" {...props}>\r\n        {children}\r\n      </p>\r\n    );\r\n  },\r\n  ol: ({ node, children, ...props }) => {\r\n  return (\r\n    <ol\r\n      className=\"space-y-1.5 pl-0 counter-reset-[custom-counter] text-sm leading-relaxed\"\r\n      style={{ counterReset: 'custom-counter' }}\r\n      {...props}\r\n    >\r\n      {children}\r\n    </ol>\r\n  );\r\n},\r\nli: ({ node, children, ...props }) => {\r\n  return (\r\n    <li className=\"relative pl-4  text-gray-800 before:content-['•'] before:absolute before:left-0 before:text-gray-400\" {...props}>\r\n      {children}\r\n    </li>\r\n  );\r\n},\r\n  ul: ({ node, children, ...props }) => {\r\n    return (\r\n      <ul className=\" list-outside ml-4\" {...props}>\r\n        {children}\r\n      </ul>\r\n    );\r\n  },\r\n  strong: ({ node, children, ...props }) => {\r\n    return (\r\n      <span className=\"font-semibold\" {...props}>\r\n        {children}\r\n      </span>\r\n    );\r\n  },\r\n  a: ({ node, children, ...props }) => {\r\n    return (\r\n      <Link\r\n        className=\"text-blue-500 hover:underline\"\r\n        target=\"_blank\"\r\n        rel=\"noreferrer\"\r\n        {...props}\r\n      >\r\n        {children}\r\n      </Link>\r\n    );\r\n  },\r\n  h1: ({ node, children, ...props }) => {\r\n    return (\r\n      <h1 className=\"text-3xl font-semibold mt-6 mb-2\" {...props}>\r\n        {children}\r\n      </h1>\r\n    );\r\n  },\r\n  h2: ({ node, children, ...props }) => {\r\n    return (\r\n      <h2 className=\"text-2xl font-semibold mt-6 mb-2\" {...props}>\r\n        {children}\r\n      </h2>\r\n    );\r\n  },\r\n  h3: ({ node, children, ...props }) => {\r\n    return (\r\n      <h3 className=\"text-xl font-semibold mt-6 mb-2\" {...props}>\r\n        {children}\r\n      </h3>\r\n    );\r\n  },\r\n  h4: ({ node, children, ...props }) => {\r\n    return (\r\n      <h4 className=\"text-lg font-semibold mt-6 mb-2\" {...props}>\r\n        {children}\r\n      </h4>\r\n    );\r\n  },\r\n  h5: ({ node, children, ...props }) => {\r\n    return (\r\n      <h5 className=\"text-base font-semibold mt-6 mb-2\" {...props}>\r\n        {children}\r\n      </h5>\r\n    );\r\n  },\r\n  h6: ({ node, children, ...props }) => {\r\n    return (\r\n      <h6 className=\"text-sm font-semibold mt-6 mb-2\" {...props}>\r\n        {children}\r\n      </h6>\r\n    );\r\n  },\r\n  table: ({ node, children, ...props }) => {\r\n    return (\r\n      <table className=\"min-w-full border-collapse border my-4\" {...props}>\r\n        {children}\r\n      </table>\r\n    );\r\n  },\r\n  thead: ({ node, children, ...props }) => {\r\n    return (\r\n      <thead {...props}>\r\n        {children}\r\n      </thead>\r\n    );\r\n  },\r\n  tr: ({ node, children, ...props }) => {\r\n    return (\r\n      <tr className=\"border-b \" {...props}>\r\n        {children}\r\n      </tr>\r\n    );\r\n  },\r\n  th: ({ node, children, ...props }) => {\r\n    return (\r\n      <th className=\"px-6 py-3 text-left text-sm font-semibold  border-r  last:border-r-0\" {...props}>\r\n        {children}\r\n      </th>\r\n    );\r\n  },\r\n  td: ({ node, children, ...props }) => {\r\n    return (\r\n      <td className=\"px-6 py-4 text-sm border-r last:border-r-0\" {...props}>\r\n        {children}\r\n      </td>\r\n    );\r\n  },\r\n};\r\n\r\nconst remarkPlugins = [remarkGfm];\r\n\r\nconst NonMemoizedMarkdown = ({ children }: { children: string }) => {\r\n  return (\r\n    <ReactMarkdown remarkPlugins={remarkPlugins} components={components}>\r\n      {children}\r\n    </ReactMarkdown>\r\n  );\r\n};\r\n\r\nexport const Markdown = memo(\r\n  NonMemoizedMarkdown,\r\n  (prevProps, nextProps) => prevProps.children === nextProps.children,\r\n);\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAEA,MAAM,aAAkC;IACtC,MAAM,+HAAA,CAAA,YAAS;IACf,GAAG,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;QAC9B,qBACE,sSAAC;YAAE,WAAU;YAAQ,GAAG,KAAK;sBAC1B;;;;;;IAGP;IACA,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;QACjC,qBACE,sSAAC;YACC,WAAU;YACV,OAAO;gBAAE,cAAc;YAAiB;YACvC,GAAG,KAAK;sBAER;;;;;;IAGP;IACA,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;QAC/B,qBACE,sSAAC;YAAG,WAAU;YAAwG,GAAG,KAAK;sBAC3H;;;;;;IAGP;IACE,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;QAC/B,qBACE,sSAAC;YAAG,WAAU;YAAsB,GAAG,KAAK;sBACzC;;;;;;IAGP;IACA,QAAQ,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;QACnC,qBACE,sSAAC;YAAK,WAAU;YAAiB,GAAG,KAAK;sBACtC;;;;;;IAGP;IACA,GAAG,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;QAC9B,qBACE,sSAAC,wQAAA,CAAA,UAAI;YACH,WAAU;YACV,QAAO;YACP,KAAI;YACH,GAAG,KAAK;sBAER;;;;;;IAGP;IACA,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;QAC/B,qBACE,sSAAC;YAAG,WAAU;YAAoC,GAAG,KAAK;sBACvD;;;;;;IAGP;IACA,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;QAC/B,qBACE,sSAAC;YAAG,WAAU;YAAoC,GAAG,KAAK;sBACvD;;;;;;IAGP;IACA,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;QAC/B,qBACE,sSAAC;YAAG,WAAU;YAAmC,GAAG,KAAK;sBACtD;;;;;;IAGP;IACA,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;QAC/B,qBACE,sSAAC;YAAG,WAAU;YAAmC,GAAG,KAAK;sBACtD;;;;;;IAGP;IACA,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;QAC/B,qBACE,sSAAC;YAAG,WAAU;YAAqC,GAAG,KAAK;sBACxD;;;;;;IAGP;IACA,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;QAC/B,qBACE,sSAAC;YAAG,WAAU;YAAmC,GAAG,KAAK;sBACtD;;;;;;IAGP;IACA,OAAO,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;QAClC,qBACE,sSAAC;YAAM,WAAU;YAA0C,GAAG,KAAK;sBAChE;;;;;;IAGP;IACA,OAAO,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;QAClC,qBACE,sSAAC;YAAO,GAAG,KAAK;sBACb;;;;;;IAGP;IACA,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;QAC/B,qBACE,sSAAC;YAAG,WAAU;YAAa,GAAG,KAAK;sBAChC;;;;;;IAGP;IACA,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;QAC/B,qBACE,sSAAC;YAAG,WAAU;YAAwE,GAAG,KAAK;sBAC3F;;;;;;IAGP;IACA,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;QAC/B,qBACE,sSAAC;YAAG,WAAU;YAA8C,GAAG,KAAK;sBACjE;;;;;;IAGP;AACF;AAEA,MAAM,gBAAgB;IAAC,0MAAA,CAAA,UAAS;CAAC;AAEjC,MAAM,sBAAsB,CAAC,EAAE,QAAQ,EAAwB;IAC7D,qBACE,sSAAC,kTAAA,CAAA,UAAa;QAAC,eAAe;QAAe,YAAY;kBACtD;;;;;;AAGP;KANM;AAQC,MAAM,yBAAW,CAAA,GAAA,sQAAA,CAAA,OAAI,AAAD,EACzB,qBACA,CAAC,WAAW,YAAc,UAAU,QAAQ,KAAK,UAAU,QAAQ", "debugId": null}}, {"offset": {"line": 933, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/message-editor.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { ChatRequestOptions, Message } from 'ai';\r\nimport { Dispatch, SetStateAction, useEffect, useRef, useState } from 'react';\r\nimport { toast } from 'sonner';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Textarea } from '@/components/ui/textarea';\r\nimport { useUserMessageId } from '@/lib/hooks/use-user-message-id';\r\nimport { deleteTrailingMessages } from '@/app/(map)/actions';\r\n\r\nexport type MessageEditorProps = {\r\n  message: Message;\r\n  setMode: Dispatch<SetStateAction<'view' | 'edit'>>;\r\n  setMessages: (\r\n    messages: Message[] | ((messages: Message[]) => Message[]),\r\n  ) => void;\r\n  reload: (\r\n    chatRequestOptions?: ChatRequestOptions,\r\n  ) => Promise<string | null | undefined>;\r\n};\r\n\r\nexport function MessageEditor({\r\n  message,\r\n  setMode,\r\n  setMessages,\r\n  reload,\r\n}: MessageEditorProps) {\r\n  const { userMessageIdFromServer } = useUserMessageId();\r\n  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);\r\n\r\n  const [draftContent, setDraftContent] = useState<string>(message.content);\r\n  const textareaRef = useRef<HTMLTextAreaElement>(null);\r\n\r\n  useEffect(() => {\r\n    if (textareaRef.current) {\r\n      adjustHeight();\r\n    }\r\n  }, []);\r\n\r\n  const adjustHeight = () => {\r\n    if (textareaRef.current) {\r\n      textareaRef.current.style.height = 'auto';\r\n      textareaRef.current.style.height = `${textareaRef.current.scrollHeight + 2}px`;\r\n    }\r\n  };\r\n\r\n  const handleInput = (event: React.ChangeEvent<HTMLTextAreaElement>) => {\r\n    setDraftContent(event.target.value);\r\n    adjustHeight();\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex flex-col gap-2 w-full\">\r\n      <Textarea\r\n        ref={textareaRef}\r\n        className=\"bg-transparent outline-none overflow-hidden resize-none !text-base rounded-xl w-full\"\r\n        value={draftContent}\r\n        onChange={handleInput}\r\n      />\r\n\r\n      <div className=\"flex flex-row gap-2 justify-end\">\r\n        <Button\r\n          variant=\"outline\"\r\n          className=\"h-fit py-2 px-3\"\r\n          onClick={() => {\r\n            setMode('view');\r\n          }}\r\n        >\r\n          취소\r\n        </Button>\r\n        <Button\r\n          variant=\"default\"\r\n          className=\"h-fit py-2 px-3\"\r\n          disabled={isSubmitting}\r\n          onClick={async () => {\r\n            setIsSubmitting(true);\r\n            const messageId = userMessageIdFromServer ?? message.id;\r\n\r\n            if (!messageId) {\r\n              toast.error('Something went wrong, please try again!');\r\n              setIsSubmitting(false);\r\n              return;\r\n            }\r\n\r\n            await deleteTrailingMessages({\r\n              id: messageId,\r\n            });\r\n\r\n            setMessages((messages) => {\r\n              const index = messages.findIndex((m) => m.id === message.id);\r\n\r\n              if (index !== -1) {\r\n                const updatedMessage = {\r\n                  ...message,\r\n                  content: draftContent,\r\n                };\r\n\r\n                return [...messages.slice(0, index), updatedMessage];\r\n              }\r\n\r\n              return messages;\r\n            });\r\n\r\n            setMode('view');\r\n            reload();\r\n          }}\r\n        >\r\n          {isSubmitting ? '전송중...' : '전송'}\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;AAqBO,SAAS,cAAc,EAC5B,OAAO,EACP,OAAO,EACP,WAAW,EACX,MAAM,EACa;;IACnB,MAAM,EAAE,uBAAuB,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAW;IAE1D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAU,QAAQ,OAAO;IACxE,MAAM,cAAc,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAAuB;IAEhD,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,YAAY,OAAO,EAAE;gBACvB;YACF;QACF;kCAAG,EAAE;IAEL,MAAM,eAAe;QACnB,IAAI,YAAY,OAAO,EAAE;YACvB,YAAY,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG;YACnC,YAAY,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,YAAY,OAAO,CAAC,YAAY,GAAG,EAAE,EAAE,CAAC;QAChF;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,gBAAgB,MAAM,MAAM,CAAC,KAAK;QAClC;IACF;IAEA,qBACE,sSAAC;QAAI,WAAU;;0BACb,sSAAC,gIAAA,CAAA,WAAQ;gBACP,KAAK;gBACL,WAAU;gBACV,OAAO;gBACP,UAAU;;;;;;0BAGZ,sSAAC;gBAAI,WAAU;;kCACb,sSAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,WAAU;wBACV,SAAS;4BACP,QAAQ;wBACV;kCACD;;;;;;kCAGD,sSAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,WAAU;wBACV,UAAU;wBACV,SAAS;4BACP,gBAAgB;4BAChB,MAAM,YAAY,2BAA2B,QAAQ,EAAE;4BAEvD,IAAI,CAAC,WAAW;gCACd,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gCACZ,gBAAgB;gCAChB;4BACF;4BAEA,MAAM,CAAA,GAAA,yJAAA,CAAA,yBAAsB,AAAD,EAAE;gCAC3B,IAAI;4BACN;4BAEA,YAAY,CAAC;gCACX,MAAM,QAAQ,SAAS,SAAS,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,QAAQ,EAAE;gCAE3D,IAAI,UAAU,CAAC,GAAG;oCAChB,MAAM,iBAAiB;wCACrB,GAAG,OAAO;wCACV,SAAS;oCACX;oCAEA,OAAO;2CAAI,SAAS,KAAK,CAAC,GAAG;wCAAQ;qCAAe;gCACtD;gCAEA,OAAO;4BACT;4BAEA,QAAQ;4BACR;wBACF;kCAEC,eAAe,WAAW;;;;;;;;;;;;;;;;;;AAKrC;GA3FgB;;QAMsB,+IAAA,CAAA,mBAAgB;;;KANtC", "debugId": null}}, {"offset": {"line": 1071, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/message-actions.tsx"], "sourcesContent": ["import type { Message } from 'ai';\r\nimport { toast } from 'sonner';\r\nimport { useSWRConfig } from 'swr';\r\nimport { useCopyToClipboard } from 'usehooks-ts';\r\n\r\n// import type { Vote } from '@/lib/db/schema';\r\nimport { getMessageIdFromAnnotations } from '@/lib/utils';\r\n\r\nimport { memo } from 'react';\r\nimport equal from 'fast-deep-equal';\r\nimport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';\r\nimport { Button } from '@/components/ui/button';\r\nimport { CopyIcon } from 'lucide-react';\r\nimport { ThumbDownIcon, ThumbUpIcon } from '@/components/ui/icons';\r\nimport { Vote } from '@/lib/db/schema';\r\n\r\nexport function PureMessageActions({\r\n  chatId,\r\n  message,\r\n  vote,\r\n  isLoading,\r\n}: {\r\n  chatId: string;\r\n  message: Message;\r\n  vote: Vote | undefined;\r\n  isLoading: boolean;\r\n}) {\r\n  const { mutate } = useSWRConfig();\r\n  const [_, copyToClipboard] = useCopyToClipboard();\r\n\r\n  if (isLoading) return null;\r\n  if (message.role === 'user') return null;\r\n  // if (message.toolInvocations && message.toolInvocations.length > 0)\r\n    // return null;\r\n\r\n  return (\r\n    <TooltipProvider delayDuration={0}>\r\n      <div className=\"flex ml-2 flex-row gap-2\">\r\n        <Tooltip>\r\n          <TooltipTrigger asChild>\r\n            <Button\r\n              className=\"py-1 px-2 h-fit text-muted-foreground\"\r\n              variant=\"outline\"\r\n              onClick={async () => {\r\n                await copyToClipboard(message.content as string);\r\n                toast.success('클립보드에 복사되었습니다!');\r\n              }}\r\n            >\r\n              <CopyIcon />\r\n            </Button>\r\n          </TooltipTrigger>\r\n          <TooltipContent>복사하기</TooltipContent>\r\n        </Tooltip>\r\n\r\n        {/* <Tooltip>\r\n          <TooltipTrigger asChild>\r\n            <Button\r\n              className=\"py-1 px-2 h-fit text-muted-foreground !pointer-events-auto\"\r\n              // disabled={vote?.isUpvoted}\r\n              variant=\"outline\"\r\n              onClick={async () => {\r\n                const messageId = getMessageIdFromAnnotations(message);\r\n\r\n                const upvote = fetch('/api/vote', {\r\n                  method: 'POST',\r\n                  body: JSON.stringify({\r\n                    chatId,\r\n                    messageId,\r\n                    type: 'up',\r\n                  }),\r\n                });\r\n\r\n                toast.promise(upvote, {\r\n                  loading: '응답을 추천하는 중...',\r\n                  success: () => {\r\n                    mutate<Array<Vote>>(\r\n                      `/api/vote?chatId=${chatId}`,\r\n                      (currentVotes) => {\r\n                        if (!currentVotes) return [];\r\n\r\n                        const votesWithoutCurrent = currentVotes.filter(\r\n                          (vote) => vote.messageId !== message.id,\r\n                        );\r\n\r\n                        return [\r\n                          ...votesWithoutCurrent,\r\n                          {\r\n                            chatId,\r\n                            messageId: message.id,\r\n                            isUpvoted: true,\r\n                          },\r\n                        ];\r\n                      },\r\n                      { revalidate: false },\r\n                    );\r\n\r\n                    return '응답을 추천했습니다!';\r\n                  },\r\n                  error: '응답 추천에 실패했습니다.',\r\n                });\r\n              }}\r\n            >\r\n              <ThumbUpIcon />\r\n            </Button>\r\n          </TooltipTrigger>\r\n          <TooltipContent>좋아요</TooltipContent>\r\n        </Tooltip>\r\n\r\n        <Tooltip>\r\n          <TooltipTrigger asChild>\r\n            <Button\r\n              className=\"py-1 px-2 h-fit text-muted-foreground !pointer-events-auto\"\r\n              variant=\"outline\"\r\n              // disabled={vote && !vote.isUpvoted}\r\n              onClick={async () => {\r\n                const messageId = getMessageIdFromAnnotations(message);\r\n\r\n                const downvote = fetch('/api/vote', {\r\n                  method: 'PATCH',\r\n                  body: JSON.stringify({\r\n                    chatId,\r\n                    messageId,\r\n                    type: 'down',\r\n                  }),\r\n                });\r\n\r\n                toast.promise(downvote, {\r\n                  loading: '응답을 비추천하는 중...',\r\n                  success: () => {\r\n                    mutate<Array<Vote>>(\r\n                      `/api/vote?chatId=${chatId}`,\r\n                      (currentVotes) => {\r\n                        if (!currentVotes) return [];\r\n\r\n                        const votesWithoutCurrent = currentVotes.filter(\r\n                          (vote) => vote.messageId !== message.id,\r\n                        );\r\n\r\n                        return [\r\n                          ...votesWithoutCurrent,\r\n                          {\r\n                            chatId,\r\n                            messageId: message.id,\r\n                            isUpvoted: false,\r\n                          },\r\n                        ];\r\n                      },\r\n                      { revalidate: false },\r\n                    );\r\n\r\n                    return '응답을 비추천했습니다!';\r\n                  },\r\n                  error: '응답 비추천에 실패했습니다.',\r\n                });\r\n              }}\r\n            >\r\n              <ThumbDownIcon />\r\n            </Button>\r\n          </TooltipTrigger>\r\n          <TooltipContent>별로에요</TooltipContent>\r\n        </Tooltip> */}\r\n      </div>\r\n    </TooltipProvider>\r\n  );\r\n}\r\n\r\nexport const MessageActions = memo(\r\n  PureMessageActions,\r\n  (prevProps, nextProps) => {\r\n    // if (!equal(prevProps.vote, nextProps.vote)) return false;\r\n    if (prevProps.isLoading !== nextProps.isLoading) return false;\r\n\r\n    return true;\r\n  },\r\n);\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAAA;AACA;AAKA;AAEA;AACA;AACA;;;;;;;;;;AAIO,SAAS,mBAAmB,EACjC,MAAM,EACN,OAAO,EACP,IAAI,EACJ,SAAS,EAMV;;IACC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,2NAAA,CAAA,eAAY,AAAD;IAC9B,MAAM,CAAC,GAAG,gBAAgB,GAAG,CAAA,GAAA,mOAAA,CAAA,qBAAkB,AAAD;IAE9C,IAAI,WAAW,OAAO;IACtB,IAAI,QAAQ,IAAI,KAAK,QAAQ,OAAO;IACpC,qEAAqE;IACnE,eAAe;IAEjB,qBACE,sSAAC,+HAAA,CAAA,kBAAe;QAAC,eAAe;kBAC9B,cAAA,sSAAC;YAAI,WAAU;sBACb,cAAA,sSAAC,+HAAA,CAAA,UAAO;;kCACN,sSAAC,+HAAA,CAAA,iBAAc;wBAAC,OAAO;kCACrB,cAAA,sSAAC,8HAAA,CAAA,SAAM;4BACL,WAAU;4BACV,SAAQ;4BACR,SAAS;gCACP,MAAM,gBAAgB,QAAQ,OAAO;gCACrC,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;4BAChB;sCAEA,cAAA,sSAAC,6RAAA,CAAA,WAAQ;;;;;;;;;;;;;;;kCAGb,sSAAC,+HAAA,CAAA,iBAAc;kCAAC;;;;;;;;;;;;;;;;;;;;;;AAiH1B;GApJgB;;QAWK,2NAAA,CAAA,eAAY;QACF,mOAAA,CAAA,qBAAkB;;;KAZjC;AAsJT,MAAM,+BAAiB,CAAA,GAAA,sQAAA,CAAA,OAAI,AAAD,EAC/B,oBACA,CAAC,WAAW;IACV,4DAA4D;IAC5D,IAAI,UAAU,SAAS,KAAK,UAAU,SAAS,EAAE,OAAO;IAExD,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1180, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/reasoning.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useRef, useEffect, useId } from \"react\";\r\nimport {\r\n  Accordion,\r\n  AccordionItem,\r\n  AccordionTrigger,\r\n  AccordionContent\r\n} from \"@/components/ui/accordion\";\r\nimport { BrainCircuit } from \"lucide-react\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { AnimatedShinyText } from \"./magicui/animated-shiny-text\";\r\n\r\nconst thinkingPhrases = [\r\n  \"더 나은 답변을 위해 고민 중이에요...\",\r\n  \"조금만 기다려 주세요...\",\r\n];\r\n\r\nconst completedPhrases = [\r\n  \"생각 완료\",\r\n];\r\n\r\n\r\n\r\ninterface ReasoningProps {\r\n  reasoning: string;\r\n  className?: string;\r\n  initialOpen?: boolean;\r\n  open?: boolean;\r\n  isReasoning?: boolean;\r\n}\r\n\r\nexport function Reasoning({ reasoning, className, initialOpen = true, open, isReasoning }: ReasoningProps) {\r\n  const contentRef = useRef<HTMLDivElement>(null);\r\n  const uniqueId = useId();\r\n  const itemValue = `reasoning-${uniqueId}`;\r\n  const [isOpenInternal, setIsOpenInternal] = useState(initialOpen);\r\n\r\n  useEffect(() => {\r\n    if (open !== undefined) {\r\n      setIsOpenInternal(open);\r\n    }\r\n  }, [open]);\r\n\r\n  const accordionValue = isOpenInternal ? itemValue : \"\";\r\n\r\n  useEffect(() => {\r\n    if (isOpenInternal && contentRef.current) {\r\n      contentRef.current.scrollTop = contentRef.current.scrollHeight;\r\n    }\r\n  }, [reasoning, isOpenInternal]);\r\n\r\n\r\n  const [currentPhrase, setCurrentPhrase] = useState(\"\");\r\n  const [isAnimating, setIsAnimating] = useState(false);\r\n\r\n  useEffect(() => {\r\n    if (isReasoning) {\r\n      // 초기 메시지 설정\r\n      const randomIndex = Math.floor(Math.random() * thinkingPhrases.length);\r\n      setCurrentPhrase(thinkingPhrases[randomIndex]);\r\n      setIsAnimating(true);\r\n      \r\n      // 3초마다 메시지 변경\r\n      const interval = setInterval(() => {\r\n        const newIndex = Math.floor(Math.random() * thinkingPhrases.length);\r\n        setCurrentPhrase(thinkingPhrases[newIndex]);\r\n      }, 3000);\r\n      \r\n      return () => {\r\n        clearInterval(interval);\r\n        setIsAnimating(false);\r\n      };\r\n    } else {\r\n      // 완료 상태일 때는 랜덤한 완료 메시지 표시\r\n      const randomIndex = Math.floor(Math.random() * completedPhrases.length);\r\n      setCurrentPhrase(completedPhrases[randomIndex]);\r\n    }\r\n  }, [isReasoning]);\r\n\r\n  return (\r\n    <Accordion \r\n      type=\"single\" \r\n      value={accordionValue}\r\n      onValueChange={(val) => setIsOpenInternal(val === itemValue)}\r\n      className={cn(\"w-full h-auto\", className)}\r\n      collapsible\r\n    >\r\n      <AccordionItem value={itemValue} className=\"border-b-0 rounded-lg overflow-hidden bg-card\">\r\n        <AccordionTrigger className=\"px-3 py-0 h-6 min-h-6\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            {isReasoning ? (\r\n              <div className=\"flex items-center gap-2\">\r\n                <BrainCircuit \r\n                  className={`h-4 w-4 text-yellow-500 ${isAnimating ? 'animate-pulse' : ''}`} \r\n                />\r\n                <AnimatedShinyText \r\n                  key={currentPhrase}\r\n                  className=\"text-sm\"\r\n                >\r\n                  {currentPhrase}\r\n                </AnimatedShinyText>\r\n              </div>\r\n            ) : (\r\n              <div className=\"flex items-center gap-2\">\r\n                <BrainCircuit \r\n                  className=\"h-4 w-4 text-green-500\" \r\n                />\r\n                <span className=\"text-sm text-muted-foreground\">\r\n                  {currentPhrase}\r\n                </span>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </AccordionTrigger>\r\n        <AccordionContent className=\"px-2 mt-2\">\r\n          <div \r\n            ref={contentRef}\r\n            className=\"text-sm text-gray-500 bg-accent/80 max-h-[150px] overflow-y-auto whitespace-pre-wrap px-4 py-2 border-l-2 border-yellow-400 pl-3 ml-1\"\r\n          >\r\n            {reasoning}\r\n          </div>\r\n        </AccordionContent>\r\n      </AccordionItem>\r\n    </Accordion>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAMA;AACA;AACA;;;AAXA;;;;;;AAaA,MAAM,kBAAkB;IACtB;IACA;CACD;AAED,MAAM,mBAAmB;IACvB;CACD;AAYM,SAAS,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,cAAc,IAAI,EAAE,IAAI,EAAE,WAAW,EAAkB;;IACvG,MAAM,aAAa,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,WAAW,CAAA,GAAA,sQAAA,CAAA,QAAK,AAAD;IACrB,MAAM,YAAY,CAAC,UAAU,EAAE,UAAU;IACzC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,SAAS,WAAW;gBACtB,kBAAkB;YACpB;QACF;8BAAG;QAAC;KAAK;IAET,MAAM,iBAAiB,iBAAiB,YAAY;IAEpD,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,kBAAkB,WAAW,OAAO,EAAE;gBACxC,WAAW,OAAO,CAAC,SAAS,GAAG,WAAW,OAAO,CAAC,YAAY;YAChE;QACF;8BAAG;QAAC;QAAW;KAAe;IAG9B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,aAAa;gBACf,YAAY;gBACZ,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,gBAAgB,MAAM;gBACrE,iBAAiB,eAAe,CAAC,YAAY;gBAC7C,eAAe;gBAEf,cAAc;gBACd,MAAM,WAAW;oDAAY;wBAC3B,MAAM,WAAW,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,gBAAgB,MAAM;wBAClE,iBAAiB,eAAe,CAAC,SAAS;oBAC5C;mDAAG;gBAEH;2CAAO;wBACL,cAAc;wBACd,eAAe;oBACjB;;YACF,OAAO;gBACL,0BAA0B;gBAC1B,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,iBAAiB,MAAM;gBACtE,iBAAiB,gBAAgB,CAAC,YAAY;YAChD;QACF;8BAAG;QAAC;KAAY;IAEhB,qBACE,sSAAC,iIAAA,CAAA,YAAS;QACR,MAAK;QACL,OAAO;QACP,eAAe,CAAC,MAAQ,kBAAkB,QAAQ;QAClD,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC/B,WAAW;kBAEX,cAAA,sSAAC,iIAAA,CAAA,gBAAa;YAAC,OAAO;YAAW,WAAU;;8BACzC,sSAAC,iIAAA,CAAA,mBAAgB;oBAAC,WAAU;8BAC1B,cAAA,sSAAC;wBAAI,WAAU;kCACZ,4BACC,sSAAC;4BAAI,WAAU;;8CACb,sSAAC,6SAAA,CAAA,eAAY;oCACX,WAAW,CAAC,wBAAwB,EAAE,cAAc,kBAAkB,IAAI;;;;;;8CAE5E,sSAAC,sJAAA,CAAA,oBAAiB;oCAEhB,WAAU;8CAET;mCAHI;;;;;;;;;;iDAOT,sSAAC;4BAAI,WAAU;;8CACb,sSAAC,6SAAA,CAAA,eAAY;oCACX,WAAU;;;;;;8CAEZ,sSAAC;oCAAK,WAAU;8CACb;;;;;;;;;;;;;;;;;;;;;;8BAMX,sSAAC,iIAAA,CAAA,mBAAgB;oBAAC,WAAU;8BAC1B,cAAA,sSAAC;wBACC,KAAK;wBACL,WAAU;kCAET;;;;;;;;;;;;;;;;;;;;;;AAMb;GA9FgB;;QAEG,sQAAA,CAAA,QAAK;;;KAFR", "debugId": null}}, {"offset": {"line": 1377, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/annotations.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport type { JSONValue } from \"ai\";\r\nimport { Message as AIMessage } from \"ai\";\r\nimport React from \"react\";\r\nimport { Reasoning } from \"./reasoning\";\r\nimport { Badge } from \"./ui/badge\";\r\nimport { Collapsible, CollapsibleContent, CollapsibleTrigger } from \"./ui/collapsible\";\r\nimport { ProgressStages, type ProgressStage } from \"./progress-stages\";\r\nimport {\r\n  Search,\r\n  MapPin,\r\n  Route,\r\n  Layers3,\r\n  Settings,\r\n  MessageSquare,\r\n  CheckCircle2,\r\n  Zap,\r\n  Filter,\r\n  Edit2,\r\n  Edit,\r\n  Eraser,\r\n  Map,\r\n  ChevronDown,\r\n  ChevronUp\r\n} from \"lucide-react\";\r\nimport { AnimatePresence, motion } from \"framer-motion\";\r\nimport { AnimatedShinyText } from \"./magicui/animated-shiny-text\";\r\nimport { getLayerAttributesCount } from \"@/app/(map)/api/chat/agents/tools\";\r\n// getAgentFriendlyMessage import 제거 - 더 이상 사용하지 않음\r\n// Orchestrator Status Annotation Types\r\ntype OrchestratorStatusValue = \"planning\" | \"executing_step\" | \"done\";\r\n\r\ninterface BaseOrchestratorStatusAnnotation {\r\n  type: \"status\";\r\n  value: OrchestratorStatusValue;\r\n  [key: string]: JSONValue;\r\n}\r\n\r\ninterface PlanningStatusAnnotation extends BaseOrchestratorStatusAnnotation {\r\n  value: \"planning\";\r\n}\r\n\r\nexport interface ExecutingStepStatusAnnotation\r\n  extends BaseOrchestratorStatusAnnotation {\r\n  value: \"executing_step\";\r\n  agentName: string;\r\n  stepNumber: number;\r\n  totalSteps: number;\r\n  goal: string;\r\n}\r\n\r\ninterface DoneStatusAnnotation extends BaseOrchestratorStatusAnnotation {\r\n  value: \"done\";\r\n}\r\n\r\nexport type OrchestratorStatusAnnotation =\r\n  | PlanningStatusAnnotation\r\n  | ExecutingStepStatusAnnotation\r\n  | DoneStatusAnnotation;\r\n\r\n// Application-specific Annotation Types\r\ninterface BaseAppAnnotation {\r\n  type: string;\r\n  message: JSONValue;\r\n  [key: string]: JSONValue | undefined;\r\n}\r\n\r\nexport interface AgentStartAnnotation extends BaseAppAnnotation {\r\n  type: \"agent_start\";\r\n  agent: string;\r\n  message: string;\r\n}\r\n\r\nexport interface IntentAnalyzedAnnotation extends BaseAppAnnotation {\r\n  type: \"intent_analyzed\";\r\n  intent: string;\r\n  message: string;\r\n}\r\n\r\nexport interface AgentCompletedAnnotation extends BaseAppAnnotation {\r\n  type: \"agent_completed\";\r\n  agent: string;\r\n  message: string;\r\n}\r\n\r\nexport interface ProgressUpdateAnnotation extends BaseAppAnnotation {\r\n  type: \"progress_update\";\r\n  message: string;\r\n  stage: \"analyzing\" | \"processing\" | \"calculating\" | \"completing\";\r\n}\r\n\r\nexport interface ToolCallAnnotation extends BaseAppAnnotation {\r\n  type: \"tool_call\";\r\n  toolName: string;\r\n  args: any;\r\n}\r\n\r\n// 평가 관련 어노테이션 타입들\r\nexport interface EvaluationStartAnnotation extends BaseAppAnnotation {\r\n  type: \"evaluation_start\";\r\n  iteration: number;\r\n  maxIterations: number;\r\n  message: string;\r\n}\r\n\r\nexport interface EvaluationCompletedAnnotation extends BaseAppAnnotation {\r\n  type: \"evaluation_completed\";\r\n  iteration: number;\r\n  maxIterations: number;\r\n  isCompleted: boolean;\r\n  shouldContinue: boolean;\r\n  message: string;\r\n  reason: string;\r\n  improvementSuggestions?: JSONValue;\r\n}\r\n\r\nexport interface RetryStartingAnnotation extends BaseAppAnnotation {\r\n  type: \"retry_starting\";\r\n  iteration: number;\r\n  maxIterations: number;\r\n  message: string;\r\n  reason: string;\r\n  improvementSuggestions: JSONValue;\r\n}\r\n\r\nexport interface RetryCompletedAnnotation extends BaseAppAnnotation {\r\n  type: \"retry_completed\";\r\n  totalIterations: number;\r\n  maxIterations: number;\r\n  finalResult: \"success\" | \"partial\";\r\n  message: string;\r\n}\r\n\r\nexport interface RetryLimitReachedAnnotation extends BaseAppAnnotation {\r\n  type: \"retry_limit_reached\";\r\n  totalIterations: number;\r\n  maxIterations: number;\r\n  message: string;\r\n  finalEvaluation: any;\r\n}\r\n\r\nexport type AppSpecificAnnotation =\r\n  | AgentStartAnnotation\r\n  | IntentAnalyzedAnnotation\r\n  | AgentCompletedAnnotation\r\n  | ProgressUpdateAnnotation\r\n  | ToolCallAnnotation\r\n  | EvaluationStartAnnotation\r\n  | EvaluationCompletedAnnotation\r\n  | RetryStartingAnnotation\r\n  | RetryCompletedAnnotation\r\n  | RetryLimitReachedAnnotation;\r\n\r\n// Combined annotation type for filtering\r\nexport type RelevantAnnotation =\r\n  | OrchestratorStatusAnnotation\r\n  | AppSpecificAnnotation;\r\n\r\n\r\n\r\ninterface AnnotationsProps {\r\n  annotations?: AIMessage[\"annotations\"];\r\n  isLoading: boolean;\r\n}\r\n\r\n// 도구 이름을 사용자 친화적으로 변환하고 아이콘 추가\r\nexport const getToolDisplayInfo = (toolName: string) => {\r\n  const toolMap: Record<string, { label: string; icon: React.ReactNode; variant: \"default\" | \"secondary\" | \"outline\" }> = {\r\n    // 검색/조회 도구들 (Search 아이콘)\r\n    getLayer: { label: \"레이어 조회\", icon: <Search className=\"h-3 w-3\" />, variant: \"default\" },\r\n    getLayerInfo: { label: \"레이어 정보\", icon: <Search className=\"h-3 w-3\" />, variant: \"default\" },\r\n    getLayerAttributes: { label: \"속성 조회\", icon: <Search className=\"h-3 w-3\" />, variant: \"default\" },\r\n    getLayerList: { label: \"레이어 검색\", icon: <Search className=\"h-3 w-3\" />, variant: \"default\" },\r\n    searchAddress: { label: \"주소 검색\", icon: <Search className=\"h-3 w-3\" />, variant: \"default\" },\r\n    searchCoord: { label: \"좌표 검색\", icon: <MapPin className=\"h-3 w-3 text-purple-600\" />, variant: \"default\" },\r\n    searchOrigin: { label: \"출발지 검색\", icon: <MapPin className=\"h-3 w-3 text-green-600\" />, variant: \"default\" },\r\n    searchDestination: { label: \"목적지 검색\", icon: <MapPin className=\"h-3 w-3 text-red-600\" />, variant: \"default\" },\r\n    searchDirections: { label: \"경로 탐색\", icon: <Route className=\"h-3 w-3\" />, variant: \"default\" },\r\n\r\n\r\n\r\n    // 지도 조작 도구들 (MapPin 아이콘)\r\n    setCenter: { label: \"지도 이동\", icon: <MapPin className=\"h-3 w-3\" />, variant: \"outline\" },\r\n    changeBasemap: { label: \"배경지도 변경\", icon: <Map className=\"h-3 w-3\" />, variant: \"outline\" },\r\n    highlightGeometry: { label: \"영역 강조\", icon: <MapPin className=\"h-3 w-3\" />, variant: \"outline\" },\r\n    moveMapByDirection: { label: \"방향 이동\", icon: <MapPin className=\"h-3 w-3\" />, variant: \"outline\" },\r\n    setMapZoom: { label: \"확대/축소\", icon: <MapPin className=\"h-3 w-3\" />, variant: \"outline\" },\r\n\r\n    // 레이어 관련 도구들 (Layers3 아이콘)\r\n    createLayerFilter: { label: \"필터 적용\", icon: <Filter className=\"h-3 w-3\" />, variant: \"secondary\" },\r\n    createVectorStyle: { label: \"스타일 생성\", icon: <Layers3 className=\"h-3 w-3\" />, variant: \"secondary\" },\r\n    updateLayerStyle: { label: \"스타일 변경\", icon: <Edit2 className=\"h-3 w-3\" />, variant: \"secondary\" },\r\n    removeLayer: { label: \"레이어 삭제\", icon: <Eraser className=\"h-3 w-3\" />, variant: \"secondary\" },\r\n    generateCategoricalStyle: { label: \"유형별 스타일\", icon: <Edit className=\"h-3 w-3\" />, variant: \"secondary\" },\r\n    getLayerAttributesCount: { label: \"데이터 개수 조회\", icon: <Search className=\"h-3 w-3\" />, variant: \"secondary\" },\r\n\r\n    // 분석 도구들 (Zap 아이콘)\r\n    performDensityAnalysis: { label: \"밀도 분석\", icon: <Zap className=\"h-3 w-3\" />, variant: \"outline\" },\r\n\r\n    // HIL 도구들 (MessageSquare 아이콘)\r\n    chooseOption: { label: \"옵션 선택 제안\", icon: <MessageSquare className=\"h-3 w-3\" />, variant: \"outline\" },\r\n    getUserInput: { label: \"입력 요청 제안\", icon: <MessageSquare className=\"h-3 w-3\" />, variant: \"outline\" },\r\n    confirmWithCheckbox: { label: \"확인\", icon: <CheckCircle2 className=\"h-3 w-3\" />, variant: \"outline\" },\r\n    getLocation: { label: \"위치 정보 요청\", icon: <MapPin className=\"h-3 w-3\" />, variant: \"outline\" },\r\n  };\r\n\r\n  return toolMap[toolName] || {\r\n    label: toolName,\r\n    icon: <Settings className=\"h-3 w-3\" />,\r\n    variant: \"secondary\" as const\r\n  };\r\n};\r\n\r\nexport const Annotations = ({ annotations, isLoading }: AnnotationsProps) => {\r\n  if (!annotations || annotations.length === 0) return null;\r\n\r\n  // 상태 변수들\r\n  const reasoningData: string[] = [];\r\n  let isIntentAnalyzed = false;\r\n  let intentMessage = \"\";\r\n  let isAgentStarted = false;\r\n  let isAgentCompleted = false;\r\n  const toolCalls: string[] = [];\r\n\r\n  // 평가 관련 상태 변수들\r\n  let evaluationResult: EvaluationCompletedAnnotation | null = null;\r\n  let retryInfo: RetryStartingAnnotation | null = null;\r\n  let finalResult: RetryCompletedAnnotation | RetryLimitReachedAnnotation | null = null;\r\n\r\n  // 4단계 진행 상태 결정을 위한 함수\r\n  const determineProgressStage = (): ProgressStage => {\r\n    if (isAgentCompleted) return \"completed\";\r\n    if (isAgentStarted || toolCalls.length > 0) return \"executing\";\r\n    if (isIntentAnalyzed) return \"planning\";\r\n    return \"analyzing\";\r\n  };\r\n\r\n  // agent_completed 이후의 새로운 대화 시작을 감지\r\n  let lastAgentCompletedIndex = -1;\r\n  let hasNewConversation = false;\r\n\r\n  // 먼저 agent_completed의 위치들을 찾고, 그 이후에 reasoning이 있는지 확인\r\n  annotations.forEach((ann: JSONValue, index) => {\r\n    if (typeof ann === \"object\" && ann !== null && !Array.isArray(ann)) {\r\n      if (\"type\" in ann && ann.type === \"agent_completed\") {\r\n        // agent_completed 이후에 reasoning이 있는지 확인\r\n        const hasReasoningAfter = annotations.slice(index + 1).some((nextAnn: JSONValue) => {\r\n          return typeof nextAnn === \"object\" && nextAnn !== null && !Array.isArray(nextAnn) &&\r\n            \"type\" in nextAnn && nextAnn.type === \"reasoning\";\r\n        });\r\n\r\n        if (hasReasoningAfter) {\r\n          lastAgentCompletedIndex = index;\r\n          hasNewConversation = true;\r\n        }\r\n      }\r\n    }\r\n  });\r\n\r\n  // 먼저 전체 어노테이션에서 toolCalls 수집 (새 대화 여부와 관계없이)\r\n  // 중복 제거하지 않고 호출된 만큼 모두 수집\r\n  annotations.forEach((ann: JSONValue) => {\r\n    if (typeof ann === \"object\" && ann !== null && !Array.isArray(ann)) {\r\n      if (\"type\" in ann && ann.type === \"tool_call\") {\r\n        const toolAnn = ann as ToolCallAnnotation;\r\n        toolCalls.push(toolAnn.toolName);\r\n      }\r\n    }\r\n  });\r\n\r\n  // 어노테이션을 분석하여 상태 파악\r\n  annotations.forEach((ann: JSONValue, index) => {\r\n    if (typeof ann === \"object\" && ann !== null && !Array.isArray(ann)) {\r\n      // messageIdFromServer는 건너뛰기\r\n      if (\"messageIdFromServer\" in ann) {\r\n        return;\r\n      }\r\n\r\n      // 새로운 대화가 시작된 경우, 마지막 agent_completed 이전의 상태는 무시 (reasoning 제외)\r\n      if (hasNewConversation && lastAgentCompletedIndex >= 0 && index <= lastAgentCompletedIndex) {\r\n        // reasoning만 누적하고 나머지는 무시\r\n        if (\"type\" in ann && ann.type === \"reasoning\" && \"textDelta\" in ann) {\r\n          reasoningData.push(ann.textDelta as string);\r\n        }\r\n        return;\r\n      }\r\n\r\n      if (\"type\" in ann && ann.type === \"reasoning\" && \"textDelta\" in ann) {\r\n        reasoningData.push(ann.textDelta as string);\r\n      } else if (\"type\" in ann && ann.type === \"intent_analyzed\") {\r\n        isIntentAnalyzed = true;\r\n        const intentAnn = ann as IntentAnalyzedAnnotation;\r\n        intentMessage = intentAnn.message;\r\n      } else if (\"type\" in ann && ann.type === \"agent_start\") {\r\n        isAgentStarted = true;\r\n      } else if (\"type\" in ann && ann.type === \"agent_completed\") {\r\n        // 새로운 대화가 아닌 경우에만 완료 상태로 설정\r\n        if (!hasNewConversation || index > lastAgentCompletedIndex) {\r\n          isAgentCompleted = true;\r\n        }\r\n      } else if (\"type\" in ann && ann.type === \"evaluation_completed\") {\r\n        evaluationResult = ann as EvaluationCompletedAnnotation;\r\n      } else if (\"type\" in ann && ann.type === \"retry_starting\") {\r\n        retryInfo = ann as RetryStartingAnnotation;\r\n      } else if (\"type\" in ann && (ann.type === \"retry_completed\" || ann.type === \"retry_limit_reached\")) {\r\n        finalResult = ann as RetryCompletedAnnotation | RetryLimitReachedAnnotation;\r\n      }\r\n    }\r\n  });\r\n\r\n  // 어노테이션을 표시할지 결정 (사용하지 않으므로 제거)\r\n\r\n  return (\r\n    <div className=\"w-full max-w-4xl mx-auto\">\r\n\r\n      {/* 1. 의도분석 진행중 상태 */}\r\n      {!isIntentAnalyzed && isLoading && reasoningData.length > 0 && (\r\n        <div className=\"mb-4 px-1 relative\">\r\n          <div className=\"absolute left-0 top-0 bottom-0 w-1 bg-blue-500 rounded-full\"></div>\r\n          <div className=\"pl-4\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <AnimatedShinyText className=\"text-sm inline-flex items-center justify-center px-2 py-1 transition ease-out hover:text-neutral-600 hover:duration-300 hover:dark:text-neutral-400\">\r\n                답변을 준비 중입니다...\r\n              </AnimatedShinyText>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* reasoning 컴포넌트 (항상 표시) */}\r\n      {reasoningData.length > 0 && (\r\n        <div className=\"mb-4\">\r\n          <Reasoning\r\n            reasoning={reasoningData.join(\"\")}\r\n            className=\"my-2\"\r\n            initialOpen={false}\r\n            // initialOpen={!isIntentAnalyzed}\r\n            isReasoning={isLoading && !isIntentAnalyzed}\r\n          />\r\n        </div>\r\n      )}\r\n\r\n\r\n      {/* 2. 의도분석 완료 - 작업 계획 메시지 */}\r\n      {isIntentAnalyzed && intentMessage && (\r\n        <div className=\"mb-4 px-1 relative\">\r\n          <div className=\"absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-blue-500 to-purple-500 rounded-full\"></div>\r\n          <div className=\"pl-4\">\r\n            <p className=\"text-sm text-gray-700 leading-relaxed font-medium\">\r\n              {intentMessage}\r\n            </p>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* 3. 에이전트 실행 - 도구 호출 표시 */}\r\n      {isAgentStarted && (\r\n        <div className=\"mb-4 px-1 relative\">\r\n          <div className=\"absolute left-0 top-0 bottom-0 w-1 bg-gray-200 rounded-full\"></div>\r\n          <div className=\"pl-4\">\r\n            <div className=\"flex items-center gap-2 mb-2\">\r\n              {!isAgentCompleted &&\r\n                <span className=\"text-sm font-medium text-gray-800\">작업 중</span>\r\n              }\r\n              {!isAgentCompleted && (\r\n                <div className=\"w-2 h-2 bg-orange-500 rounded-full animate-pulse\"></div>\r\n              )}\r\n            </div>\r\n\r\n            {/* 도구 호출 뱃지들 - 동적으로 표시 */}\r\n            {toolCalls.length > 0 && (\r\n              <div className=\"flex flex-wrap gap-2\">\r\n                {toolCalls.map((toolName, index) => {\r\n                  const toolInfo = getToolDisplayInfo(toolName);\r\n\r\n                  return (\r\n                    <motion.div\r\n                      key={index}\r\n                      initial={{ opacity: 0, scale: 0.5 }}\r\n                      animate={{ opacity: 1, scale: 1 }}\r\n                      exit={{ opacity: 0, scale: 0.5 }}\r\n                      transition={{ duration: 0.2 }}\r\n                    >\r\n                      <Badge\r\n                        key={index}\r\n                        variant={toolInfo.variant}\r\n                        className=\"text-xs px-2 py-1 font-medium flex items-center gap-1\"\r\n                      >\r\n                        {toolInfo.icon}\r\n                        {toolInfo.label}\r\n                      </Badge>\r\n                    </motion.div>\r\n                  );\r\n                })}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* 4. 평가 완료 결과 - 아코디언으로 개선 */}\r\n      {evaluationResult && (\r\n        <div className=\"mb-4 px-1 relative\">\r\n          <div className={`absolute left-0 top-0 bottom-0 w-1 rounded-full ${(evaluationResult as EvaluationCompletedAnnotation).isCompleted ? 'bg-green-500' : 'bg-yellow-500'\r\n            }`}></div>\r\n          <div className=\"pl-4\">\r\n            <Collapsible>\r\n              <CollapsibleTrigger className=\"flex items-center gap-2 w-full text-left hover:bg-gray-50 rounded-md transition-colors group\">\r\n                {(evaluationResult as EvaluationCompletedAnnotation).isCompleted ? (\r\n                  <CheckCircle2 className=\"h-4 w-4 text-green-600 flex-shrink-0\" />\r\n                ) : (\r\n                  <Zap className=\"h-4 w-4 text-yellow-600 flex-shrink-0\" />\r\n                )}\r\n                <span className=\"text-sm font-medium flex-1\">\r\n                  {(evaluationResult as EvaluationCompletedAnnotation).message}\r\n\r\n                </span>\r\n                <ChevronDown className=\"h-4 w-4 text-gray-400 transition-transform group-data-[state=open]:rotate-180\" />\r\n              </CollapsibleTrigger>\r\n\r\n              <CollapsibleContent className=\"mt-2 space-y-3\">\r\n                {/* 평가 이유 표시 */}\r\n                <div className=\"text-xs text-gray-600\">\r\n                  <div className=\"font-medium mb-1 text-gray-800\">평가 내용</div>\r\n                  <p className=\"bg-white p-2 rounded border text-gray-700\">\r\n                    {(evaluationResult as EvaluationCompletedAnnotation).reason}\r\n                  </p>\r\n                </div>\r\n\r\n                {/* 개선 제안 표시 */}\r\n                {(() => {\r\n                  const suggestions = (evaluationResult as EvaluationCompletedAnnotation).improvementSuggestions;\r\n                  if (Array.isArray(suggestions) && suggestions.length > 0) {\r\n                    return (\r\n                      <div className=\"text-xs text-gray-600\">\r\n                        <div className=\"font-medium mb-1 text-gray-800\">개선 제안</div>\r\n                        <div className=\"bg-white p-2 rounded border\">\r\n                          <ul className=\"list-disc list-inside space-y-1 text-gray-700\">\r\n                            {suggestions.map((suggestion: any, index: number) => (\r\n                              <li key={index}>{String(suggestion)}</li>\r\n                            ))}\r\n                          </ul>\r\n                        </div>\r\n                      </div>\r\n                    );\r\n                  }\r\n                  return null;\r\n                })()}\r\n              </CollapsibleContent>\r\n            </Collapsible>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* 6. 재시도 시작 */}\r\n      {retryInfo && (\r\n        <div className=\"mb-4 px-1 relative\">\r\n          <div className=\"absolute left-0 top-0 bottom-0 w-1 bg-blue-500 rounded-full\"></div>\r\n          <div className=\"pl-4\">\r\n            <div className=\"flex items-center gap-2 mb-2\">\r\n              <div className=\"w-2 h-2 bg-blue-500 rounded-full animate-pulse\"></div>\r\n              <span className=\"text-sm font-medium text-blue-700\">\r\n                {(retryInfo as RetryStartingAnnotation).message}\r\n              </span>\r\n            </div>\r\n            <div className=\"text-xs text-gray-600\">\r\n              {(retryInfo as RetryStartingAnnotation).reason}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* 7. 최종 결과 */}\r\n      {finalResult && (\r\n        <div className=\"mb-4 px-1 relative\">\r\n          <div className={`absolute left-0 top-0 bottom-0 w-1 rounded-full ${(finalResult as any).type === \"retry_completed\" && (finalResult as RetryCompletedAnnotation).finalResult === \"success\"\r\n              ? 'bg-green-500' : 'bg-orange-500'\r\n            }`}></div>\r\n          <div className=\"pl-4\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <span className=\"text-sm font-medium\">\r\n                {(finalResult as any).message}\r\n              </span>\r\n            </div>\r\n            <div className=\"text-xs text-gray-600 mt-1\">\r\n              총 {(finalResult as any).totalIterations}회 시도 완료\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* 8. 완료 상태 */}\r\n      {/* {isAgentCompleted && !isLoading && (\r\n        <div className=\"mt-3 px-1 relative\">\r\n          <div className=\"absolute left-0 top-0 bottom-0 w-1 bg-green-500 rounded-full\"></div>\r\n          <div className=\"pl-4\">\r\n            <p className=\"text-sm text-green-700 font-medium\">\r\n              완료되었습니다\r\n            </p>\r\n          </div>\r\n        </div>\r\n      )} */}\r\n\r\n      {/* 스트리밍 완료 후 최소한의 상태 표시 */}\r\n      {!isLoading && !isIntentAnalyzed && !isAgentStarted && !isAgentCompleted && reasoningData.length > 0 && (\r\n        <div className=\"mt-3 px-1 relative\">\r\n          <div className=\"absolute left-0 top-0 bottom-0 w-1 bg-gray-300 rounded-full\"></div>\r\n          <div className=\"pl-4\">\r\n            <p className=\"text-sm text-gray-600\">\r\n              처리 완료\r\n            </p>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;AAKA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AACA;AA3BA;;;;;;;;AAuKO,MAAM,qBAAqB,CAAC;IACjC,MAAM,UAAkH;QACtH,yBAAyB;QACzB,UAAU;YAAE,OAAO;YAAU,oBAAM,sSAAC,6RAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YAAc,SAAS;QAAU;QACtF,cAAc;YAAE,OAAO;YAAU,oBAAM,sSAAC,6RAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YAAc,SAAS;QAAU;QAC1F,oBAAoB;YAAE,OAAO;YAAS,oBAAM,sSAAC,6RAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YAAc,SAAS;QAAU;QAC/F,cAAc;YAAE,OAAO;YAAU,oBAAM,sSAAC,6RAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YAAc,SAAS;QAAU;QAC1F,eAAe;YAAE,OAAO;YAAS,oBAAM,sSAAC,6RAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YAAc,SAAS;QAAU;QAC1F,aAAa;YAAE,OAAO;YAAS,oBAAM,sSAAC,iSAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YAA8B,SAAS;QAAU;QACxG,cAAc;YAAE,OAAO;YAAU,oBAAM,sSAAC,iSAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YAA6B,SAAS;QAAU;QACzG,mBAAmB;YAAE,OAAO;YAAU,oBAAM,sSAAC,iSAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YAA2B,SAAS;QAAU;QAC5G,kBAAkB;YAAE,OAAO;YAAS,oBAAM,sSAAC,2RAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YAAc,SAAS;QAAU;QAI5F,yBAAyB;QACzB,WAAW;YAAE,OAAO;YAAS,oBAAM,sSAAC,iSAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YAAc,SAAS;QAAU;QACtF,eAAe;YAAE,OAAO;YAAW,oBAAM,sSAAC,uRAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;YAAc,SAAS;QAAU;QACzF,mBAAmB;YAAE,OAAO;YAAS,oBAAM,sSAAC,iSAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YAAc,SAAS;QAAU;QAC9F,oBAAoB;YAAE,OAAO;YAAS,oBAAM,sSAAC,iSAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YAAc,SAAS;QAAU;QAC/F,YAAY;YAAE,OAAO;YAAS,oBAAM,sSAAC,iSAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YAAc,SAAS;QAAU;QAEvF,2BAA2B;QAC3B,mBAAmB;YAAE,OAAO;YAAS,oBAAM,sSAAC,6RAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YAAc,SAAS;QAAY;QAChG,mBAAmB;YAAE,OAAO;YAAU,oBAAM,sSAAC,mSAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YAAc,SAAS;QAAY;QAClG,kBAAkB;YAAE,OAAO;YAAU,oBAAM,sSAAC,yRAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YAAc,SAAS;QAAY;QAC/F,aAAa;YAAE,OAAO;YAAU,oBAAM,sSAAC,6RAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YAAc,SAAS;QAAY;QAC3F,0BAA0B;YAAE,OAAO;YAAW,oBAAM,sSAAC,kSAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YAAc,SAAS;QAAY;QACvG,yBAAyB;YAAE,OAAO;YAAa,oBAAM,sSAAC,6RAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YAAc,SAAS;QAAY;QAE1G,mBAAmB;QACnB,wBAAwB;YAAE,OAAO;YAAS,oBAAM,sSAAC,uRAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;YAAc,SAAS;QAAU;QAEhG,8BAA8B;QAC9B,cAAc;YAAE,OAAO;YAAY,oBAAM,sSAAC,+SAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;YAAc,SAAS;QAAU;QACnG,cAAc;YAAE,OAAO;YAAY,oBAAM,sSAAC,+SAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;YAAc,SAAS;QAAU;QACnG,qBAAqB;YAAE,OAAO;YAAM,oBAAM,sSAAC,4SAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;YAAc,SAAS;QAAU;QACnG,aAAa;YAAE,OAAO;YAAY,oBAAM,sSAAC,iSAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YAAc,SAAS;QAAU;IAC7F;IAEA,OAAO,OAAO,CAAC,SAAS,IAAI;QAC1B,OAAO;QACP,oBAAM,sSAAC,iSAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAC1B,SAAS;IACX;AACF;AAEO,MAAM,cAAc,CAAC,EAAE,WAAW,EAAE,SAAS,EAAoB;IACtE,IAAI,CAAC,eAAe,YAAY,MAAM,KAAK,GAAG,OAAO;IAErD,SAAS;IACT,MAAM,gBAA0B,EAAE;IAClC,IAAI,mBAAmB;IACvB,IAAI,gBAAgB;IACpB,IAAI,iBAAiB;IACrB,IAAI,mBAAmB;IACvB,MAAM,YAAsB,EAAE;IAE9B,eAAe;IACf,IAAI,mBAAyD;IAC7D,IAAI,YAA4C;IAChD,IAAI,cAA6E;IAEjF,sBAAsB;IACtB,MAAM,yBAAyB;QAC7B,IAAI,kBAAkB,OAAO;QAC7B,IAAI,kBAAkB,UAAU,MAAM,GAAG,GAAG,OAAO;QACnD,IAAI,kBAAkB,OAAO;QAC7B,OAAO;IACT;IAEA,oCAAoC;IACpC,IAAI,0BAA0B,CAAC;IAC/B,IAAI,qBAAqB;IAEzB,uDAAuD;IACvD,YAAY,OAAO,CAAC,CAAC,KAAgB;QACnC,IAAI,OAAO,QAAQ,YAAY,QAAQ,QAAQ,CAAC,MAAM,OAAO,CAAC,MAAM;YAClE,IAAI,UAAU,OAAO,IAAI,IAAI,KAAK,mBAAmB;gBACnD,wCAAwC;gBACxC,MAAM,oBAAoB,YAAY,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC;oBAC3D,OAAO,OAAO,YAAY,YAAY,YAAY,QAAQ,CAAC,MAAM,OAAO,CAAC,YACvE,UAAU,WAAW,QAAQ,IAAI,KAAK;gBAC1C;gBAEA,IAAI,mBAAmB;oBACrB,0BAA0B;oBAC1B,qBAAqB;gBACvB;YACF;QACF;IACF;IAEA,6CAA6C;IAC7C,0BAA0B;IAC1B,YAAY,OAAO,CAAC,CAAC;QACnB,IAAI,OAAO,QAAQ,YAAY,QAAQ,QAAQ,CAAC,MAAM,OAAO,CAAC,MAAM;YAClE,IAAI,UAAU,OAAO,IAAI,IAAI,KAAK,aAAa;gBAC7C,MAAM,UAAU;gBAChB,UAAU,IAAI,CAAC,QAAQ,QAAQ;YACjC;QACF;IACF;IAEA,oBAAoB;IACpB,YAAY,OAAO,CAAC,CAAC,KAAgB;QACnC,IAAI,OAAO,QAAQ,YAAY,QAAQ,QAAQ,CAAC,MAAM,OAAO,CAAC,MAAM;YAClE,4BAA4B;YAC5B,IAAI,yBAAyB,KAAK;gBAChC;YACF;YAEA,gEAAgE;YAChE,IAAI,sBAAsB,2BAA2B,KAAK,SAAS,yBAAyB;gBAC1F,0BAA0B;gBAC1B,IAAI,UAAU,OAAO,IAAI,IAAI,KAAK,eAAe,eAAe,KAAK;oBACnE,cAAc,IAAI,CAAC,IAAI,SAAS;gBAClC;gBACA;YACF;YAEA,IAAI,UAAU,OAAO,IAAI,IAAI,KAAK,eAAe,eAAe,KAAK;gBACnE,cAAc,IAAI,CAAC,IAAI,SAAS;YAClC,OAAO,IAAI,UAAU,OAAO,IAAI,IAAI,KAAK,mBAAmB;gBAC1D,mBAAmB;gBACnB,MAAM,YAAY;gBAClB,gBAAgB,UAAU,OAAO;YACnC,OAAO,IAAI,UAAU,OAAO,IAAI,IAAI,KAAK,eAAe;gBACtD,iBAAiB;YACnB,OAAO,IAAI,UAAU,OAAO,IAAI,IAAI,KAAK,mBAAmB;gBAC1D,4BAA4B;gBAC5B,IAAI,CAAC,sBAAsB,QAAQ,yBAAyB;oBAC1D,mBAAmB;gBACrB;YACF,OAAO,IAAI,UAAU,OAAO,IAAI,IAAI,KAAK,wBAAwB;gBAC/D,mBAAmB;YACrB,OAAO,IAAI,UAAU,OAAO,IAAI,IAAI,KAAK,kBAAkB;gBACzD,YAAY;YACd,OAAO,IAAI,UAAU,OAAO,CAAC,IAAI,IAAI,KAAK,qBAAqB,IAAI,IAAI,KAAK,qBAAqB,GAAG;gBAClG,cAAc;YAChB;QACF;IACF;IAEA,gCAAgC;IAEhC,qBACE,sSAAC;QAAI,WAAU;;YAGZ,CAAC,oBAAoB,aAAa,cAAc,MAAM,GAAG,mBACxD,sSAAC;gBAAI,WAAU;;kCACb,sSAAC;wBAAI,WAAU;;;;;;kCACf,sSAAC;wBAAI,WAAU;kCACb,cAAA,sSAAC;4BAAI,WAAU;sCACb,cAAA,sSAAC,sJAAA,CAAA,oBAAiB;gCAAC,WAAU;0CAAsJ;;;;;;;;;;;;;;;;;;;;;;YAS1L,cAAc,MAAM,GAAG,mBACtB,sSAAC;gBAAI,WAAU;0BACb,cAAA,sSAAC,2HAAA,CAAA,YAAS;oBACR,WAAW,cAAc,IAAI,CAAC;oBAC9B,WAAU;oBACV,aAAa;oBACb,kCAAkC;oBAClC,aAAa,aAAa,CAAC;;;;;;;;;;;YAOhC,oBAAoB,+BACnB,sSAAC;gBAAI,WAAU;;kCACb,sSAAC;wBAAI,WAAU;;;;;;kCACf,sSAAC;wBAAI,WAAU;kCACb,cAAA,sSAAC;4BAAE,WAAU;sCACV;;;;;;;;;;;;;;;;;YAOR,gCACC,sSAAC;gBAAI,WAAU;;kCACb,sSAAC;wBAAI,WAAU;;;;;;kCACf,sSAAC;wBAAI,WAAU;;0CACb,sSAAC;gCAAI,WAAU;;oCACZ,CAAC,kCACA,sSAAC;wCAAK,WAAU;kDAAoC;;;;;;oCAErD,CAAC,kCACA,sSAAC;wCAAI,WAAU;;;;;;;;;;;;4BAKlB,UAAU,MAAM,GAAG,mBAClB,sSAAC;gCAAI,WAAU;0CACZ,UAAU,GAAG,CAAC,CAAC,UAAU;oCACxB,MAAM,WAAW,mBAAmB;oCAEpC,qBACE,sSAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAClC,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCAChC,MAAM;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAC/B,YAAY;4CAAE,UAAU;wCAAI;kDAE5B,cAAA,sSAAC,6HAAA,CAAA,QAAK;4CAEJ,SAAS,SAAS,OAAO;4CACzB,WAAU;;gDAET,SAAS,IAAI;gDACb,SAAS,KAAK;;2CALV;;;;;uCAPF;;;;;gCAgBX;;;;;;;;;;;;;;;;;;YAQT,kCACC,sSAAC;gBAAI,WAAU;;kCACb,sSAAC;wBAAI,WAAW,CAAC,gDAAgD,EAAE,AAAC,iBAAmD,WAAW,GAAG,iBAAiB,iBAClJ;;;;;;kCACJ,sSAAC;wBAAI,WAAU;kCACb,cAAA,sSAAC,mIAAA,CAAA,cAAW;;8CACV,sSAAC,mIAAA,CAAA,qBAAkB;oCAAC,WAAU;;wCAC1B,iBAAmD,WAAW,iBAC9D,sSAAC,4SAAA,CAAA,eAAY;4CAAC,WAAU;;;;;iEAExB,sSAAC,uRAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDAEjB,sSAAC;4CAAK,WAAU;sDACb,AAAC,iBAAmD,OAAO;;;;;;sDAG9D,sSAAC,2SAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;8CAGzB,sSAAC,mIAAA,CAAA,qBAAkB;oCAAC,WAAU;;sDAE5B,sSAAC;4CAAI,WAAU;;8DACb,sSAAC;oDAAI,WAAU;8DAAiC;;;;;;8DAChD,sSAAC;oDAAE,WAAU;8DACV,AAAC,iBAAmD,MAAM;;;;;;;;;;;;wCAK9D,CAAC;4CACA,MAAM,cAAc,AAAC,iBAAmD,sBAAsB;4CAC9F,IAAI,MAAM,OAAO,CAAC,gBAAgB,YAAY,MAAM,GAAG,GAAG;gDACxD,qBACE,sSAAC;oDAAI,WAAU;;sEACb,sSAAC;4DAAI,WAAU;sEAAiC;;;;;;sEAChD,sSAAC;4DAAI,WAAU;sEACb,cAAA,sSAAC;gEAAG,WAAU;0EACX,YAAY,GAAG,CAAC,CAAC,YAAiB,sBACjC,sSAAC;kFAAgB,OAAO;uEAAf;;;;;;;;;;;;;;;;;;;;;4CAMrB;4CACA,OAAO;wCACT,CAAC;;;;;;;;;;;;;;;;;;;;;;;;YAQV,2BACC,sSAAC;gBAAI,WAAU;;kCACb,sSAAC;wBAAI,WAAU;;;;;;kCACf,sSAAC;wBAAI,WAAU;;0CACb,sSAAC;gCAAI,WAAU;;kDACb,sSAAC;wCAAI,WAAU;;;;;;kDACf,sSAAC;wCAAK,WAAU;kDACb,AAAC,UAAsC,OAAO;;;;;;;;;;;;0CAGnD,sSAAC;gCAAI,WAAU;0CACZ,AAAC,UAAsC,MAAM;;;;;;;;;;;;;;;;;;YAOrD,6BACC,sSAAC;gBAAI,WAAU;;kCACb,sSAAC;wBAAI,WAAW,CAAC,gDAAgD,EAAE,AAAC,YAAoB,IAAI,KAAK,qBAAqB,AAAC,YAAyC,WAAW,KAAK,YAC1K,iBAAiB,iBACnB;;;;;;kCACJ,sSAAC;wBAAI,WAAU;;0CACb,sSAAC;gCAAI,WAAU;0CACb,cAAA,sSAAC;oCAAK,WAAU;8CACb,AAAC,YAAoB,OAAO;;;;;;;;;;;0CAGjC,sSAAC;gCAAI,WAAU;;oCAA6B;oCACtC,YAAoB,eAAe;oCAAC;;;;;;;;;;;;;;;;;;;YAmB/C,CAAC,aAAa,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,oBAAoB,cAAc,MAAM,GAAG,mBACjG,sSAAC;gBAAI,WAAU;;kCACb,sSAAC;wBAAI,WAAU;;;;;;kCACf,sSAAC;wBAAI,WAAU;kCACb,cAAA,sSAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;;AASjD;KAhTa", "debugId": null}}, {"offset": {"line": 2277, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/message.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Message as AIMessage } from \"ai\";\r\nimport React, { useEffect } from \"react\";\r\nimport { AnimatePresence, motion } from \"framer-motion\";\r\nimport { memo, useState } from \"react\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { PencilIcon } from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\";\r\nimport { Markdown } from \"./markdown\";\r\nimport { PreviewAttachment } from \"./preview-attachment\";\r\nimport equal from \"fast-deep-equal\";\r\nimport { MessageEditor } from \"./message-editor\";\r\nimport { MessageActions } from \"./message-actions\";\r\nimport { Vote } from \"@/lib/db/schema\";\r\nimport { Reasoning } from \"./reasoning\";\r\nimport { ToolInvocationPart } from \"./tools/tool-invocation-part\";\r\nimport { AnimatedShinyText } from \"./magicui/animated-shiny-text\";\r\nimport { Annotations } from \"./annotations\";\r\nimport { UseMapReturn } from \"@geon-map/odf\";\r\nimport type { JSONValue } from \"ai\";\r\n\r\n// 평가 진행 상태 감지 함수\r\nfunction isEvaluationInProgress(annotations: JSONValue[] | undefined): boolean {\r\n  if (!annotations) return false;\r\n\r\n  let hasEvaluationStart = false;\r\n  let hasEvaluationCompleted = false;\r\n\r\n  annotations.forEach((ann: JSONValue) => {\r\n    if (typeof ann === \"object\" && ann !== null && !Array.isArray(ann)) {\r\n      if (\"type\" in ann && ann.type === \"evaluation_start\") {\r\n        hasEvaluationStart = true;\r\n      }\r\n      if (\"type\" in ann && ann.type === \"evaluation_completed\") {\r\n        hasEvaluationCompleted = true;\r\n      }\r\n    }\r\n  });\r\n\r\n  // evaluation_start가 있고 evaluation_completed가 없을 때만 진행 중으로 판단\r\n  return hasEvaluationStart && !hasEvaluationCompleted;\r\n}\r\n\r\ninterface MessageProps {\r\n  chatId: string;\r\n  message: AIMessage;\r\n  status: \"submitted\" | \"streaming\" | \"ready\" | \"error\";\r\n  vote: Vote | undefined;\r\n  setMessages: (\r\n    messages: AIMessage[] | ((messages: AIMessage[]) => AIMessage[])\r\n  ) => void;\r\n  reload: () => Promise<string | null | undefined>;\r\n  isReadonly: boolean;\r\n  addToolResult: (params: { toolCallId: string; result: string }) => void;\r\n  mapState?: UseMapReturn;\r\n}\r\n\r\n\r\n\r\n\r\n\r\nfunction PureMessage({\r\n  chatId,\r\n  message,\r\n  vote,\r\n  status,\r\n  setMessages,\r\n  reload,\r\n  isReadonly,\r\n  addToolResult,\r\n  mapState,\r\n}: MessageProps) {\r\n  const [mode, setMode] = useState<\"view\" | \"edit\">(\"view\");\r\n  // 각 reasoning 파트의 표시 상태를 관리하는 상태\r\n  const [activeReasoningId, setActiveReasoningId] = useState<string | null>(\r\n    null\r\n  );\r\n\r\n  // 메시지의 파트 타입을 추적하여 reasoning 컴포넌트 상태 관리\r\n  useEffect(() => {\r\n    if (!message.parts || message.parts.length === 0) return;\r\n\r\n    // 가장 최근 파트 타입 확인\r\n    const lastPart = message.parts[message.parts.length - 1];\r\n\r\n    // 파트의 타입이 reasoning이면 해당 메시지의 ID를 설정\r\n    // 다른 타입이면 null로 설정하여 모든 reasoning 컴포넌트를 닫음\r\n    if (lastPart.type === \"reasoning\") {\r\n      // 고유한 식별자 생성: 메시지 ID + 마지막 파트 인덱스\r\n      const reasoningId = `${message.id}-${message.parts.length - 1}`;\r\n      setActiveReasoningId(reasoningId);\r\n    } else {\r\n      setActiveReasoningId(null);\r\n    }\r\n  }, [message.parts]);\r\n\r\n  return (\r\n    <AnimatePresence>\r\n      <motion.div\r\n        className=\"w-full mx-auto max-w-3xl px-4 group/message\"\r\n        // initial={{ y: 5, opacity: 0 }}\r\n        // animate={{ y: 0, opacity: 1 }}\r\n        data-role={message.role}\r\n      >\r\n        <div\r\n          className={cn(\r\n            \"flex flex-col gap-4 w-full\",\r\n            message.role === \"user\" ? \"items-end\" : \"items-start\"\r\n          )}\r\n        >\r\n          <div className=\"flex flex-col gap-4 w-full\">\r\n            {message.role === \"assistant\" && (\r\n              <Annotations\r\n                annotations={message.annotations}\r\n                isLoading={status === \"streaming\" || status === \"submitted\"}\r\n              />\r\n            )}\r\n            {message.experimental_attachments && (\r\n              <div className=\"flex flex-wrap gap-2\">\r\n                {message.experimental_attachments.map((attachment) => (\r\n                  <PreviewAttachment\r\n                    key={attachment.url}\r\n                    attachment={attachment}\r\n                  />\r\n                ))}\r\n              </div>\r\n            )}\r\n\r\n            {mode === \"view\" && (\r\n              <div className=\"flex flex-row gap-2 items-start\">\r\n                {message.role === \"user\" && !isReadonly && (\r\n                  <Tooltip>\r\n                    <TooltipTrigger asChild>\r\n                      <Button\r\n                        variant=\"ghost\"\r\n                        className=\"px-2 h-fit rounded-full text-muted-foreground opacity-0 group-hover/message:opacity-100\"\r\n                        onClick={() => setMode(\"edit\")}\r\n                      >\r\n                        <PencilIcon className=\"h-4 w-4\" />\r\n                      </Button>\r\n                    </TooltipTrigger>\r\n                    <TooltipContent>메시지 수정</TooltipContent>\r\n                  </Tooltip>\r\n                )}\r\n\r\n                <div\r\n                  className={cn(\"flex flex-col gap-4 overflow-hidden\", {\r\n                    \"bg-primary text-primary-foreground px-1 py-2 rounded-xl ml-auto max-w-[80%] md:max-w-[70%] lg:max-w-[60%] whitespace-pre-wrap break-words\":\r\n                      message.role === \"user\",\r\n                    \"w-full\": message.role !== \"user\",\r\n                  })}\r\n                >\r\n                  {message.parts?.map((part, j) => {\r\n                    switch (part.type) {\r\n                      case \"text\":\r\n                        return <Markdown key={j}>{part.text}</Markdown>;\r\n\r\n                      case \"reasoning\":\r\n                        // 각 reasoning 파트를 고유하게 식별하는 ID 생성\r\n                        const reasoningId = `${message.id}-${j}`;\r\n                        // 현재 활성화된 reasoning ID와 매칭되는지 확인\r\n                        const isReasoningActive =\r\n                          activeReasoningId === reasoningId;\r\n\r\n                        return (\r\n                          <Reasoning\r\n                            key={j}\r\n                            reasoning={part.reasoning}\r\n                            className=\"my-2\"\r\n                            open={isReasoningActive}\r\n                            isReasoning={isReasoningActive}\r\n                          />\r\n                        );\r\n\r\n                      case \"tool-invocation\": {\r\n                        return (\r\n                          <ToolInvocationPart\r\n                            key={j}\r\n                            invocation={part.toolInvocation}\r\n                            status={status}\r\n                            addToolResult={addToolResult}\r\n                            mapState={mapState}\r\n                          />\r\n                        );\r\n                      }\r\n                      default:\r\n                        return null;\r\n                    }\r\n                  })}\r\n\r\n                  {/* 평가 진행 상태 - 메시지 텍스트 아래에 표시 */}\r\n                  {message.role === \"assistant\" && isEvaluationInProgress(message.annotations) && (\r\n                    <div className=\"mt-3 flex items-center gap-2 text-xs text-gray-500\">\r\n                      <div className=\"w-1.5 h-1.5 bg-gray-400 rounded-full animate-pulse\"></div>\r\n                      <span>작업 결과를 평가 중입니다...</span>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {mode === \"edit\" && (\r\n              <div className=\"flex flex-row gap-2 items-start\">\r\n                <MessageEditor\r\n                  key={message.id}\r\n                  message={message}\r\n                  setMode={setMode}\r\n                  setMessages={setMessages}\r\n                  reload={reload}\r\n                />\r\n              </div>\r\n            )}\r\n\r\n            {!isReadonly && (\r\n              <MessageActions\r\n                key={`action-${message.id}`}\r\n                chatId={chatId}\r\n                message={message}\r\n                vote={vote}\r\n                isLoading={status === \"submitted\" || status === \"streaming\"}\r\n              />\r\n            )}\r\n          </div>\r\n        </div>\r\n      </motion.div>\r\n    </AnimatePresence>\r\n  );\r\n}\r\n\r\nexport const Message = memo(PureMessage, (prevProps, nextProps) => {\r\n  if (prevProps.status !== nextProps.status) return false;\r\n  if (prevProps.message.id !== nextProps.message.id) return false;\r\n  if (prevProps.message.content !== nextProps.message.content) return false;\r\n  if (!equal(prevProps.message.parts, nextProps.message.parts)) return false;\r\n  if (!equal(prevProps.message.annotations, nextProps.message.annotations))\r\n    return false;\r\n  return true;\r\n});\r\n\r\nexport const ThinkingMessage = () => {\r\n  const thinkingPhrases = [\r\n    \"답변을 준비하고 있습니다...\",\r\n    \"요청하신 내용을 처리 중입니다...\",\r\n    \"최적의 답변을 찾고 있습니다...\",\r\n    \"생각을 정리하고 있습니다...\",\r\n  ];\r\n\r\n  const [currentPhrase, setCurrentPhrase] = React.useState(\"\");\r\n\r\n  React.useEffect(() => {\r\n    setCurrentPhrase(\r\n      thinkingPhrases[Math.floor(Math.random() * thinkingPhrases.length)]\r\n    );\r\n  }, []);\r\n  return (\r\n    <motion.div\r\n      className=\"w-full max-w-3xl px-4 group/message\"\r\n      initial={{ y: 5, opacity: 0 }}\r\n      animate={{ y: 0, opacity: 1 }}\r\n      data-role=\"assistant\"\r\n    >\r\n      <div className=\"flex items-center justify-start gap-2.5\">\r\n        <AnimatedShinyText className=\"inline-flex items-center justify-center px-2 py-1 transition ease-out\">\r\n          <span>{currentPhrase || \"처리 중입니다...\"}</span>\r\n        </AnimatedShinyText>\r\n      </div>\r\n    </motion.div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAAA;AAEA;AACA;AACA;AACA;AAKA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;;AAvBA;;;;;;;;;;;;;;;;;AA2BA,iBAAiB;AACjB,SAAS,uBAAuB,WAAoC;IAClE,IAAI,CAAC,aAAa,OAAO;IAEzB,IAAI,qBAAqB;IACzB,IAAI,yBAAyB;IAE7B,YAAY,OAAO,CAAC,CAAC;QACnB,IAAI,OAAO,QAAQ,YAAY,QAAQ,QAAQ,CAAC,MAAM,OAAO,CAAC,MAAM;YAClE,IAAI,UAAU,OAAO,IAAI,IAAI,KAAK,oBAAoB;gBACpD,qBAAqB;YACvB;YACA,IAAI,UAAU,OAAO,IAAI,IAAI,KAAK,wBAAwB;gBACxD,yBAAyB;YAC3B;QACF;IACF;IAEA,6DAA6D;IAC7D,OAAO,sBAAsB,CAAC;AAChC;AAoBA,SAAS,YAAY,EACnB,MAAM,EACN,OAAO,EACP,IAAI,EACJ,MAAM,EACN,WAAW,EACX,MAAM,EACN,UAAU,EACV,aAAa,EACb,QAAQ,EACK;;IACb,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAmB;IAClD,iCAAiC;IACjC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EACvD;IAGF,wCAAwC;IACxC,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,CAAC,QAAQ,KAAK,IAAI,QAAQ,KAAK,CAAC,MAAM,KAAK,GAAG;YAElD,iBAAiB;YACjB,MAAM,WAAW,QAAQ,KAAK,CAAC,QAAQ,KAAK,CAAC,MAAM,GAAG,EAAE;YAExD,qCAAqC;YACrC,2CAA2C;YAC3C,IAAI,SAAS,IAAI,KAAK,aAAa;gBACjC,kCAAkC;gBAClC,MAAM,cAAc,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,QAAQ,KAAK,CAAC,MAAM,GAAG,GAAG;gBAC/D,qBAAqB;YACvB,OAAO;gBACL,qBAAqB;YACvB;QACF;gCAAG;QAAC,QAAQ,KAAK;KAAC;IAElB,qBACE,sSAAC,kSAAA,CAAA,kBAAe;kBACd,cAAA,sSAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,iCAAiC;YACjC,iCAAiC;YACjC,aAAW,QAAQ,IAAI;sBAEvB,cAAA,sSAAC;gBACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8BACA,QAAQ,IAAI,KAAK,SAAS,cAAc;0BAG1C,cAAA,sSAAC;oBAAI,WAAU;;wBACZ,QAAQ,IAAI,KAAK,6BAChB,sSAAC,6HAAA,CAAA,cAAW;4BACV,aAAa,QAAQ,WAAW;4BAChC,WAAW,WAAW,eAAe,WAAW;;;;;;wBAGnD,QAAQ,wBAAwB,kBAC/B,sSAAC;4BAAI,WAAU;sCACZ,QAAQ,wBAAwB,CAAC,GAAG,CAAC,CAAC,2BACrC,sSAAC,uIAAA,CAAA,oBAAiB;oCAEhB,YAAY;mCADP,WAAW,GAAG;;;;;;;;;;wBAO1B,SAAS,wBACR,sSAAC;4BAAI,WAAU;;gCACZ,QAAQ,IAAI,KAAK,UAAU,CAAC,4BAC3B,sSAAC,+HAAA,CAAA,UAAO;;sDACN,sSAAC,+HAAA,CAAA,iBAAc;4CAAC,OAAO;sDACrB,cAAA,sSAAC,8HAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,WAAU;gDACV,SAAS,IAAM,QAAQ;0DAEvB,cAAA,sSAAC,iSAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;sDAG1B,sSAAC,+HAAA,CAAA,iBAAc;sDAAC;;;;;;;;;;;;8CAIpB,sSAAC;oCACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,uCAAuC;wCACnD,6IACE,QAAQ,IAAI,KAAK;wCACnB,UAAU,QAAQ,IAAI,KAAK;oCAC7B;;wCAEC,QAAQ,KAAK,EAAE,IAAI,CAAC,MAAM;4CACzB,OAAQ,KAAK,IAAI;gDACf,KAAK;oDACH,qBAAO,sSAAC,0HAAA,CAAA,WAAQ;kEAAU,KAAK,IAAI;uDAAb;;;;;gDAExB,KAAK;oDACH,kCAAkC;oDAClC,MAAM,cAAc,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,GAAG;oDACxC,iCAAiC;oDACjC,MAAM,oBACJ,sBAAsB;oDAExB,qBACE,sSAAC,2HAAA,CAAA,YAAS;wDAER,WAAW,KAAK,SAAS;wDACzB,WAAU;wDACV,MAAM;wDACN,aAAa;uDAJR;;;;;gDAQX,KAAK;oDAAmB;wDACtB,qBACE,sSAAC,qJAAA,CAAA,qBAAkB;4DAEjB,YAAY,KAAK,cAAc;4DAC/B,QAAQ;4DACR,eAAe;4DACf,UAAU;2DAJL;;;;;oDAOX;gDACA;oDACE,OAAO;4CACX;wCACF;wCAGC,QAAQ,IAAI,KAAK,eAAe,uBAAuB,QAAQ,WAAW,mBACzE,sSAAC;4CAAI,WAAU;;8DACb,sSAAC;oDAAI,WAAU;;;;;;8DACf,sSAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;wBAOf,SAAS,wBACR,sSAAC;4BAAI,WAAU;sCACb,cAAA,sSAAC,mIAAA,CAAA,gBAAa;gCAEZ,SAAS;gCACT,SAAS;gCACT,aAAa;gCACb,QAAQ;+BAJH,QAAQ,EAAE;;;;;;;;;;wBASpB,CAAC,4BACA,sSAAC,oIAAA,CAAA,iBAAc;4BAEb,QAAQ;4BACR,SAAS;4BACT,MAAM;4BACN,WAAW,WAAW,eAAe,WAAW;2BAJ3C,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;AAY3C;GAtKS;KAAA;AAwKF,MAAM,wBAAU,CAAA,GAAA,sQAAA,CAAA,OAAI,AAAD,EAAE,aAAa,CAAC,WAAW;IACnD,IAAI,UAAU,MAAM,KAAK,UAAU,MAAM,EAAE,OAAO;IAClD,IAAI,UAAU,OAAO,CAAC,EAAE,KAAK,UAAU,OAAO,CAAC,EAAE,EAAE,OAAO;IAC1D,IAAI,UAAU,OAAO,CAAC,OAAO,KAAK,UAAU,OAAO,CAAC,OAAO,EAAE,OAAO;IACpE,IAAI,CAAC,CAAA,GAAA,mNAAA,CAAA,UAAK,AAAD,EAAE,UAAU,OAAO,CAAC,KAAK,EAAE,UAAU,OAAO,CAAC,KAAK,GAAG,OAAO;IACrE,IAAI,CAAC,CAAA,GAAA,mNAAA,CAAA,UAAK,AAAD,EAAE,UAAU,OAAO,CAAC,WAAW,EAAE,UAAU,OAAO,CAAC,WAAW,GACrE,OAAO;IACT,OAAO;AACT;;AAEO,MAAM,kBAAkB;;IAC7B,MAAM,kBAAkB;QACtB;QACA;QACA;QACA;KACD;IAED,MAAM,CAAC,eAAe,iBAAiB,GAAG,sQAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAEzD,sQAAA,CAAA,UAAK,CAAC,SAAS;qCAAC;YACd,iBACE,eAAe,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,gBAAgB,MAAM,EAAE;QAEvE;oCAAG,EAAE;IACL,qBACE,sSAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,GAAG;YAAG,SAAS;QAAE;QAC5B,SAAS;YAAE,GAAG;YAAG,SAAS;QAAE;QAC5B,aAAU;kBAEV,cAAA,sSAAC;YAAI,WAAU;sBACb,cAAA,sSAAC,sJAAA,CAAA,oBAAiB;gBAAC,WAAU;0BAC3B,cAAA,sSAAC;8BAAM,iBAAiB;;;;;;;;;;;;;;;;;;;;;AAKlC;IA7Ba;MAAA", "debugId": null}}, {"offset": {"line": 2643, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/messages.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { ChatRequestOptions, Message } from 'ai';\r\nimport { useScrollToBottom } from '@/lib/hooks/use-scroll-to-bottom';\r\nimport { Dispatch, memo, SetStateAction } from 'react';\r\nimport equal from 'fast-deep-equal';\r\nimport { Message as PreviewMessage, ThinkingMessage } from './message';\r\nimport { Overview } from '@/components/chat-map/overview';\r\nimport { Vote } from '@/lib/db/schema';\r\nimport { UseMapReturn } from \"@geon-map/odf\";\r\n\r\n\r\ninterface MessagesProps {\r\n  chatId: string;\r\n  // block: UIBlock;\r\n  // setBlock: Dispatch<SetStateAction<UIBlock>>;\r\n  votes: Array<Vote> | undefined;\r\n  messages: Array<Message>;\r\n  setMessages: (messages: Message[] | ((messages: Message[]) => Message[])) => void;\r\n  reload: (chatRequestOptions?: ChatRequestOptions) => Promise<string | null | undefined>;\r\n  isReadonly: boolean;\r\n  setInput: (input: string) => void;  // Add this line\r\n  status: 'submitted' | 'streaming' | 'ready' | 'error';\r\n  error: Error | undefined | null; // error prop 추가\r\n  addToolResult: ({\r\n    toolCallId,\r\n    result,\r\n  }: {\r\n    toolCallId: string;\r\n    result: string;\r\n  }) => void;\r\n  mapState?: UseMapReturn; // mapState 추가\r\n}\r\n\r\nfunction PureMessages({\r\n  chatId,\r\n  // block,\r\n  // setBlock,\r\n  votes,\r\n  messages,\r\n  setMessages,\r\n  reload,\r\n  isReadonly,\r\n  setInput,  // Add this line\r\n  status,\r\n  error, // error prop 추가\r\n  addToolResult,\r\n  mapState, // mapState 추가\r\n}: MessagesProps) {\r\n  const [messagesContainerRef, messagesEndRef] = useScrollToBottom<HTMLDivElement>();\r\n  return (\r\n    <div\r\n      ref={messagesContainerRef}\r\n      className=\"flex flex-col min-w-0 gap-12 flex-1 overflow-y-auto pt-4 styled-scrollbar\"\r\n    >\r\n      {messages.length === 0 && <Overview setInput={setInput} />}\r\n\r\n      {messages.map((message) => (\r\n        <PreviewMessage\r\n          key={message.id}\r\n          chatId={chatId}\r\n          message={message}\r\n          status={status} // status prop 전달\r\n          vote={\r\n            votes\r\n              ? votes.find((vote) => vote.messageId === message.id)\r\n              : undefined\r\n          }\r\n          setMessages={setMessages}\r\n          reload={reload}\r\n          isReadonly={isReadonly}\r\n          addToolResult={addToolResult}\r\n          mapState={mapState} // mapState 전달\r\n        />\r\n\r\n      ))}\r\n\r\n      {/* ThinkingMessage: 마지막 메시지가 사용자 입력이고, 현재 제출/스트리밍 중일 때 표시 */}\r\n      {(status === 'submitted') && \r\n        <ThinkingMessage />}\r\n\r\n      {/* Status and Error Messages */}\r\n        {status === 'error' && error && (\r\n        <div className=\"px-4 py-2 text-center text-sm\">\r\n          <p className=\"text-red-500\">\r\n            오류가 발생했습니다. \r\n            지속적으로 발생되는 경우 새 대화를 시도해주세요. \r\n            {error.message}\r\n          </p>\r\n        </div>\r\n      )}\r\n\r\n      <div\r\n        ref={messagesEndRef}\r\n        className=\"shrink-0 min-w-[24px] min-h-[24px]\"\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\n\r\nexport const Messages = memo(PureMessages, (prevProps, nextProps) => {\r\n  if (prevProps.status !== nextProps.status) return false;\r\n  if (prevProps.status && nextProps.status) return false;\r\n  if (prevProps.messages.length !== nextProps.messages.length) return false;\r\n  if (!equal(prevProps.messages, nextProps.messages)) return false;\r\n  if (!equal(prevProps.votes, nextProps.votes)) return false;\r\n\r\n  return true;\r\n});"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;AAkCA,SAAS,aAAa,EACpB,MAAM,EACN,SAAS;AACT,YAAY;AACZ,KAAK,EACL,QAAQ,EACR,WAAW,EACX,MAAM,EACN,UAAU,EACV,QAAQ,EACR,MAAM,EACN,KAAK,EACL,aAAa,EACb,QAAQ,EACM;;IACd,MAAM,CAAC,sBAAsB,eAAe,GAAG,CAAA,GAAA,gJAAA,CAAA,oBAAiB,AAAD;IAC/D,qBACE,sSAAC;QACC,KAAK;QACL,WAAU;;YAET,SAAS,MAAM,KAAK,mBAAK,sSAAC,yIAAA,CAAA,WAAQ;gBAAC,UAAU;;;;;;YAE7C,SAAS,GAAG,CAAC,CAAC,wBACb,sSAAC,yHAAA,CAAA,UAAc;oBAEb,QAAQ;oBACR,SAAS;oBACT,QAAQ;oBACR,MACE,QACI,MAAM,IAAI,CAAC,CAAC,OAAS,KAAK,SAAS,KAAK,QAAQ,EAAE,IAClD;oBAEN,aAAa;oBACb,QAAQ;oBACR,YAAY;oBACZ,eAAe;oBACf,UAAU;mBAbL,QAAQ,EAAE;;;;;YAmBjB,WAAW,6BACX,sSAAC,yHAAA,CAAA,kBAAe;;;;;YAGf,WAAW,WAAW,uBACvB,sSAAC;gBAAI,WAAU;0BACb,cAAA,sSAAC;oBAAE,WAAU;;wBAAe;wBAGzB,MAAM,OAAO;;;;;;;;;;;;0BAKpB,sSAAC;gBACC,KAAK;gBACL,WAAU;;;;;;;;;;;;AAIlB;GAhES;;QAewC,gJAAA,CAAA,oBAAiB;;;KAfzD;AAmEF,MAAM,yBAAW,CAAA,GAAA,sQAAA,CAAA,OAAI,AAAD,EAAE,cAAc,CAAC,WAAW;IACrD,IAAI,UAAU,MAAM,KAAK,UAAU,MAAM,EAAE,OAAO;IAClD,IAAI,UAAU,MAAM,IAAI,UAAU,MAAM,EAAE,OAAO;IACjD,IAAI,UAAU,QAAQ,CAAC,MAAM,KAAK,UAAU,QAAQ,CAAC,MAAM,EAAE,OAAO;IACpE,IAAI,CAAC,CAAA,GAAA,mNAAA,CAAA,UAAK,AAAD,EAAE,UAAU,QAAQ,EAAE,UAAU,QAAQ,GAAG,OAAO;IAC3D,IAAI,CAAC,CAAA,GAAA,mNAAA,CAAA,UAAK,AAAD,EAAE,UAAU,KAAK,EAAE,UAAU,KAAK,GAAG,OAAO;IAErD,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2756, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/footer.tsx"], "sourcesContent": ["import React from 'react'\r\n\r\nimport { cn } from '@/lib/utils'\r\n\r\nexport function FooterText({ className, ...props }: React.ComponentProps<'p'>) {\r\n\treturn (\r\n\t\t<p\r\n\t\t\tclassName={cn(\r\n\t\t\t\t'px-2 text-center text-xs leading-normal text-muted-foreground',\r\n\t\t\t\tclassName\r\n\t\t\t)}\r\n\t\t\t{...props}\r\n\t\t>\r\n\t\t\t작업을 도와드리기 위해 최선을 다하지만 실패할 수 있습니다.\r\n\t\t\t{/* 문서와 관련된 자세한 내용은 {' '} */}\r\n\t\t\t{/* <ExternalLink href=\"https://developer.geon.kr\">개발자 지원센터</ExternalLink> 를 확인해주세요 */}\r\n\t\t\t\r\n\t\t</p>\r\n\t)\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEO,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAkC;IAC5E,qBACC,sSAAC;QACA,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACX,iEACA;QAEA,GAAG,KAAK;kBACT;;;;;;AAOH;KAfgB", "debugId": null}}, {"offset": {"line": 2786, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/components/server-status.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { startTransition } from 'react';\nimport { useServerHealth } from '@/lib/hooks/use-server-health';\nimport { Button } from '@/components/ui/button';\nimport {\n  BetterTooltip,\n  Tooltip,\n  TooltipContent,\n  TooltipTrigger\n} from '@/components/ui/tooltip';\nimport {\n  AlertCircle,\n  CheckCircle,\n  Loader2,\n  RefreshCw,\n  Wifi,\n  WifiOff\n} from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { toast } from 'sonner';\nimport { saveModelId } from '@/app/(map)/actions';\n\ninterface ServerStatusProps {\n  selectedModelId: string;\n  onModelSwitchSuggested?: () => void;\n  className?: string;\n}\n\nexport function ServerStatus({\n  selectedModelId,\n  onModelSwitchSuggested,\n  className\n}: ServerStatusProps) {\n  // Qwen3 모델들일 때만 서버 상태 체크\n  const shouldCheck = selectedModelId === 'Qwen3-4B' || selectedModelId === 'Qwen3-14B';\n  const { isHealthy, isLoading, error, lastChecked, responseTime, refresh } =\n    useServerHealth(shouldCheck, selectedModelId);\n\n  // Qwen3 모델이 아닌 경우 표시하지 않음\n  if (!shouldCheck) {\n    return null;\n  }\n\n  const handleRefresh = () => {\n    refresh();\n  };\n\n  const handleModelSwitchSuggestion = () => {\n    if (onModelSwitchSuggested) {\n      onModelSwitchSuggested();\n    } else {\n      // 직접 모델을 gpt-4.1-mini로 변경\n      startTransition(() => {\n        saveModelId('gpt-4.1-mini');\n        toast.success('모델이 gpt-4.1-mini로 변경되었습니다.');\n        // 페이지 새로고침으로 변경사항 적용\n        setTimeout(() => {\n          window.location.reload();\n        }, 1000);\n      });\n    }\n  };\n\n  const getStatusIcon = () => {\n    if (isLoading) {\n      return <Loader2 className=\"h-3 w-3 animate-spin\" />;\n    }\n\n    if (isHealthy) {\n      return <CheckCircle className=\"h-3 w-3 text-green-500\" />;\n    }\n\n    return <AlertCircle className=\"h-3 w-3 text-red-500\" />;\n  };\n\n  const getStatusText = () => {\n    if (isLoading) return '확인 중...';\n    if (isHealthy) return '정상';\n    return '오프라인';\n  };\n\n  const getStatusColor = () => {\n    if (isLoading) return 'text-yellow-600';\n    if (isHealthy) return 'text-green-600';\n    return 'text-red-600';\n  };\n\n  const getTooltipContent = () => {\n    const baseInfo = `Qwen3 서버 상태: ${getStatusText()}`;\n\n    if (isLoading) {\n      return baseInfo;\n    }\n\n    if (isHealthy && responseTime) {\n      return `${baseInfo}\\n응답시간: ${responseTime}ms\\n마지막 확인: ${lastChecked?.toLocaleTimeString()}`;\n    }\n\n    if (error) {\n      return `${baseInfo}\\n오류: ${error}\\n마지막 확인: ${lastChecked?.toLocaleTimeString()}\\n\\ngpt-4.1-mini 모델 사용을 권장합니다.`;\n    }\n\n    return baseInfo;\n  };\n\n  return (\n    <div className={cn(\"flex items-center ml-2 gap-1\", className)}>\n      <BetterTooltip content={getTooltipContent()}>\n        <Button\n          variant=\"ghost\"\n          size=\"icon\"\n          className=\"h-6 w-6\"\n          onClick={handleRefresh}\n          disabled={isLoading}\n        >\n          <div className=\"flex items-center gap-1 px-2 py-1 rounded-md bg-secondary/20 hover:bg-secondary/30 transition-colors\">\n            {getStatusIcon()}\n            <span className={cn(\"text-xs font-medium\", getStatusColor())}>\n              {getStatusText()}\n            </span>\n          </div>\n        </Button>\n      </BetterTooltip>\n\n      {/* 서버가 오프라인일 때 모델 변경 제안 버튼 */}\n      {!isHealthy && !isLoading && (\n        <BetterTooltip content=\"Qwen3 서버가 오프라인입니다. 클릭하면 gpt-4.1-mini 모델로 변경됩니다.\">\n          <Button\n            variant=\"destructive\"\n            size=\"sm\"\n            className=\"h-6 ml-6 px-2 text-xs animate-pulse\"\n            onClick={handleModelSwitchSuggestion}\n          >\n            GPT로 변경\n          </Button>\n        </BetterTooltip>\n      )}\n    </div>\n  );\n}\n\n// 간단한 상태 표시용 컴포넌트 (헤더용)\nexport function ServerStatusIndicator({\n  selectedModelId,\n  className\n}: {\n  selectedModelId: string;\n  className?: string;\n}) {\n  const shouldCheck = selectedModelId === 'Qwen3-4B' || selectedModelId === 'Qwen3-14B';\n  const { isHealthy, isLoading } = useServerHealth(shouldCheck, selectedModelId);\n\n  if (!shouldCheck) return null;\n\n  return (\n    <div className={cn(\"flex items-center\", className)}>\n      {isLoading ? (\n        <Wifi className=\"h-3 w-3 text-yellow-500 animate-pulse\" />\n      ) : isHealthy ? (\n        <Wifi className=\"h-3 w-3 text-green-500\" />\n      ) : (\n        <WifiOff className=\"h-3 w-3 text-red-500\" />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAMA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AACA;;;AArBA;;;;;;;;;AA6BO,SAAS,aAAa,EAC3B,eAAe,EACf,sBAAsB,EACtB,SAAS,EACS;;IAClB,yBAAyB;IACzB,MAAM,cAAc,oBAAoB,cAAc,oBAAoB;IAC1E,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,EAAE,YAAY,EAAE,OAAO,EAAE,GACvE,CAAA,GAAA,0IAAA,CAAA,kBAAe,AAAD,EAAE,aAAa;IAE/B,0BAA0B;IAC1B,IAAI,CAAC,aAAa;QAChB,OAAO;IACT;IAEA,MAAM,gBAAgB;QACpB;IACF;IAEA,MAAM,8BAA8B;QAClC,IAAI,wBAAwB;YAC1B;QACF,OAAO;YACL,0BAA0B;YAC1B,CAAA,GAAA,sQAAA,CAAA,kBAAe,AAAD,EAAE;gBACd,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE;gBACZ,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,qBAAqB;gBACrB,WAAW;oBACT,OAAO,QAAQ,CAAC,MAAM;gBACxB,GAAG;YACL;QACF;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,WAAW;YACb,qBAAO,sSAAC,wSAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;QAC5B;QAEA,IAAI,WAAW;YACb,qBAAO,sSAAC,kTAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;QAChC;QAEA,qBAAO,sSAAC,2SAAA,CAAA,cAAW;YAAC,WAAU;;;;;;IAChC;IAEA,MAAM,gBAAgB;QACpB,IAAI,WAAW,OAAO;QACtB,IAAI,WAAW,OAAO;QACtB,OAAO;IACT;IAEA,MAAM,iBAAiB;QACrB,IAAI,WAAW,OAAO;QACtB,IAAI,WAAW,OAAO;QACtB,OAAO;IACT;IAEA,MAAM,oBAAoB;QACxB,MAAM,WAAW,CAAC,aAAa,EAAE,iBAAiB;QAElD,IAAI,WAAW;YACb,OAAO;QACT;QAEA,IAAI,aAAa,cAAc;YAC7B,OAAO,GAAG,SAAS,QAAQ,EAAE,aAAa,YAAY,EAAE,aAAa,sBAAsB;QAC7F;QAEA,IAAI,OAAO;YACT,OAAO,GAAG,SAAS,MAAM,EAAE,MAAM,UAAU,EAAE,aAAa,qBAAqB,8BAA8B,CAAC;QAChH;QAEA,OAAO;IACT;IAEA,qBACE,sSAAC;QAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,gCAAgC;;0BACjD,sSAAC,+HAAA,CAAA,gBAAa;gBAAC,SAAS;0BACtB,cAAA,sSAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,SAAS;oBACT,UAAU;8BAEV,cAAA,sSAAC;wBAAI,WAAU;;4BACZ;0CACD,sSAAC;gCAAK,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;0CACxC;;;;;;;;;;;;;;;;;;;;;;YAOR,CAAC,aAAa,CAAC,2BACd,sSAAC,+HAAA,CAAA,gBAAa;gBAAC,SAAQ;0BACrB,cAAA,sSAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,SAAS;8BACV;;;;;;;;;;;;;;;;;AAOX;GA/GgB;;QAQZ,0IAAA,CAAA,kBAAe;;;KARH;AAkHT,SAAS,sBAAsB,EACpC,eAAe,EACf,SAAS,EAIV;;IACC,MAAM,cAAc,oBAAoB,cAAc,oBAAoB;IAC1E,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,kBAAe,AAAD,EAAE,aAAa;IAE9D,IAAI,CAAC,aAAa,OAAO;IAEzB,qBACE,sSAAC;QAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,qBAAqB;kBACrC,0BACC,sSAAC,yRAAA,CAAA,OAAI;YAAC,WAAU;;;;;mBACd,0BACF,sSAAC,yRAAA,CAAA,OAAI;YAAC,WAAU;;;;;iCAEhB,sSAAC,mSAAA,CAAA,UAAO;YAAC,WAAU;;;;;;;;;;;AAI3B;IAvBgB;;QAQmB,0IAAA,CAAA,kBAAe;;;MARlC", "debugId": null}}]}