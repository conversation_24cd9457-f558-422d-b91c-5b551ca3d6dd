# @ai-sdk/openai-compatible

## 0.2.16

### Patch Changes

- 919ce07: Modified the validation logic for tool_calls for the index parameter to be optional.

## 0.2.15

### Patch Changes

- 2a8a853: Allow passing config to chat models

## 0.2.14

### Patch Changes

- Updated dependencies [d87b9d1]
  - @ai-sdk/provider-utils@2.2.8

## 0.2.13

### Patch Changes

- 23571c9: feat(providers/xai): add reasoningEffort provider option

## 0.2.12

### Patch Changes

- 13492fe: fix(providers/xai): return actual usage when streaming instead of NaN

## 0.2.11

### Patch Changes

- b5c9cd4: fix (provider/openai-compatible): change tool_call type schema to nullish

## 0.2.10

### Patch Changes

- Updated dependencies [beef951]
  - @ai-sdk/provider@1.1.3
  - @ai-sdk/provider-utils@2.2.7

## 0.2.9

### Patch Changes

- 1bbc698: chore(openai-compatible): deprecate simulateStreaming

## 0.2.8

### Patch Changes

- Updated dependencies [013faa8]
  - @ai-sdk/provider@1.1.2
  - @ai-sdk/provider-utils@2.2.6

## 0.2.7

### Patch Changes

- Updated dependencies [c21fa6d]
  - @ai-sdk/provider-utils@2.2.5
  - @ai-sdk/provider@1.1.1

## 0.2.6

### Patch Changes

- Updated dependencies [2c19b9a]
  - @ai-sdk/provider-utils@2.2.4

## 0.2.5

### Patch Changes

- d186cca: feat (provider/openai-compatible): add additional token usage metrics

## 0.2.4

### Patch Changes

- Updated dependencies [28be004]
  - @ai-sdk/provider-utils@2.2.3

## 0.2.3

### Patch Changes

- Updated dependencies [b01120e]
  - @ai-sdk/provider-utils@2.2.2

## 0.2.2

### Patch Changes

- a6b55cc: feat (providers/openai-compatible): add openai-compatible image model and use as xai image model base

## 0.2.1

### Patch Changes

- Updated dependencies [f10f0fa]
  - @ai-sdk/provider-utils@2.2.1

## 0.2.0

### Minor Changes

- 5bc638d: AI SDK 4.2

### Patch Changes

- Updated dependencies [5bc638d]
  - @ai-sdk/provider@1.1.0
  - @ai-sdk/provider-utils@2.2.0

## 0.1.17

### Patch Changes

- Updated dependencies [d0c4659]
  - @ai-sdk/provider-utils@2.1.15

## 0.1.16

### Patch Changes

- Updated dependencies [0bd5bc6]
  - @ai-sdk/provider@1.0.12
  - @ai-sdk/provider-utils@2.1.14

## 0.1.15

### Patch Changes

- Updated dependencies [2e1101a]
  - @ai-sdk/provider@1.0.11
  - @ai-sdk/provider-utils@2.1.13

## 0.1.14

### Patch Changes

- Updated dependencies [1531959]
  - @ai-sdk/provider-utils@2.1.12

## 0.1.13

### Patch Changes

- e1d3d42: feat (ai): expose raw response body in generateText and generateObject
- Updated dependencies [e1d3d42]
  - @ai-sdk/provider@1.0.10
  - @ai-sdk/provider-utils@2.1.11

## 0.1.12

### Patch Changes

- Updated dependencies [ddf9740]
  - @ai-sdk/provider@1.0.9
  - @ai-sdk/provider-utils@2.1.10

## 0.1.11

### Patch Changes

- Updated dependencies [2761f06]
  - @ai-sdk/provider@1.0.8
  - @ai-sdk/provider-utils@2.1.9

## 0.1.10

### Patch Changes

- Updated dependencies [2e898b4]
  - @ai-sdk/provider-utils@2.1.8

## 0.1.9

### Patch Changes

- Updated dependencies [3ff4ef8]
  - @ai-sdk/provider-utils@2.1.7

## 0.1.8

### Patch Changes

- Updated dependencies [d89c3b9]
  - @ai-sdk/provider@1.0.7
  - @ai-sdk/provider-utils@2.1.6

## 0.1.7

### Patch Changes

- f2c6c37: feat (provider/openai-compatible): support providerOptions in generateText/streamText

## 0.1.6

### Patch Changes

- Updated dependencies [3a602ca]
  - @ai-sdk/provider-utils@2.1.5

## 0.1.5

### Patch Changes

- Updated dependencies [066206e]
  - @ai-sdk/provider-utils@2.1.4

## 0.1.4

### Patch Changes

- Updated dependencies [39e5c1f]
  - @ai-sdk/provider-utils@2.1.3

## 0.1.3

### Patch Changes

- 361fd08: chore: update a few add'l processor references to extractor

## 0.1.2

### Patch Changes

- ed012d2: feat (provider): add metadata extraction mechanism to openai-compatible providers
- Updated dependencies [ed012d2]
- Updated dependencies [3a58a2e]
  - @ai-sdk/provider-utils@2.1.2
  - @ai-sdk/provider@1.0.6

## 0.1.1

### Patch Changes

- 0a699f1: feat: add reasoning token support
- Updated dependencies [e7a9ec9]
- Updated dependencies [0a699f1]
  - @ai-sdk/provider-utils@2.1.1
  - @ai-sdk/provider@1.0.5

## 0.1.0

### Minor Changes

- 62ba5ad: release: AI SDK 4.1

### Patch Changes

- Updated dependencies [62ba5ad]
  - @ai-sdk/provider-utils@2.1.0

## 0.0.18

### Patch Changes

- Updated dependencies [00114c5]
  - @ai-sdk/provider-utils@2.0.8

## 0.0.17

### Patch Changes

- ae57beb: feat (provider/openai-compatible): add support for optional custom URL parameters in requests.

## 0.0.16

### Patch Changes

- 7611964: feat (provider/xai): Support structured output for latest models.

## 0.0.15

### Patch Changes

- Updated dependencies [90fb95a]
- Updated dependencies [e6dfef4]
- Updated dependencies [6636db6]
  - @ai-sdk/provider-utils@2.0.7

## 0.0.14

### Patch Changes

- 43b37f7: feat (provider/openai-compatible): Add 'apiKey' option for concise direct use.
- Updated dependencies [19a2ce7]
- Updated dependencies [19a2ce7]
- Updated dependencies [6337688]
  - @ai-sdk/provider@1.0.4
  - @ai-sdk/provider-utils@2.0.6

## 0.0.13

### Patch Changes

- 6564812: feat (provider/openai-compatible): Add'l exports for customization.

## 0.0.12

### Patch Changes

- 70003b8: feat (provider/openai-compatible): Allow extending messages via metadata.

## 0.0.11

### Patch Changes

- 5ed5e45: chore (config): Use ts-library.json tsconfig for no-UI libs.
- 307c247: fix (provider/openai-compatible): Fix docs link to more info.
- Updated dependencies [5ed5e45]
  - @ai-sdk/provider-utils@2.0.5
  - @ai-sdk/provider@1.0.3

## 0.0.10

### Patch Changes

- baae8f4: feat (provider/deepinfra): Add DeepInfra provider.

## 0.0.9

### Patch Changes

- 9c7653b: feat (docs): Update OpenAI Compatible docs for new package.

## 0.0.8

### Patch Changes

- 6faab13: feat (provider/openai-compatible): simulated streaming setting

## 0.0.7

### Patch Changes

- ad2bf11: feat (provider/fireworks): Add Fireworks provider.

## 0.0.6

### Patch Changes

- Updated dependencies [09a9cab]
  - @ai-sdk/provider@1.0.2
  - @ai-sdk/provider-utils@2.0.4

## 0.0.5

### Patch Changes

- e958996: fix (provider/openai-compatible): remove unused index property from validation

## 0.0.4

### Patch Changes

- Updated dependencies [0984f0b]
  - @ai-sdk/provider-utils@2.0.3

## 0.0.3

### Patch Changes

- a9a19cb: fix (provider/openai,groq): prevent sending duplicate tool calls

## 0.0.2

### Patch Changes

- fc18132: feat (ai/core): experimental output for generateText

## 0.0.1

### Patch Changes

- 962978b: feat (packages/openai-compatible): Base for OpenAI-compatible providers.
