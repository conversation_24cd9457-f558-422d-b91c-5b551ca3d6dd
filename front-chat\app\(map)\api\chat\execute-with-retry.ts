import {
  streamText,
  createDataStreamResponse,
  generateObject,
  smoothStream,
  appendResponseMessages,
  UIMessage,
} from "ai";
import { evaluateAgentResult } from "./agents/evaluate";
import { agentConfigs, AgentName } from "./agents";
import { openai } from "@ai-sdk/openai";
import { saveMessages, updateMessage, getMessagesByChatId } from "@/lib/db/queries";
import { generateUUID } from "@/lib/utils";

interface ExecuteWithRetryOptions {
  model: any;
  agentName: AgentName;
  messages: any[];
  stateMessage: any;
  intentMessage: string;
  dataStream: any;
  session: any;
  enable_smart_navigation: boolean;
  isNonGeonProvider: boolean;
  iteration?: number;
  maxIterations?: number;
  onEvaluationComplete?: (evaluationResult: any) => void;
  chatId?: string; // AI 응답을 DB에 저장하기 위한 chatId 추가
  modelId?: string; // 사용된 모델 ID 추가
  enableReasoning?: boolean; // 사용자의 추론 활성화 여부 추가
}

/**
 * Agent 실행 및 평가 결과 반환
 */
export async function executeAgentWithRetry({
  model,
  agentName,
  messages,
  stateMessage,
  intentMessage,
  dataStream,
  session,
  enable_smart_navigation,
  isNonGeonProvider,
  iteration = 0, // 0부터 시작하여 재시도 시마다 증가
  maxIterations = 3,
  onEvaluationComplete,
  chatId,
  modelId,
  enableReasoning,
}: ExecuteWithRetryOptions) {
  console.log(`=== Agent 실행 (${iteration + 1}/${maxIterations}) ===`);

  const agentConfig = agentConfigs[agentName];
  // 평가를 위한 정보 수집 변수들
  let allToolCalls: any[] = [];
  let agentResponse = "";
  let evaluationResult: any = null;

  const result = streamText({
    model,
    messages: [
      {
        role: "system",
        content: agentConfig.system,
      },
      stateMessage,
      ...messages,
    ],
    temperature: 0,
    tools: agentConfig.tools,
    toolCallStreaming: true,
    maxSteps: agentConfig.maxSteps || 5,
    experimental_transform: smoothStream({ chunking: "word" }),
    experimental_continueSteps: true,
    experimental_repairToolCall: async ({
      toolCall,
      tools,
      parameterSchema,
      error,
    }) => {
      if (error.message.includes("No such tool")) {
        console.error(`[TOOL_REPAIR] Tool not found, cannot repair`);
        return null;
      }

      const tool = tools[toolCall.toolName as keyof typeof tools];
      const { object: repairedArgs } = await generateObject({
        model: openai("gpt-4.1-mini", { structuredOutputs: true }),
        schema: tool.parameters,
        prompt: [
          `The model tried to call the tool "${toolCall.toolName}" with the following arguments:`,
          JSON.stringify(toolCall.args),
          `The tool accepts the following schema:`,
          JSON.stringify(parameterSchema(toolCall)),
          "Please fix the arguments.",
        ].join("\n"),
      });

      return { ...toolCall, args: JSON.stringify(repairedArgs) };
    },
    ...(isNonGeonProvider
      ? {}
      : {
          providerOptions: {
            geon: {
              metadata: {
                chat_template_kwargs: { enable_thinking: false },
              },
            },
          },
        }),
    onStepFinish: ({ toolCalls, text, toolResults }) => {
      // 평가를 위한 정보 수집
      if (toolCalls && toolCalls.length > 0) {
        allToolCalls.push(...toolCalls);
      }

      // 도구 결과도 수집 (다음 시도에서 참조할 수 있도록)
      if (toolResults && toolResults.length > 0) {
        // toolResults를 allToolCalls에 매핑하여 저장
        toolResults.forEach((result: any, index) => {
          const toolCallIndex =
            allToolCalls.length - toolResults.length + index;
          if (allToolCalls[toolCallIndex]) {
            const toolCall = allToolCalls[toolCallIndex];
            const tool = agentConfig.tools[toolCall.toolName];

            // 원본 결과는 항상 result 필드에 저장 (response.messages에 원본이 담기도록)
            (toolCall as any).result = result.result;

            // toToolResultContent가 있는 도구는 프루닝된 결과를 prunedResult에 추가 저장
            if (
              tool &&
              "experimental_toToolResultContent" in tool &&
              tool.experimental_toToolResultContent
            ) {
              // 프루닝된 결과를 별도 필드에 저장 (평가자용)
              (toolCall as any).prunedResult =
                tool.experimental_toToolResultContent(result.result);
            }
          }
        });
      }

      // 도구 호출 정보를 어노테이션으로 전송
      if (toolCalls && toolCalls.length > 0) {
        toolCalls.forEach((toolCall) => {
          dataStream.writeMessageAnnotation({
            type: "tool_call",
            toolName: toolCall.toolName,
            args: toolCall.args,
            enableSmartNavigation: enable_smart_navigation,
          });
        });
      }
    },
    onFinish: async ({ text, toolCalls, response }) => {
      // 최종 정보 수집
      if (text) {
        agentResponse += text;
      }
      if (toolCalls && toolCalls.length > 0) {
        allToolCalls.push(...toolCalls);
      }

      // 평가자 실행 시작 알림
      dataStream.writeMessageAnnotation({
        type: "evaluation_start",
        iteration: iteration + 1,
        maxIterations,
        message: "작업 결과를 평가하고 있습니다...",
      });

      // 평가자 실행
      try {
        evaluationResult = await evaluateAgentResult(
          intentMessage,
          agentResponse,
          allToolCalls
        );

        // 평가 완료 어노테이션
        const evaluationMessage = evaluationResult.isCompleted
          ? "작업이 성공적으로 완료되었습니다"
          : "작업이 미완료되어 계속 진행합니다";

        dataStream.writeMessageAnnotation({
          type: "evaluation_completed",
          iteration: iteration + 1,
          maxIterations,
          isCompleted: evaluationResult.isCompleted,
          shouldContinue: !evaluationResult.isCompleted,
          message: evaluationMessage,
          reason: evaluationResult.reason,
          improvementSuggestions: evaluationResult.improvementSuggestions,
        });

        // 콜백으로 평가 결과와 도구 호출 정보 전달
        if (onEvaluationComplete) {
          onEvaluationComplete({
            ...evaluationResult,
            toolCalls: allToolCalls,
            agentResponse: agentResponse,
          });
        }
      } catch (error) {
        console.error("평가자 실행 실패:", error);
        evaluationResult = {
          isCompleted: false,
          reason: "평가자 실행 중 오류가 발생하여 미완료로 처리",
          improvementSuggestions: ["평가자 오류로 인한 재시도가 필요합니다"],
        };
      }

      // AI SDK 4 공식 패턴: response.messages를 사용하여 tool calls와 results 포함하여 저장
      if (
        session.user?.id &&
        chatId &&
        response.messages &&
        response.messages.length > 0
      ) {
        try {
          // tool-result의 result를 allToolCalls의 원본으로 교체하여 UI/DB에는 원본 결과가 저장되도록 함
          const messagesWithOriginalResults = response.messages.map((message: any) => {
            if (message.role === 'tool' && Array.isArray(message.content)) {
              const updatedContent = message.content.map((contentItem: any) => {
                if (contentItem.type === 'tool-result' && contentItem.toolCallId) {
                  const originalToolCall = allToolCalls.find(
                    (call: any) => call.toolCallId === contentItem.toolCallId
                  );
                  if (originalToolCall && originalToolCall.result) {
                    return {
                      ...contentItem,
                      result: originalToolCall.result,
                    };
                  }
                }
                return contentItem;
              });
              return { ...message, content: updatedContent };
            }
            return message;
          });

          // AI SDK의 appendResponseMessages를 사용하여 기존 메시지에 응답 메시지들을 추가
          const updatedMessages = appendResponseMessages({
            messages,
            responseMessages: messagesWithOriginalResults,
          });
          console.log("[updatedMessages]:", JSON.stringify(updatedMessages));
          // HIL 도구 완료 감지 및 처리
          const hilTools = ['chooseOption', 'getUserInput', 'confirmWithCheckbox', 'getLocation'];

          // HIL 도구가 포함된 경우 감지
          const hasHILTool = updatedMessages.some((msg: any) =>
            msg.role === 'assistant' && Array.isArray(msg.content) &&
            msg.content.some((content: any) =>
              content.type === 'tool-call' && hilTools.includes(content.toolName)
            )
          );

          // 최종 assistant 응답이 있는지 확인 (모든 도구 호출이 완료된 후)
          const hasFinalResponse = updatedMessages.some((msg: any) =>
            msg.role === 'assistant' && msg.id && typeof msg.content === 'string'
          );

          // HIL 도구가 포함되고 최종 응답이 있으면 HIL 완료로 간주
          const isHILComplete = hasHILTool && hasFinalResponse;

          console.log(`HIL 완료 감지: hasHILTool=${hasHILTool}, hasFinalResponse=${hasFinalResponse}, isHILComplete=${isHILComplete}`);

          if (isHILComplete) {
            // HIL 도구 완료 시 기존 메시지 업데이트 로직
            const finalMessage = updatedMessages.find((msg: any) =>
              msg.role === 'assistant' && msg.id && typeof msg.content === 'string' && msg.toolInvocations
            );

            if (finalMessage && finalMessage.toolInvocations) {
              // HIL 도구 찾기
              const hilToolCall = updatedMessages.find((msg: any) =>
                msg.role === 'assistant' && Array.isArray(msg.content) &&
                msg.content.some((content: any) =>
                  content.type === 'tool-call' && hilTools.includes(content.toolName)
                )
              );

              if (hilToolCall && Array.isArray(hilToolCall.content)) {
                const hilToolCallContent = hilToolCall.content.find((content: any) =>
                  content.type === 'tool-call' && hilTools.includes(content.toolName)
                );

                if (hilToolCallContent) {
                  // HIL 도구 결과 찾기
                  const hilToolResult = updatedMessages.find((msg: any) =>
                    msg.role === 'tool' && Array.isArray(msg.content) &&
                    msg.content.some((content: any) =>
                      content.type === 'tool-result' && content.toolCallId === hilToolCallContent.toolCallId
                    )
                  );

                  if (hilToolResult && Array.isArray(hilToolResult.content)) {
                    const hilToolResultContent = hilToolResult.content.find((content: any) =>
                      content.type === 'tool-result' && content.toolCallId === hilToolCallContent.toolCallId
                    );

                    if (hilToolResultContent) {
                      // HIL 도구를 toolInvocation 형태로 변환
                      const hilToolInvocation = {
                        state: "result",
                        step: 0,
                        args: hilToolCallContent.args,
                        toolCallId: hilToolCallContent.toolCallId,
                        toolName: hilToolCallContent.toolName,
                        result: hilToolResultContent.result
                      };

                      // 기존 toolInvocations의 step을 1씩 증가하고 HIL 도구를 맨 앞에 추가
                      const updatedToolInvocations = [
                        hilToolInvocation,
                        ...finalMessage.toolInvocations.map((inv: any) => ({
                          ...inv,
                          step: inv.step + 1
                        }))
                      ];

                      // parts 형태로 변환
                      const integratedParts = [
                        { type: "step-start" },
                        ...updatedToolInvocations.flatMap((invocation: any, index: number) => [
                          {
                            type: "tool-invocation",
                            toolInvocation: invocation
                          },
                          ...(index < updatedToolInvocations.length - 1 ? [{ type: "step-start" }] : [])
                        ]),
                        { type: "step-start" },
                        { type: "text", text: finalMessage.content }
                      ];

                      // 기존 HIL 메시지 찾아서 업데이트
                      try {
                        const existingMessages = await getMessagesByChatId({ id: chatId });
                        const existingHILMessage = existingMessages.find((msg: any) =>
                          msg.role === 'assistant' && Array.isArray(msg.parts) &&
                          msg.parts.some((part: any) =>
                            part.type === 'tool-invocation' &&
                            part.toolInvocation?.toolCallId === hilToolCallContent.toolCallId
                          )
                        );

                        if (existingHILMessage) {
                          // 기존 HIL 메시지 업데이트
                          await updateMessage({
                            id: existingHILMessage.id,
                            parts: integratedParts
                          });

                          // 기존 메시지 ID를 dataStream에 알림
                          dataStream.writeMessageAnnotation({
                            messageIdFromServer: existingHILMessage.id,
                          });

                          console.log(`HIL 도구 완료: 메시지 ${existingHILMessage.id} 업데이트됨 (HIL + ${finalMessage.toolInvocations.length}개 도구)`);
                        } else {
                          console.log("기존 HIL 메시지를 찾을 수 없습니다. 새 메시지로 저장합니다.");

                          // 폴백: 기존 메시지를 못 찾으면 새 메시지로 저장
                          const newMessageId = generateUUID();
                          await saveMessages({
                            messages: [{
                              id: newMessageId,
                              chatId,
                              role: "assistant",
                              content: "",
                              parts: integratedParts,
                              attachments: [],
                              createdAt: new Date(),
                              deletedAt: null,
                              enableReasoning: null,
                              modelId: null
                            }]
                          });

                          dataStream.writeMessageAnnotation({
                            messageIdFromServer: newMessageId,
                          });
                        }
                      } catch (error) {
                        console.error("HIL 메시지 업데이트 실패:", error);
                      }

                      console.log(`HIL 도구 통합 완료: ${hilToolCallContent.toolName} + ${finalMessage.toolInvocations.length}개 도구`);
                    }
                  }
                }
              }
            }
          } else {
            // 일반적인 새 메시지 저장 로직
            const newMessages = updatedMessages.slice(messages.length);

            const messagesToSave = newMessages.map((message) => {
              const messageId = generateUUID();

              if (message.role === "assistant") {
                dataStream.writeMessageAnnotation({
                  messageIdFromServer: messageId,
                });
              }

              // AI SDK 메시지 구조에 맞게 parts 처리
              let parts: any[] = [];
              if (message.parts && Array.isArray(message.parts)) {
                parts = message.parts;
              } else if (Array.isArray(message.content)) {
                parts = message.content;
              } else if (typeof message.content === "string") {
                parts = [{ type: "text", text: message.content }];
              }

              return {
                id: messageId,
                chatId,
                role: message.role,
                content: "", // AI SDK 권장: content는 빈 문자열
                parts, // 올바르게 처리된 parts 배열
                attachments: message.experimental_attachments || [], // AI SDK attachments
                createdAt: new Date(),
                deletedAt: null, // 새 메시지는 삭제되지 않은 상태
                enableReasoning: enableReasoning || null, // 사용자의 추론 활성화 여부 기록
                modelId: message.role === "assistant" ? (modelId || null) : null, // assistant 메시지에만 모델 ID 저장
              };
            });

            if (messagesToSave.length > 0) {
              await saveMessages({
                messages: messagesToSave,
              });
            }
          }
        } catch (error) {
          console.error("메시지 저장 실패:", error);
        }
      }

      // 작업 완료 메시지 전송
      if (session.user?.id) {
        try {
          dataStream.writeMessageAnnotation({
            type: "agent_completed",
            agent: agentName,
            message: evaluationResult?.isCompleted
              ? "작업이 완료되었습니다."
              : "작업이 진행 중입니다.",
            finalEvaluation: evaluationResult,
            iteration: iteration + 1,
            maxIterations,
          });
        } catch (error) {
          console.error("메시지 저장 실패:", error);
        }
      }
    },
  });

  return result;
}

/**
 * 평가 결과를 포함한 Agent 실행 래퍼
 */
export async function executeAgentWithEvaluation({
  model,
  agentName,
  messages,
  stateMessage,
  intentMessage,
  dataStream,
  session,
  enable_smart_navigation,
  isNonGeonProvider,
  iteration = 0,
  maxIterations = 3,
  chatId,
}: ExecuteWithRetryOptions): Promise<{
  streamResult: any;
  evaluationResult: any;
}> {
  let evaluationResult: any = null;

  // Agent 실행
  const result = await executeAgentWithRetry({
    model,
    agentName,
    messages,
    stateMessage,
    intentMessage,
    dataStream,
    session,
    enable_smart_navigation,
    isNonGeonProvider,
    iteration,
    maxIterations,
    chatId,
  });

  // 스트림을 데이터스트림에 병합하고 완료 대기
  await new Promise<void>((resolve) => {
    result.mergeIntoDataStream(dataStream, {
      sendReasoning: true,
    });
  });

  return {
    streamResult: result,
    evaluationResult: evaluationResult,
  };
}
