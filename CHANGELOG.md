# Changelog (2025-07-01 ~ 2025-08-11)

## New Features

### 밀도분석 의도 분석 규칙 강화 (2025-07-03)
- **커밋**: d6f9699fa5d88a1725cd106e7280fb384e2626e8
- **설명**: 밀도분석 기능에 "포인트 타입 레이어만 지원" 제약 조건을 명시적으로 추가
- **영향**: 사용자가 잘못된 레이어 타입으로 밀도분석을 시도하는 오류 감소, 더 정확한 안내 제공

### 레이어 타입 필터링 기능 추가 (2025-07-03)
- **커밋**: d6f9699fa5d88a1725cd106e7280fb384e2626e8
- **설명**: 레이어 검색 도구에 레이어 타입 코드 기반 필터링 기능 추가
- **영향**: 특정 타입의 레이어만 검색 가능, 분석 도구의 정확성 향상

### 위치 기반 카테고리 스타일링 우선순위 개선 (2025-07-25)
- **커밋**: c50db66b25e2260df8e2e879c52d946c994bec01
- **설명**: 카테고리 스타일링 시 위치 관련 속성(도시, 구, 주소 등)을 우선적으로 고려하도록 에이전트 워크플로 개선
- **영향**: "서울만 빨간색으로 표시" 같은 지역 조건부 시각화 요청의 정확도 향상

### 메시지 프루닝 메커니즘 도입 (2025-07-03)
- **커밋**: d6f9699fa5d88a1725cd106e7280fb384e2626e8
- **설명**: 휴먼 인더 루프 이후 도구 결과 없이 대화가 이어질 때 최근 메시지를 정리하는 프루닝 로직 추가
- **영향**: 대화 컨텍스트 효율성 개선, 비용 및 응답 속도 최적화

## Improvements

### 속성 테이블 조회 성능 개선 (2025-07-31)
- **커밋**: 970b76c73f0b4c5f2abf4f40bdbb3f6c48fff92f
- **설명**: 서버사이드 페이지네이션을 도입하여 대용량 레이어 속성 데이터 처리 성능 향상
- **영향**: 대용량 데이터셋의 초기 로딩 시간 단축, 사용자 경험 개선

### 레이어 구성 정보 전달 구조 개선 (2025-07-03)
- **커밋**: d6f9699fa5d88a1725cd106e7280fb384e2626e8
- **설명**: 채팅 라우트와 ChatMap 컴포넌트에서 레이어 구성(이름, geometryType) 정보를 AI 에이전트에 정확히 전달
- **영향**: AI 에이전트의 레이어 관련 의사결정 정확도 향상

### 사용자 안내 문구 개선 (2025-07-31)
- **커밋**: 970b76c73f0b4c5f2abf4f40bdbb3f6c48fff92f
- **설명**: annotations.tsx와 reasoning.tsx 컴포넌트의 사용자 안내 메시지 개선
- **영향**: 데이터 처리 중 사용자에게 더 명확한 피드백 제공

### Overview 예시 명령어 업데이트 (2025-07-03, 2025-07-25)
- **커밋**: d6f9699fa5d88a1725cd106e7280fb384e2626e8, c50db66b25e2260df8e2e879c52d946c994bec01
- **설명**: 밀도분석 및 위치 기반 스타일링 기능에 맞춰 Overview 컴포넌트의 예시 명령어 갱신
- **영향**: 신규 사용자의 기능 이해도 및 온보딩 경험 향상

## Bug Fixes

### 도구 규칙 및 우선순위 로직 수정 (2025-07-25)
- **커밋**: c50db66b25e2260df8e2e879c52d946c994bec01
- **설명**: generateCategoricalStyle 도구의 위치 속성 우선순위 처리 로직 수정
- **영향**: 지역 기반 조건부 스타일링에서 발생하던 오작동 해결

## Code Refactoring & Architecture

### ServerDataTable 컴포넌트 분리 (2025-07-31)
- **커밋**: 970b76c73f0b4c5f2abf4f40bdbb3f6c48fff92f
- **설명**: 속성 테이블 조회 기능을 독립적인 ServerDataTable 컴포넌트로 분리
- **영향**: 컴포넌트 재사용성 향상, 코드 유지보수성 개선

### 레이어 속성 API 구조 개선 (2025-07-31)
- **커밋**: 970b76c73f0b4c5f2abf4f40bdbb3f6c48fff92f
- **설명**: `/api/layer/detailed-info` 엔드포인트에 pageIndex, pageSize 파라미터 추가
- **영향**: 서버-클라이언트 간 데이터 전송 효율성 향상, 확장성 개선

### 에이전트 워크플로 구조 정비 (2025-07-25)
- **커밋**: c50db66b25e2260df8e2e879c52d946c994bec01
- **설명**: 레이어 도구 규칙과 에이전트 설정 파일의 구조 정리 및 일관성 개선
- **영향**: AI 에이전트 동작의 예측 가능성 및 안정성 향상

## Technical Notes

- **테스트 범위**: 이 기간 동안 명시적인 의존성 변경이나 패키지 업데이트는 확인되지 않음
- **호환성**: 모든 변경사항은 기존 API와 하위 호환성 유지
- **성능**: 서버사이드 페이지네이션 도입으로 대용량 데이터 처리 성능 크게 개선
- **사용자 경험**: 의도 분석 정확도 향상과 안내 메시지 개선으로 전반적인 UX 품질 상승

## Next Steps

이번 변경사항들을 바탕으로 다음 개선 작업이 권장됩니다:

1. **Annotation 진행 단계 고도화**: 4단계 시각화 (의도 분석 → 계획 → 실행 → 완료)
2. **레이어 관리 일원화**: useLayerManager 중심의 통합 레이어 상태 관리
3. **좌표계 처리 가드레일**: EPSG:5186 환경에서의 좌표 변환 안정성 강화
4. **심볼 생성 기능**: 포인트/라인/폴리곤별 시각적 스타일링 도구 확장
