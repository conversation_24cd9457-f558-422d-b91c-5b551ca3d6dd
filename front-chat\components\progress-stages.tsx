"use client";

import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  BrainCir<PERSON>it, 
  ClipboardList, 
  Zap, 
  CheckCircle2,
  Clock,
  ArrowRight
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";

export type ProgressStage = "analyzing" | "planning" | "executing" | "completed";

interface ProgressStagesProps {
  currentStage: ProgressStage;
  isLoading?: boolean;
  intentMessage?: string;
  toolCalls?: string[];
  className?: string;
}

interface StageConfig {
  id: ProgressStage;
  label: string;
  icon: React.ReactNode;
  description: string;
  color: {
    active: string;
    completed: string;
    pending: string;
  };
}

const stages: StageConfig[] = [
  {
    id: "analyzing",
    label: "의도 분석",
    icon: <BrainCircuit className="h-4 w-4" />,
    description: "사용자 요청을 분석하고 있습니다",
    color: {
      active: "bg-blue-500 text-white border-blue-500",
      completed: "bg-green-500 text-white border-green-500", 
      pending: "bg-gray-100 text-gray-500 border-gray-200"
    }
  },
  {
    id: "planning",
    label: "작업 계획",
    icon: <ClipboardList className="h-4 w-4" />,
    description: "최적의 작업 계획을 수립하고 있습니다",
    color: {
      active: "bg-purple-500 text-white border-purple-500",
      completed: "bg-green-500 text-white border-green-500",
      pending: "bg-gray-100 text-gray-500 border-gray-200"
    }
  },
  {
    id: "executing",
    label: "작업 실행",
    icon: <Zap className="h-4 w-4" />,
    description: "필요한 도구들을 실행하고 있습니다",
    color: {
      active: "bg-orange-500 text-white border-orange-500",
      completed: "bg-green-500 text-white border-green-500",
      pending: "bg-gray-100 text-gray-500 border-gray-200"
    }
  },
  {
    id: "completed",
    label: "완료",
    icon: <CheckCircle2 className="h-4 w-4" />,
    description: "작업이 성공적으로 완료되었습니다",
    color: {
      active: "bg-green-500 text-white border-green-500",
      completed: "bg-green-500 text-white border-green-500",
      pending: "bg-gray-100 text-gray-500 border-gray-200"
    }
  }
];

const getStageIndex = (stage: ProgressStage): number => {
  return stages.findIndex(s => s.id === stage);
};

const getStageStatus = (stageIndex: number, currentStageIndex: number, isCompleted: boolean): "active" | "completed" | "pending" => {
  if (isCompleted && stageIndex <= currentStageIndex) return "completed";
  if (stageIndex === currentStageIndex) return "active";
  if (stageIndex < currentStageIndex) return "completed";
  return "pending";
};

export const ProgressStages: React.FC<ProgressStagesProps> = ({
  currentStage,
  isLoading = false,
  intentMessage,
  toolCalls = [],
  className
}) => {
  const currentStageIndex = getStageIndex(currentStage);
  const isCompleted = currentStage === "completed";
  const progressPercentage = isCompleted ? 100 : ((currentStageIndex + 1) / stages.length) * 100;

  return (
    <div className={cn("w-full max-w-4xl mx-auto", className)}>
      {/* 전체 진행률 바 */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">진행 상황</span>
          <span className="text-sm text-gray-500">{Math.round(progressPercentage)}%</span>
        </div>
        <Progress value={progressPercentage} className="h-2" />
      </div>

      {/* 단계별 표시 */}
      <div className="space-y-4">
        {stages.map((stage, index) => {
          const status = getStageStatus(index, currentStageIndex, isCompleted);
          const isCurrentStage = index === currentStageIndex;
          
          return (
            <motion.div
              key={stage.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className="relative"
            >
              {/* 연결선 */}
              {index < stages.length - 1 && (
                <div className="absolute left-6 top-12 w-0.5 h-8 bg-gray-200" />
              )}
              
              <div className="flex items-start gap-4">
                {/* 아이콘 */}
                <div className={cn(
                  "flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-300",
                  stage.color[status]
                )}>
                  {status === "active" && isLoading ? (
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                    >
                      <Clock className="h-4 w-4" />
                    </motion.div>
                  ) : (
                    stage.icon
                  )}
                </div>

                {/* 내용 */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className={cn(
                      "font-medium transition-colors",
                      status === "active" ? "text-gray-900" : 
                      status === "completed" ? "text-green-700" : "text-gray-500"
                    )}>
                      {stage.label}
                    </h3>
                    
                    {status === "active" && isLoading && (
                      <Badge variant="outline" className="text-xs animate-pulse">
                        진행 중
                      </Badge>
                    )}
                    
                    {status === "completed" && (
                      <Badge variant="outline" className="text-xs border-green-500 text-green-700">
                        완료
                      </Badge>
                    )}
                  </div>

                  <p className={cn(
                    "text-sm transition-colors",
                    status === "active" ? "text-gray-700" : 
                    status === "completed" ? "text-gray-600" : "text-gray-400"
                  )}>
                    {stage.description}
                  </p>

                  {/* 단계별 추가 정보 */}
                  <AnimatePresence>
                    {isCurrentStage && stage.id === "planning" && intentMessage && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: "auto" }}
                        exit={{ opacity: 0, height: 0 }}
                        className="mt-3 p-3 bg-purple-50 rounded-lg border border-purple-200"
                      >
                        <p className="text-sm text-purple-800 leading-relaxed">
                          {intentMessage}
                        </p>
                      </motion.div>
                    )}

                    {isCurrentStage && stage.id === "executing" && toolCalls.length > 0 && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: "auto" }}
                        exit={{ opacity: 0, height: 0 }}
                        className="mt-3"
                      >
                        <div className="flex flex-wrap gap-2">
                          {toolCalls.map((toolName, toolIndex) => (
                            <motion.div
                              key={toolIndex}
                              initial={{ opacity: 0, scale: 0.8 }}
                              animate={{ opacity: 1, scale: 1 }}
                              transition={{ delay: toolIndex * 0.1 }}
                            >
                              <Badge 
                                variant="secondary" 
                                className="text-xs px-2 py-1 bg-orange-100 text-orange-800 border border-orange-200"
                              >
                                {toolName}
                              </Badge>
                            </motion.div>
                          ))}
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>

                {/* 다음 단계 화살표 */}
                {index < stages.length - 1 && status === "completed" && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="flex items-center text-gray-400"
                  >
                    <ArrowRight className="h-4 w-4" />
                  </motion.div>
                )}
              </div>
            </motion.div>
          );
        })}
      </div>
    </div>
  );
};
