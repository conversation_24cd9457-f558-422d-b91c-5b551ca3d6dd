"use client";

import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  BrainCircuit,
  Zap,
  CheckCircle2,
  Clock
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { AnimatedShinyText } from "./magicui/animated-shiny-text";

export type ProgressStage = "analyzing" | "executing" | "completed";

interface ProgressStagesProps {
  currentStage: ProgressStage;
  isLoading?: boolean;
  intentMessage?: string;
  toolCalls?: string[];
  className?: string;
}

interface StageConfig {
  id: ProgressStage;
  label: string;
  icon: React.ReactNode;
  description: string;
  color: {
    active: string;
    completed: string;
    pending: string;
  };
}

const stages: StageConfig[] = [
  {
    id: "analyzing",
    label: "분석 & 계획",
    icon: <BrainCircuit className="h-3 w-3" />,
    description: "요청을 분석하고 작업 계획을 수립합니다",
    color: {
      active: "bg-blue-500 text-white border-blue-500",
      completed: "bg-green-500 text-white border-green-500",
      pending: "bg-gray-100 text-gray-500 border-gray-200"
    }
  },
  {
    id: "executing",
    label: "실행",
    icon: <Zap className="h-3 w-3" />,
    description: "필요한 도구들을 실행합니다",
    color: {
      active: "bg-orange-500 text-white border-orange-500",
      completed: "bg-green-500 text-white border-green-500",
      pending: "bg-gray-100 text-gray-500 border-gray-200"
    }
  },
  {
    id: "completed",
    label: "완료",
    icon: <CheckCircle2 className="h-3 w-3" />,
    description: "작업이 성공적으로 완료되었습니다",
    color: {
      active: "bg-green-500 text-white border-green-500",
      completed: "bg-green-500 text-white border-green-500",
      pending: "bg-gray-100 text-gray-500 border-gray-200"
    }
  }
];

const getStageIndex = (stage: ProgressStage): number => {
  return stages.findIndex(s => s.id === stage);
};

const getStageStatus = (stageIndex: number, currentStageIndex: number, isCompleted: boolean): "active" | "completed" | "pending" => {
  if (isCompleted && stageIndex <= currentStageIndex) return "completed";
  if (stageIndex === currentStageIndex) return "active";
  if (stageIndex < currentStageIndex) return "completed";
  return "pending";
};

export const ProgressStages: React.FC<ProgressStagesProps> = ({
  currentStage,
  isLoading = false,
  intentMessage,
  toolCalls = [],
  className
}) => {
  const currentStageIndex = getStageIndex(currentStage);
  const isCompleted = currentStage === "completed";

  return (
    <div className={cn("w-full", className)}>
      {/* 가로형 단계 표시 */}
      <div className="flex items-center justify-between gap-1 mx-2">
        {stages.map((stage, index) => {
          const status = getStageStatus(index, currentStageIndex, isCompleted);
          const isLastStage = index === stages.length - 1;

          return (
            <React.Fragment key={stage.id}>
              <div className="flex flex-col items-center">
                {/* 아이콘 */}
                <div className={cn(
                  "flex items-center justify-center w-8 h-8 rounded-full border-2 transition-all duration-300 mb-2",
                  stage.color[status]
                )}>
                  {status === "active" && isLoading ? (
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                      className="flex items-center justify-center"
                    >
                      <Clock className="h-3 w-3" />
                    </motion.div>
                  ) : (
                    <div className="flex items-center justify-center">
                      {stage.icon}
                    </div>
                  )}
                </div>

                {/* 라벨 */}
                <span className={cn(
                  "text-xs font-medium text-center transition-colors",
                  status === "active" ? "text-gray-900" :
                    status === "completed" ? "text-green-700" : "text-gray-500"
                )}>
                  {stage.label}
                </span>
              </div>

              {/* 연결선 */}
              {!isLastStage && (
                <div className="flex-1 mx-2 mb-6">
                  <div className={cn(
                    "h-0.5 transition-colors duration-300",
                    status === "completed" ? "bg-green-500" : "bg-gray-200"
                  )} />
                </div>
              )}
            </React.Fragment>
          );
        })}
      </div>

      {/* 현재 단계 추가 정보 */}
      <div className="text-left mt-4">
        {/* 의도 분석 중/완료 메시지 - stage에 관계없이 표시 */}
        <AnimatePresence>
          {currentStage === "analyzing" && !intentMessage && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
            >
              <AnimatedShinyText className="inline-flex items-center text-sm justify-center px-2 py-1 transition ease-out">
                요청을 분석하고 작업 계획을 수립하고 있습니다...
              </AnimatedShinyText>
            </motion.div>
          )}

          {intentMessage && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
            >
              <div className="px-1 relative">
                <div className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-blue-500 to-purple-500 rounded-full"></div>
                <div className="pl-4">
                  <p className="text-sm text-gray-700 leading-relaxed font-medium">
                    {intentMessage}
                  </p>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* 단계별 추가 정보 */}
        <AnimatePresence mode="wait">
          {stages.map((stage, index) => {
            const isCurrentStage = index === currentStageIndex;
            if (!isCurrentStage) return null;

            return (
              <motion.div
                key={stage.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
              >

                {/* 실행 단계에서 도구 호출 표시 */}
                {stage.id === "executing" && toolCalls.length > 0 && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="mt-3"
                  >
                    <div className="flex flex-wrap justify-start gap-2">
                      {toolCalls.map((toolName, toolIndex) => (
                        <motion.div
                          key={toolIndex}
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ delay: toolIndex * 0.1 }}
                        >
                          <Badge
                            variant="secondary"
                            className="text-xs px-2 py-1 bg-orange-100 text-orange-800 border border-orange-200 flex items-center gap-1"
                          >
                            {toolName}
                          </Badge>
                        </motion.div>
                      ))}
                    </div>
                  </motion.div>
                )}
              </motion.div>
            );
          })}
        </AnimatePresence>
      </div>
    </div>
  );
};
