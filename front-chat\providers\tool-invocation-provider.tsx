"use client";

import React, {
  createContext,
  useContext,
  useEffect,
  useMemo,
  useReducer,
  useRef,
  useState,
  useCallback,
} from "react";
import type { Message, ToolInvocation } from "ai";
import {
  ManagedLayerProps,
  LayerAction,
  type LayerManagerContext as ILayerManagerContext,
  layerTransformers
} from "@/types/layer-manager";
import { UseMapReturn } from "@geon-map/odf";
import { WMSLayerStyle, createDefaultPointStyle, createDefaultLineStyle, createDefaultPolygonStyle } from "@/types/layer-style";
import { highlightPointOnMap } from "@/lib/map-utils";
import { toast } from "sonner";
import { useBasemap } from "./basemap-provider";



// toolName => array of invocation results
export type ToolResultMap = Record<string, ToolInvocation[]>;

// 단순한 스타일 속성을 WMS 스타일 구조로 변환하는 함수
const convertSimpleStyleToWMS = (
  simpleStyle: any,
  layerInfo?: { geometryType?: string; currentStyle?: any }
): WMSLayerStyle => {
  let baseStyle: WMSLayerStyle;

  // 기존 스타일이 있다면 그것을 베이스로 사용
  if (layerInfo?.currentStyle && layerInfo.currentStyle.rules) {
    baseStyle = { ...layerInfo.currentStyle };
  } else {
    // 지오메트리 타입에 따라 적절한 기본 스타일 선택
    const geometryType = layerInfo?.geometryType || 'point';
    switch (geometryType) {
      case 'polygon':
      case '3':
        baseStyle = createDefaultPolygonStyle();
        break;
      case 'line':
      case '2':
        baseStyle = createDefaultLineStyle();
        break;
      case 'point':
      case '1':
      default:
        baseStyle = createDefaultPointStyle();
        break;
    }
  }

  // 기존 스타일을 복사하여 수정
  const updatedStyle = JSON.parse(JSON.stringify(baseStyle));
  const rule = updatedStyle.rules[0];
  const symbolizer = rule.symbolizers[0] as any;

  // 단순 스타일 속성을 WMS 구조에 매핑
  if (simpleStyle.color) {
    symbolizer.color = simpleStyle.color;
  }
  if (simpleStyle.fillOpacity !== undefined) {
    symbolizer.fillOpacity = simpleStyle.fillOpacity;
  }
  if (simpleStyle.strokeColor) {
    if (symbolizer.kind === 'Fill') {
      symbolizer.outlineColor = simpleStyle.strokeColor;
    } else {
      symbolizer.strokeColor = simpleStyle.strokeColor;
    }
  }
  if (simpleStyle.strokeWidth !== undefined) {
    if (symbolizer.kind === 'Fill') {
      symbolizer.outlineWidth = simpleStyle.strokeWidth;
    } else {
      symbolizer.strokeWidth = simpleStyle.strokeWidth;
    }
  }
  if (simpleStyle.radius !== undefined && symbolizer.kind === 'Mark') {
    symbolizer.radius = simpleStyle.radius;
  }
  if (simpleStyle.width !== undefined && symbolizer.kind === 'Line') {
    symbolizer.width = simpleStyle.width;
  }
  if (simpleStyle.symbol && symbolizer.kind === 'Mark') {
    symbolizer.wellKnownName = simpleStyle.symbol;
  }

  return updatedStyle;
};

// 현재 위치 정보 타입
export interface LocationInfo {
  latitude: number;
  longitude: number;
  accuracy?: number;
  timestamp?: number;
  projectedCoord: [number, number];
}

// 출발지/목적지 정보 타입
export interface RoutePointInfo {
  address: {
    roadAddr: string;
    jibunAddr?: string;
    buildName?: string;
    buildLo: string;
    buildLa: string;
  };
  projectedCoord: [number, number];
  toolCallId: string;
}

// 위치 컨텍스트
interface LocationContextType {
  currentLocation: LocationInfo | null;
  setCurrentLocation: (location: LocationInfo | null) => void;
  originPoint: RoutePointInfo | null;
  setOriginPoint: (origin: RoutePointInfo | null) => void;
  destinationPoint: RoutePointInfo | null;
  setDestinationPoint: (destination: RoutePointInfo | null) => void;
}

const ToolContext = createContext<ToolResultMap>({});
const LayerManagerContext = createContext<ILayerManagerContext | null>(null);
const LocationContext = createContext<LocationContextType>({
  currentLocation: null,
  setCurrentLocation: () => { },
  originPoint: null,
  setOriginPoint: () => { },
  destinationPoint: null,
  setDestinationPoint: () => { },
});

// 레이어 상태 관리 리듀서
function layerReducer(state: ManagedLayerProps[], action: LayerAction): ManagedLayerProps[] {
  switch (action.type) {
    case 'ADD_LAYER':
      // 중복 방지: 같은 ID가 있으면 업데이트
      const existingIndex = state.findIndex(layer => layer.id === action.payload.id);
      if (existingIndex >= 0) {
        const newState = [...state];
        newState[existingIndex] = { ...newState[existingIndex], ...action.payload };
        return newState;
      }

      // 새 레이어는 가장 높은 zIndex를 가져야 함 (가장 위에 표시)
      const maxZIndex = state.length > 0 ? Math.max(...state.map(layer => layer.zIndex || 0)) : 0;
      const newLayer = {
        ...action.payload,
        zIndex: maxZIndex + 1
      };

      return [...state, newLayer];

    case 'UPDATE_LAYER':
      return state.map(layer =>
        layer.id === action.payload.id
          ? { ...layer, ...action.payload.updates }
          : layer
      );

    case 'REMOVE_LAYER':
      return state.filter(layer => layer.id !== action.payload);

    case 'TOGGLE_VISIBILITY':
      return state.map(layer =>
        layer.id === action.payload
          ? { ...layer, visible: !layer.visible, userModified: true }
          : layer
      );

    case 'UPDATE_FILTER':
      return state.map(layer =>
        layer.id === action.payload.id
          ? { ...layer, filter: action.payload.filter }
          : layer
      );

    case 'UPDATE_STYLE':
      return state.map(layer =>
        layer.id === action.payload.id
          ? { ...layer, style: action.payload.style }
          : layer
      );

    case 'UPDATE_Z_INDEX':
      return state.map(layer =>
        layer.id === action.payload.id
          ? { ...layer, zIndex: action.payload.zIndex }
          : layer
      );

    case 'REORDER_LAYERS':
      const { layerIds } = action.payload;

      // 기존 state를 복사하여 객체 참조를 최대한 유지
      return state.map(layer => {
        const layerIndex = layerIds.indexOf(layer.id);
        if (layerIndex >= 0) {
          // DnD 인덱스와 zIndex는 반대 관계
          // DnD에서 index 0 = 가장 아래 = 가장 낮은 zIndex
          // DnD에서 마지막 index = 가장 위 = 가장 높은 zIndex
          const newZIndex = layerIds.length - layerIndex;

          // zIndex만 변경되었을 때만 새 객체 생성
          if (layer.zIndex !== newZIndex) {
            return {
              ...layer,
              zIndex: newZIndex
            };
          }
          return layer; // zIndex가 같으면 기존 객체 그대로 반환
        }

        // 순서에 포함되지 않은 레이어는 zIndex 0으로 설정
        if (layer.zIndex !== 0) {
          return {
            ...layer,
            zIndex: 0
          };
        }
        return layer; // 변경사항이 없으면 기존 객체 그대로 반환
      });

    case 'SET_LAYERS':
      return action.payload;

    default:
      return state;
  }
}

export function ToolInvocationProvider({
  messages,
  children,
  enableSmartNavigation = true,
  mapState,
}: {
  messages: Message[];
  children: React.ReactNode;
  enableSmartNavigation?: boolean;
  mapState?: UseMapReturn;
}) {
  const [resultMap, setResultMap] = useState<ToolResultMap>({});
  const [layers, dispatch] = useReducer(layerReducer, []);
  const [currentLocation, setCurrentLocation] = useState<LocationInfo | null>(null);
  const [originPoint, setOriginPoint] = useState<RoutePointInfo | null>(null);
  const [destinationPoint, setDestinationPoint] = useState<RoutePointInfo | null>(null);
  const processedToolCallsRef = useRef<Set<string>>(new Set());
  const processedSmartNavigationRef = useRef<Set<string>>(new Set()); // 스마트 네비게이션 처리 추적
  const previousMessagesRef = useRef<Message[]>([]); // 이전 메시지 상태 추적

  // 배경지도 상태 관리를 위한 useBasemap 훅
  const { setCurrentBasemap } = useBasemap();

  // 지연된 배치 처리를 위한 상태
  const pendingAddressesRef = useRef<Array<{
    address: any;
    toolCallId: string;
  }>>([]);
  const addressBatchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const BATCH_DELAY = 1000; // 2초 지연

  // 배치 처리된 주소들을 지도에 표시하는 함수
  const processBatchedAddresses = useCallback(() => {
    if (!enableSmartNavigation || !mapState || pendingAddressesRef.current.length === 0) {
      return;
    }

    try {
      const addresses = pendingAddressesRef.current;

      if (addresses.length === 1) {
        // 단일 주소인 경우 개선된 함수 사용
        const { address } = addresses[0];
        const success = highlightPointOnMap(
          mapState,
          parseFloat(address.buildLo),
          parseFloat(address.buildLa),
          address.buildGeom || address.geom,
          13,
          {
            clearPrevious: true,
            useZIndexUp: true,
            fitPadding: 1000
          }
        );
        if (success) {
          toast.success(`${address.roadAddr}로 자동 이동했습니다`);
        }
      } else {
        // 여러 주소인 경우 모든 주소를 포함하는 영역으로 이동
        const coordinates = addresses.map(({ address }) => ({
          lng: parseFloat(address.buildLo),
          lat: parseFloat(address.buildLa),
          geometry: address.buildGeom || address.geom
        }));

        // 모든 포인트를 하이라이트 (개선된 함수 사용)
        coordinates.forEach((coord, index) => {
          highlightPointOnMap(
            mapState,
            coord.lng,
            coord.lat,
            coord.geometry,
            16,
            {
              clearPrevious: index === 0, // 첫 번째만 이전 하이라이트 제거
              useZIndexUp: true, // 하이라이트 레이어를 최상단으로
              fitPadding: 1000
            }
          );
        });

        toast.success(`${addresses.length}개 위치를 지도에 표시했습니다`);
      }

      // 처리 완료 후 초기화
      pendingAddressesRef.current = [];
    } catch (error) {
      console.error('배치 주소 처리 중 오류:', error);
      pendingAddressesRef.current = [];
    }
  }, [enableSmartNavigation, mapState]);

  // 스마트 지도 제어 함수들
  const handleSmartNavigation = useCallback((toolName: string, result: any, toolCallId: string) => {
    if (!enableSmartNavigation || !mapState) return;

    const smartNavKey = `${toolName}-${toolCallId}`;
    if (processedSmartNavigationRef.current.has(smartNavKey)) {
      return; // 이미 처리됨
    }

    try {
      switch (toolName) {
        case "searchAddress":
          if (result.result?.jusoList?.length > 0) {
            const firstAddress = result.result.jusoList[0];
            if (firstAddress.buildLo && firstAddress.buildLa) {
              // 주소를 배치 처리 큐에 추가
              pendingAddressesRef.current.push({
                address: firstAddress,
                toolCallId
              });

              // 기존 타이머 취소
              if (addressBatchTimeoutRef.current) {
                clearTimeout(addressBatchTimeoutRef.current);
              }

              // 새 타이머 설정
              addressBatchTimeoutRef.current = setTimeout(() => {
                processBatchedAddresses();
                addressBatchTimeoutRef.current = null;
              }, BATCH_DELAY);
            }
          }
          break;

        case "searchOrigin":
          if (result.result?.jusoList?.length > 0) {
            const firstAddress = result.result.jusoList[0];
            if (firstAddress.buildLo && firstAddress.buildLa && mapState?.map) {
              const projection = mapState.map.getProjection();
              const projectedCoord = projection.project([
                parseFloat(firstAddress.buildLo),
                parseFloat(firstAddress.buildLa)
              ], "4326");

              // 출발지 정보 설정
              setOriginPoint({
                address: firstAddress,
                projectedCoord: projectedCoord as [number, number],
                toolCallId
              });

            }
          }
          break;

        case "searchDestination":
          if (result.result?.jusoList?.length > 0) {
            const firstAddress = result.result.jusoList[0];
            if (firstAddress.buildLo && firstAddress.buildLa && mapState?.map) {
              const projection = mapState.map.getProjection();
              const projectedCoord = projection.project([
                parseFloat(firstAddress.buildLo),
                parseFloat(firstAddress.buildLa)
              ], "4326");

              // 목적지 정보 설정
              setDestinationPoint({
                address: firstAddress,
                projectedCoord: projectedCoord as [number, number],
                toolCallId
              });

            }
          }
          break;

        case "searchDirections":
          if (result.routes?.length > 0 && result.routes[0].result_code === 0) {
            // 새로운 타입에서는 레이어 변환기를 통해 자동으로 지도에 표시됨
            // 별도의 showRouteOnMap 호출 불필요
            toast.success("경로를 지도에 자동으로 표시했습니다");
          }
          break;

        case "getLocation":
          if (!mapState?.map) return;
          const prj = mapState.map.getProjection();
          if (result.latitude && result.longitude) {
            const projectedCoord = prj.project([result.longitude, result.latitude], "4326");

            // 위치 정보를 상태에 저장
            const locationInfo: LocationInfo = {
              latitude: result.latitude,
              longitude: result.longitude,
              accuracy: result.accuracy,
              timestamp: result.timestamp,
              projectedCoord: projectedCoord as [number, number]
            };
            setCurrentLocation(locationInfo);

            mapState.map.setZoom(14);
            mapState.map.setCenter(projectedCoord);
          }
          break;

        case "getLayer":
          // 서울 건물통합정보 레이어인 경우 서울 중심 좌표로 지도 이동 (EPSG:5186 표준 서울 중심좌표)
          if (result.lyrId === "LR0000004299" || result.name === "GIS건물통합정보_서울") {
            if (!mapState?.map) return;

            const center = new odf.Coordinate(955156.7761, 1951925.0984);
            const newZoom = 11;

            mapState.map.setCenter(center);
            mapState.map.setZoom(newZoom);
            toast.success(`${result.name || result.id} 레이어를 지도에 추가했습니다`);
          } else if (result.bbox && Array.isArray(result.bbox) && result.bbox.length >= 4) {
            const [minX, minY, maxX, maxY] = result.bbox;
            const centerX = (minX + maxX) / 2;
            const centerY = (minY + maxY) / 2;
            highlightPointOnMap(mapState, centerX, centerY, undefined, 12);
            toast.success(`${result.name || result.id} 레이어 영역으로 지도를 이동했습니다`);
          } else {
            toast.success(`${result.name || result.id} 레이어를 지도에 추가했습니다`);
          }
          break;

        case "changeBasemap":
          if (result.basemap && mapState?.view?.setBasemap) {
            // 1. ODF 맵에서 배경지도 변경
            mapState.view.setBasemap(result.basemap);
            // 2. 전역 상태 업데이트 (ODF 콜백에서 자동으로 처리되지만 보험용)
            setCurrentBasemap(result.basemap);
            toast.success(`배경지도가 변경되었습니다`);
          }
          break;

        case "setMapCenter":
          if (result.center && mapState?.view?.setCenter) {
            const [longitude, latitude] = result.center;
            mapState.view.setCenter([longitude, latitude]);
            toast.success(result.message || `지도 중심점이 이동되었습니다`);
          }
          break;

        case "setMapZoom":
          if (result.zoom !== undefined && mapState?.view?.setZoom) {
            if (result.zoomType === "relative") {
              // 상대적 확대/축소인 경우 현재 줌 레벨 기준으로 계산
              const currentZoom = mapState.view.getZoom();
              const zoomChange = result.zoomDirection === "in" ? result.zoom : -result.zoom;
              const newZoom = Math.max(1, Math.min(20, currentZoom + zoomChange));
              mapState.view.setZoom(newZoom);
            } else {
              // 절대적 확대/축소
              mapState.view.setZoom(result.zoom);
            }
            toast.success(result.message || `지도 확대/축소 레벨이 변경되었습니다`);
          }
          break;

        case "moveMapByDirection":
          if (result.deltaX !== undefined && result.deltaY !== undefined && mapState?.map?.setCenter && mapState?.map?.getCenter) {
            const currentCenter = mapState.map.getCenter();
            // EPSG:5186 좌표계에서는 X, Y 좌표를 직접 사용
            const newCenter: [number, number] = [
              currentCenter[0] + result.deltaX, // X축 (동서 방향)
              currentCenter[1] + result.deltaY  // Y축 (남북 방향)
            ];
            mapState.map.setCenter(newCenter);
            toast.success(result.message || `지도가 이동되었습니다`);
          }
          break;

        default:
          // 기타 도구들은 특별한 지도 제어가 필요하지 않음
          break;
      }

      processedSmartNavigationRef.current.add(smartNavKey);
    } catch (error) {
      console.error(`Error handling smart navigation for ${toolName}:`, error);
    }
  }, [enableSmartNavigation, mapState]);

  // Tool 결과를 파싱하여 resultMap 업데이트
  useEffect(() => {
    const next: ToolResultMap = {};
    messages.forEach((msg) => {
      msg.parts?.forEach((part) => {
        if (part.type !== "tool-invocation") return;
        const { toolName, state } = part.toolInvocation;
        if (state !== "result" || !('result' in part.toolInvocation)) return;
        (next[toolName] ??= []).push(part.toolInvocation as any);
      });
    });
    setResultMap(next);
  }, [messages]);

  // 메시지가 변경될 때 (이전 대화 로드 시) 처리 상태 초기화
  useEffect(() => {
    const previousMessages = previousMessagesRef.current;

    // 새로운 대화를 불러온 경우를 감지
    const isNewConversation =
      // 이전에 메시지가 없었고 현재 메시지가 있는 경우 (초기 로드)
      (previousMessages.length === 0 && messages.length > 0) ||
      // 첫 번째 메시지의 ID가 다른 경우 (다른 대화 로드)
      (previousMessages.length > 0 && messages.length > 0 &&
        previousMessages[0]?.id !== messages[0]?.id) ||
      // 메시지 수가 크게 줄어든 경우 (다른 대화 로드)
      (previousMessages.length > messages.length && messages.length > 0);

    if (isNewConversation) {
      // 이전 대화를 불러올 때 처리 상태를 초기화하여 스타일 등이 다시 적용되도록 함
      processedToolCallsRef.current.clear();
      processedSmartNavigationRef.current.clear();
      console.log('🔄 New conversation detected - clearing processed tool calls for style reapplication');
    }

    // 현재 메시지 상태를 저장
    previousMessagesRef.current = [...messages];
  }, [messages]);

  // Tool 결과를 기반으로 레이어 상태 업데이트
  useEffect(() => {
    const newLayers: ManagedLayerProps[] = [];

    Object.entries(resultMap).forEach(([toolName, invocations]) => {
      const transformer = layerTransformers[toolName];

      invocations.forEach((invocation) => {
        if (invocation.state === 'result' && 'result' in invocation) {
          // 이미 처리된 tool call인지 확인
          const toolCallKey = `${toolName}-${invocation.toolCallId}`;
          if (processedToolCallsRef.current.has(toolCallKey)) {
            return; // 이미 처리됨, 건너뛰기
          }

          try {
            if (toolName === 'createLayerFilter') {
              // 필터 업데이트는 기존 레이어에 적용
              const filterResult = invocation.result as any;
              if (filterResult.lyr_id && filterResult.filter) {
                dispatch({
                  type: 'UPDATE_FILTER',
                  payload: { id: filterResult.lyr_id, filter: filterResult.filter }
                });
                processedToolCallsRef.current.add(toolCallKey);
              }
            } else if (toolName === 'updateLayerStyle') {
              // 스타일 업데이트는 기존 레이어에 적용
              const styleResult = invocation.result as any;
              console.log('Style result:', styleResult);
              if (styleResult.layerId && styleResult.styleUpdate && styleResult.success) {
                // 현재 레이어 정보 가져오기
                const currentLayer = layers.find(layer => layer.id === styleResult.layerId);
                const layerInfo = currentLayer ? {
                  geometryType: (currentLayer as any).geometryType,
                  currentStyle: (currentLayer as any).style || (currentLayer as any).renderOptions?.style
                } : undefined;

                console.log('Current layer info:', layerInfo);

                // 단순한 스타일 속성을 WMS 스타일 구조로 변환
                const convertedStyle = convertSimpleStyleToWMS(styleResult.styleUpdate, layerInfo);
                console.log('Converted style:', convertedStyle);

                dispatch({
                  type: 'UPDATE_STYLE',
                  payload: { id: styleResult.layerId, style: convertedStyle }
                });
                processedToolCallsRef.current.add(toolCallKey);
              }
            } else if (toolName === 'generateCategoricalStyle') {
              // 유형별 스타일 생성 결과 처리
              const categoricalResult = invocation.result as any;
              console.log('Categorical style result:', categoricalResult);
              if (categoricalResult.layerId && categoricalResult.styleRules && categoricalResult.success) {
                // 현재 레이어 정보 가져오기
                const currentLayer = layers.find(layer => layer.id === categoricalResult.layerId);
                const geometryType = (currentLayer as any)?.geometryType || 'point';

                // 지오메트리 타입에 따른 심볼라이저 생성 함수
                const createSymbolizerForGeometry = (color: string) => {
                  if (geometryType === 'point' || geometryType === '1') {
                    return {
                      kind: 'Mark' as const,
                      wellKnownName: 'circle' as const,
                      radius: 6,
                      color: color,
                      fillOpacity: 1,
                      strokeColor: '#000000',
                      strokeWidth: 1,
                      strokeOpacity: 1
                    };
                  } else if (geometryType === 'line' || geometryType === '2') {
                    return {
                      kind: 'Line' as const,
                      color: color,
                      width: 2,
                      opacity: 1,
                      cap: 'round' as const,
                      join: 'round' as const
                    };
                  } else { // polygon
                    return {
                      kind: 'Fill' as const,
                      color: color,
                      fillOpacity: 0.7,
                      outlineColor: '#000000',
                      outlineWidth: 1
                    };
                  }
                };

                // 단일 조건 필터 생성 함수
                const createSingleFilter = (condition: string, value: string, attributeName: string) => {
                  switch (condition) {
                    case 'like':
                      return ['*=', attributeName, `*${value}*`];
                    case 'equal':
                      return ['==', attributeName, value];
                    case 'greater':
                      return ['>', attributeName, value];
                    case 'less':
                      return ['<', attributeName, value];
                    case 'greaterEqual':
                      return ['>=', attributeName, value];
                    case 'lessEqual':
                      return ['<=', attributeName, value];
                    case 'default':
                    default:
                      return null; // 기본 스타일은 필터 없음
                  }
                };

                // 복합 조건 필터 생성 함수
                const createComplexFilter = (conditions: any[], logicalOperator: string = 'AND') => {
                  if (!conditions || conditions.length === 0) {
                    return null; // 기본 스타일은 필터 없음
                  }

                  if (conditions.length === 1) {
                    const cond = conditions[0];
                    return createSingleFilter(cond.condition, cond.value, cond.attributeName);
                  }

                  // 두 개 이상의 조건 처리
                  const filterConditions = conditions.map(cond =>
                    createSingleFilter(cond.condition, cond.value, cond.attributeName)
                  ).filter(filter => filter !== null);

                  if (filterConditions.length === 0) {
                    return null;
                  }

                  if (filterConditions.length === 1) {
                    return filterConditions[0];
                  }

                  // 논리 연산자에 따라 필터 조합
                  const operator = logicalOperator === 'OR' ? '||' : '&&';
                  return [operator, ...filterConditions];
                };

                // 규칙 순서 조정: default 규칙을 첫 번째로, 구체적인 조건들을 나중에
                const sortedStyleRules = [...categoricalResult.styleRules].sort((a, b) => {
                  // default 조건을 첫 번째로 (conditions가 비어있거나 default 조건이 있는 경우)
                  const aIsDefault = !a.conditions || a.conditions.length === 0 ||
                    a.conditions.some((cond: any) => cond.condition === 'default');
                  const bIsDefault = !b.conditions || b.conditions.length === 0 ||
                    b.conditions.some((cond: any) => cond.condition === 'default');

                  if (aIsDefault && !bIsDefault) return -1;
                  if (bIsDefault && !aIsDefault) return 1;
                  // 나머지는 원래 순서 유지
                  return 0;
                });

                // 유형별 스타일 규칙들을 WMS SLD 형식으로 변환
                const sldRules = sortedStyleRules.map((rule: any, index: number) => {
                  // AI가 생성한 헥스 색상 그대로 사용
                  const hexColor = rule.color || '#808080';

                  const sldRule: any = {
                    name: rule.description || `Rule ${index + 1}`,
                    symbolizers: [createSymbolizerForGeometry(hexColor)]
                  };

                  // 복합 조건 필터 생성
                  const filter = createComplexFilter(rule.conditions, rule.logicalOperator);
                  if (filter) {
                    sldRule.filter = filter;
                  }

                  return sldRule;
                });

                const wmsStyle = {
                  rules: sldRules
                };

                console.log('Converted categorical style:', wmsStyle);

                dispatch({
                  type: 'UPDATE_STYLE',
                  payload: { id: categoricalResult.layerId, style: wmsStyle }
                });
                processedToolCallsRef.current.add(toolCallKey);
              }
            } else if (toolName === 'removeLayer') {
              // 레이어 삭제 처리
              const removeResult = invocation.result as any;
              console.log('Remove layer result:', removeResult);
              if (removeResult.layerId && removeResult.success) {
                dispatch({
                  type: 'REMOVE_LAYER',
                  payload: removeResult.layerId
                });
                processedToolCallsRef.current.add(toolCallKey);
              }
            } else if (transformer) {
              // transformer 사용 (mapState 전달)
              const layerProps = transformer(invocation.result, invocation.toolCallId, mapState);

              // 스마트 네비게이션이 비활성화된 경우 레이어를 숨김 상태로 생성
              if (!enableSmartNavigation) {
                layerProps.visible = false;
                console.log('🚫 Smart navigation disabled - hiding layer:', layerProps.id);
              } else {
                console.log('✅ Smart navigation enabled - showing layer:', layerProps.id);
              }

              newLayers.push(layerProps);
              processedToolCallsRef.current.add(toolCallKey);

              // 스마트 네비게이션 수행 (새로운 도구 결과에 대해서만 1회 실행)
              handleSmartNavigation(toolName, invocation.result, invocation.toolCallId);
            } else {
              // 레이어를 생성하지 않는 도구들에 대한 스마트 네비게이션 처리
              handleSmartNavigation(toolName, invocation.result, invocation.toolCallId);
              processedToolCallsRef.current.add(toolCallKey);
            }
          } catch (error) {
            console.error(`Error transforming ${toolName} result:`, error);
          }
        }
      });
    });

    // 새 레이어들 추가
    newLayers.forEach(layer => {
      dispatch({ type: 'ADD_LAYER', payload: layer });
    });
  }, [resultMap, enableSmartNavigation, mapState]);

  // 스마트 네비게이션 상태 변경 시 기존 레이어들의 가시성 업데이트
  useEffect(() => {
    layers.forEach(layer => {
      // 도구 결과로 생성된 레이어들만 제어 (toolCallId가 있는 레이어)
      // 단, 사용자가 수동으로 수정한 레이어는 제외
      if (layer.toolCallId && !layer.userModified) {
        const shouldBeVisible = enableSmartNavigation;
        if (layer.visible !== shouldBeVisible) {
          console.log(`🔄 Auto-updating layer visibility: ${layer.id} -> ${shouldBeVisible} (smart navigation)`);
          dispatch({
            type: 'UPDATE_LAYER',
            payload: { id: layer.id, updates: { visible: shouldBeVisible } }
          });
        }
      } else if (layer.userModified) {
        console.log(`⏭️ Skipping auto-update for user-modified layer: ${layer.id}`);
      }
    });
  }, [enableSmartNavigation, layers]);

  // 컴포넌트 언마운트 시 타이머 정리
  useEffect(() => {
    return () => {
      if (addressBatchTimeoutRef.current) {
        clearTimeout(addressBatchTimeoutRef.current);
        addressBatchTimeoutRef.current = null;
      }
      pendingAddressesRef.current = [];
    };
  }, []);

  // 레이어 관리 컨텍스트 값
  const layerManagerValue: ILayerManagerContext = useMemo(() => ({
    layers,
    addLayer: (layer: ManagedLayerProps) => dispatch({ type: 'ADD_LAYER', payload: layer }),
    updateLayer: (id: string, updates: Partial<ManagedLayerProps>) =>
      dispatch({ type: 'UPDATE_LAYER', payload: { id, updates } }),
    removeLayer: (id: string) => dispatch({ type: 'REMOVE_LAYER', payload: id }),
    toggleVisibility: (id: string) => dispatch({ type: 'TOGGLE_VISIBILITY', payload: id }),
    updateFilter: (id: string, filter: string) =>
      dispatch({ type: 'UPDATE_FILTER', payload: { id, filter } }),
    updateStyle: (id: string, style: any) =>
      dispatch({ type: 'UPDATE_STYLE', payload: { id, style } }),
    updateZIndex: (id: string, zIndex: number) =>
      dispatch({ type: 'UPDATE_Z_INDEX', payload: { id, zIndex } }),
    reorderLayers: (layerIds: string[]) =>
      dispatch({ type: 'REORDER_LAYERS', payload: { layerIds } }),
    getLayerById: (id: string) => layers.find(layer => layer.id === id),
  }), [layers]);

  // 위치 컨텍스트 값
  const locationValue: LocationContextType = useMemo(() => ({
    currentLocation,
    setCurrentLocation,
    originPoint,
    setOriginPoint,
    destinationPoint,
    setDestinationPoint,
  }), [currentLocation, originPoint, destinationPoint]);

  return (
    <ToolContext.Provider value={resultMap}>
      <LayerManagerContext.Provider value={layerManagerValue}>
        <LocationContext.Provider value={locationValue}>
          {children}
        </LocationContext.Provider>
      </LayerManagerContext.Provider>
    </ToolContext.Provider>
  );
}

export function useToolResults<T = unknown>(
  toolName: string,
  transform?: (raw: unknown) => T,
): T[] {
  const map = useContext(ToolContext);
  return useMemo(
    () =>
      (map[toolName] ?? [])
        .filter((ti): ti is Extract<ToolInvocation, { state: "result" }> =>
          ti.state === "result" && 'result' in ti
        )
        .map((ti) =>
          transform ? transform(ti.result) : (ti.result as T)
        ),
    [map, toolName, transform],
  );
}

// 레이어 관리자 훅
export function useLayerManager(): ILayerManagerContext {
  const context = useContext(LayerManagerContext);
  if (!context) {
    throw new Error('useLayerManager must be used within a ToolInvocationProvider');
  }
  return context;
}

// 특정 레이어 정보를 가져오는 훅
export function useLayerById(id: string): ManagedLayerProps | undefined {
  const { getLayerById } = useLayerManager();
  return getLayerById(id);
}

// 필터가 적용된 레이어들을 가져오는 훅
export function useFilteredLayers(): ManagedLayerProps[] {
  const { layers } = useLayerManager();
  return layers.filter(layer => layer.filter);
}

// 위치 정보를 사용하는 훅
export function useLocation(): LocationContextType {
  const context = useContext(LocationContext);
  if (!context) {
    throw new Error('useLocation must be used within a ToolInvocationProvider');
  }
  return context;
}

