"use client";


import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>rollArea } from "@/components/ui/scroll-area";
import { Eye, EyeOff, X, GripVertical, Layers, Palette } from "lucide-react";
import { useLayerManager } from "@/hooks/use-layer-configs";
import type { LayerProps } from "@geon-map/odf";
import { LayerStyleEditor } from "./layer-style-editor";
import { DragDropContext, Droppable, Draggable, DropResult } from "@hello-pangea/dnd";

interface TOCProps {
  map?: any; // ODF Map instance, replace 'any' with actual type if known
  layers: LayerProps[];
}

export function TOC({ map, layers }: TOCProps) {
  const layerManager = useLayerManager();

  // zIndex 기준으로 정렬 (높은 zIndex가 위에 = DnD에서 아래쪽 인덱스)
  // 즉, zIndex가 높을수록 DnD 리스트에서 아래쪽에 위치
  const sortedLayers = [...layers].sort((a, b) => {
    const aZIndex = (a as any).zIndex || 0;
    const bZIndex = (b as any).zIndex || 0;
    return bZIndex - aZIndex; // 높은 zIndex가 먼저 (DnD 리스트 상단)
  });

  const toggleLayerVisibility = (layerId: string) => {
    layerManager.toggleVisibility(layerId);
  };

  const removeLayer = (layerId: string) => {
    layerManager.removeLayer(layerId);
  };

  const updateLayerStyle = (layerId: string, style: any) => {
    // 최적화된 스타일 업데이트 사용
    layerManager.updateStyle(layerId, style);
  };

  // 드래그 앤 드롭 핸들러
  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) return;

    const sourceIndex = result.source.index;
    const destinationIndex = result.destination.index;

    if (sourceIndex === destinationIndex) return;

    // sortedLayers를 기준으로 순서 변경
    const newOrder = Array.from(sortedLayers);
    const [reorderedItem] = newOrder.splice(sourceIndex, 1);
    newOrder.splice(destinationIndex, 0, reorderedItem);

    // 새로운 순서의 레이어 ID 배열 생성
    const layerIds = newOrder.map(layer => layer.id).filter(Boolean) as string[];
    layerManager.reorderLayers(layerIds);
  };

  if (layers.length === 0) {
    return (
      <ScrollArea className="w-full rounded-md border p-4">
        <div className="flex flex-col items-center justify-center py-8 text-center">
          <Layers className="h-12 w-12 text-muted-foreground/50 mb-4" />
          <h3 className="font-medium mb-2">레이어가 없습니다</h3>
          <p className="text-sm text-muted-foreground mb-4">
            새로운 레이어를 추가하여 지도를 구성해보세요
          </p>
          {/* <Button variant="outline" size="sm">
            레이어 추가
          </Button> */}
        </div>
      </ScrollArea>
    );
  }

  return (
    <ScrollArea className="w-full rounded-md border p-4">
      <div className="space-y-4">
        {/* 스타일 예제 섹션 */}
        {/* <LayerStyleExamples /> */}

        <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable droppableId="layers">
            {(provided, snapshot) => (
              <div
                {...provided.droppableProps}
                ref={provided.innerRef}
                className={`space-y-2 transition-colors duration-200 ${
                  snapshot.isDraggingOver ? 'bg-blue-50/50 rounded-lg p-2' : ''
                }`}
              >
                {sortedLayers.map((layer, index) => {
                  // 안정적인 ID 생성 - layer.id가 없거나 변경될 수 있는 경우 대비
                  const stableId = layer.id || `layer-${index}`;
                  const layerZIndex = (layer as any).zIndex || 0;
                  return (
                    <Draggable
                      key={stableId}
                      draggableId={stableId}
                      index={index}
                      isDragDisabled={false}
                    >
                    {(provided, snapshot) => (
                      <div
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        className={`rounded-lg bg-card border transition-all duration-200 ${
                          snapshot.isDragging
                            ? 'shadow-lg scale-105 rotate-2 bg-white border-blue-300'
                            : 'shadow-sm hover:shadow-md'
                        } ${
                          !layer.visible ? 'opacity-60' : ''
                        }`}
                      >
                        <div className="p-4">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <div
                                {...provided.dragHandleProps}
                                className="cursor-grab active:cursor-grabbing p-1 rounded hover:bg-muted/50 transition-colors"
                              >
                                <GripVertical className="h-4 w-4 text-muted-foreground" />
                              </div>
                              <div className="flex flex-col">
                                <span className="font-medium text-sm">
                                  {(layer as any).name || layer.id || `Layer ${index + 1}`}
                                </span>
                                <span className="text-xs text-muted-foreground">
                                  {(layer as any).geometryType || 'unknown'} • {layer.visible ? '표시됨' : '숨김'} • z:{layerZIndex}
                                </span>
                              </div>
                            </div>

                            <div className="flex items-center space-x-1">
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={() => layer.id && toggleLayerVisibility(layer.id)}
                                title={layer.visible ? "레이어 숨기기" : "레이어 보이기"}
                              >
                                {layer.visible ? <Eye size={14} /> : <EyeOff size={14} />}
                              </Button>

                              <LayerStyleEditor
                                layer={layer}
                                onStyleChange={(style) => layer.id && updateLayerStyle(layer.id, style)}
                              />

                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 text-destructive hover:text-destructive hover:bg-destructive/10"
                                onClick={() => layer.id && removeLayer(layer.id)}
                                title="레이어 삭제"
                              >
                                <X size={14} />
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </Draggable>
                  );
                })}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      </div>
    </ScrollArea>
  );
}
