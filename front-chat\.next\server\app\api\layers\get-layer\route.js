const CHUNK_PUBLIC_PATH = "server/app/api/layers/get-layer/route.js";
const runtime = require("../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/1e20b_next_38fc406f._.js");
runtime.loadChunk("server/chunks/b80e2_zod-to-json-schema_dist_esm_10a64f92._.js");
runtime.loadChunk("server/chunks/cdf59_zod_lib_index_mjs_c127353b._.js");
runtime.loadChunk("server/chunks/f44e9_ai_dist_index_mjs_a8d725b2._.js");
runtime.loadChunk("server/chunks/b25a3_@ai-sdk_geon_dist_index_mjs_e305efe3._.js");
runtime.loadChunk("server/chunks/node_modules__pnpm_e635b9c7._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__a5fd1f08._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/layers/get-layer/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.3_@opentelemetry+_a8516a9e8193861921635ddb2ae04d26/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/app/api/layers/get-layer/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.3_@opentelemetry+_a8516a9e8193861921635ddb2ae04d26/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/app/api/layers/get-layer/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
