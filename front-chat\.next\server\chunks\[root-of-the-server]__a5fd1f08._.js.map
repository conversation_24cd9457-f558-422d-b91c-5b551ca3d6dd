{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/chatbot/front-chat/app/api/layers/get-layer/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\";\nimport { getLayer } from \"@geon-ai/tools\";\nimport { getApiConfig } from \"@/lib/api-config\";\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { lyrId } = await request.json();\n\n    if (!lyrId) {\n      return NextResponse.json(\n        { error: \"레이어 ID가 필요합니다\" },\n        { status: 400 }\n      );\n    }\n\n    // getLayer 도구 실행 (AI 대화와 동일한 파라미터 사용)\n    const result = await getLayer.execute({\n      userId: \"admin\",\n      insttCode: \"geonpaas\",\n      userSeCode: \"14\",\n      lyrId: lyrId\n    }, {\n      abortSignal: new AbortController().signal,\n      toolCallId: `api-layer-add-${lyrId}-${Date.now()}`,\n      messages: []\n    });\n\n    return NextResponse.json(result);\n  } catch (error) {\n    console.error(\"=== 레이어 조회 실패 ===\");\n    console.error(\"Error:\", error);\n    console.error(\"Stack:\", error instanceof Error ? error.stack : \"No stack\");\n\n    return NextResponse.json(\n      { error: \"레이어 조회에 실패했습니다\" },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEpC,IAAI,CAAC,OAAO;YACV,OAAO,yOAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAgB,GACzB;gBAAE,QAAQ;YAAI;QAElB;QAEA,sCAAsC;QACtC,MAAM,SAAS,MAAM,iPAAA,CAAA,WAAQ,CAAC,OAAO,CAAC;YACpC,QAAQ;YACR,WAAW;YACX,YAAY;YACZ,OAAO;QACT,GAAG;YACD,aAAa,IAAI,kBAAkB,MAAM;YACzC,YAAY,CAAC,cAAc,EAAE,MAAM,CAAC,EAAE,KAAK,GAAG,IAAI;YAClD,UAAU,EAAE;QACd;QAEA,OAAO,yOAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC;QACd,QAAQ,KAAK,CAAC,UAAU;QACxB,QAAQ,KAAK,CAAC,UAAU,iBAAiB,QAAQ,MAAM,KAAK,GAAG;QAE/D,OAAO,yOAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAiB,GAC1B;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}