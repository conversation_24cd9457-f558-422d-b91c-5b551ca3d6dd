import { useLayerManager as useLayerManagerProvider } from "@/providers/tool-invocation-provider";
import type { GeoJSONLayerProps, LayerProps } from "@geon-map/odf";
import type { ManagedLayerProps } from "@/types/layer-manager";

/**
 * @deprecated Use useLayerManager instead for centralized layer management
 * This hook is kept for backward compatibility
 */
export const useLayerConfigs = (): LayerProps[] => {
  const { layers } = useLayerManagerProvider();

  // ManagedLayerProps를 LayerProps로 안전하게 변환
  return layers.map((layer: ManagedLayerProps): LayerProps => {
    const { source, toolCallId, ...baseProps } = layer;

    // 레이어 타입에 따른 적절한 변환
    if (layer.type === "geojson") {
      return {
        id: baseProps.id,
        type: baseProps.type,
        data: baseProps.data, // GeoJSON FeatureCollection 직접 전달
        visible: baseProps.visible ?? true, // 명시적으로 visible 속성 전달
        service: baseProps.service,
        opacity: baseProps.opacity,
        zIndex: baseProps.zIndex,
        bbox: baseProps.bbox,
        autoFit: baseProps.autoFit,
        renderOptions: baseProps.style ? {
          style: baseProps.style
        } : undefined,
        // service: baseProps.service || "geojson",
        dataProjectionCode: baseProps.dataProjectionCode || "EPSG:5186",
        featureProjectionCode: baseProps.featureProjectionCode || "EPSG:5186",
      } as GeoJSONLayerProps;
    }

    // geoserver 레이어의 경우
    if (layer.type === "geoserver") {
      return {
        id: baseProps.id,
        type: "geoserver",
        server: baseProps.server || "",
        layer: baseProps.layer || "",
        service: baseProps.service || "wfs",
        info: baseProps.info || {
          lyrId: baseProps.id,
          lyrNm: baseProps.name || "Unknown Layer",
        },
        name: baseProps.name,
        visible: baseProps.visible ?? true, // 명시적으로 visible 속성 전달
        opacity: baseProps.opacity,
        zIndex: baseProps.zIndex,
        filter: baseProps.filter,
        bbox: baseProps.bbox,
        autoFit: baseProps.autoFit,
        method: baseProps.method,
        crtfckey: baseProps.crtfckey,
        projection: baseProps.projection,
        geometryType: baseProps.geometryType,
        serviceTy: baseProps.serviceTy,
        renderOptions: baseProps.style ? {
          style: baseProps.style
        } : baseProps.renderOptions,
      } as LayerProps;
    }

    // 기타 레이어 타입들 (api, kml, csv 등)
    return {
      ...baseProps,
      // 필수 속성들 보장
      type: baseProps.type,
      id: baseProps.id,
      visible: baseProps.visible ?? true, // 명시적으로 visible 속성 전달
      autoFit: baseProps.autoFit,
      fitDuration: 100
    } as LayerProps;
  });
};

/**
 * 새로운 중앙집중식 레이어 관리 훅
 * 레이어 생성, 필터링, 스타일링을 모두 처리
 * 스타일 상태 동기화와 리마운트 방지 기능 포함
 */
export const useLayerManager = () => {
  const baseManager = useLayerManagerProvider();

  // 스타일 업데이트 시 리마운트 방지를 위한 개선된 updateStyle 함수
  const updateStyleOptimized = (id: string, style: any) => {
    const layer = baseManager.getLayerById(id);
    if (!layer) return;

    // 현재 스타일과 비교하여 실제 변경이 있는 경우에만 업데이트
    const currentStyle = layer.style || layer.renderOptions?.style;
    const styleChanged = JSON.stringify(currentStyle) !== JSON.stringify(style);

    if (styleChanged) {
      // 스타일만 업데이트하고 다른 속성은 유지
      baseManager.updateLayer(id, {
        style,
        // renderOptions도 함께 업데이트하여 일관성 보장
        renderOptions: { style }
      });
    }
  };

  // 레이어 상태 정보를 포함한 확장된 관리 기능
  const getLayerWithState = (id: string) => {
    const layer = baseManager.getLayerById(id);
    if (!layer) return null;

    return {
      ...layer,
      // 현재 스타일 상태 정보
      currentStyle: layer.style || layer.renderOptions?.style,
      // 스타일 타입 정보
      styleType: layer.service === 'wms' ? 'wms' :
                 layer.service === 'wfs' ? 'wfs' : 'default',
      // 지오메트리 타입 정보
      geometryType: layer.geometryType || 'point',
      // 수정 가능 여부
      isStyleEditable: layer.service === 'wms' || layer.service === 'wfs',
    };
  };

  // 스타일 관련 레이어들만 필터링
  const getStyleableLayers = () => {
    return baseManager.layers.filter(layer =>
      layer.service === 'wms' || layer.service === 'wfs'
    );
  };

  return {
    ...baseManager,
    // 기존 updateStyle을 최적화된 버전으로 교체
    updateStyle: updateStyleOptimized,
    // 새로운 확장 기능들
    getLayerWithState,
    getStyleableLayers,
  };
};
